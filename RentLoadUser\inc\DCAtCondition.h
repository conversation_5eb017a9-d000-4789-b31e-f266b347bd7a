/***********************************************************************
* Module:  DCAtCondition.h
* Author:  gaos
* Modified: 2010年5月26日 14:22:32
* Purpose: Declaration of the class DCAtCondition
* Comment: 融合帐务条件
***********************************************************************/

#if !defined(_DCAtCondition_h)
#define _DCAtCondition_h

#include "DCDBManer.h"
#include "DCAtCompare.h"
#include "DCDataCenter.h"


//条件集ID定义
#define ONCELOAD_COND_INSET_ID      1  //一次费
#define MONACCTBUILD_COND_INSET_ID  2  //月帐帐单生成
#define RENT_EVENT_TYPE_COND_INSET_ID 3 //事件类型条件判断

#define ONE_CONDITION_ITEM_NUMS               30
const int ONE_CONDITONN_ITEM_SIZE       =        ONE_CONDITION_ITEM_NUMS*sizeof(STATCondItem);

//条件集支持左值类型
#define TYPE_LEFT_CODE_ALL    0  //支持所有
#define TYPE_LEFT_CODE_INSET  1  //支持输入集
#define TYPE_LEFT_CODE_QUERY  2  //支持查询

#define TYPE_LEFT_CODE_ALL    0  //支持所有
#define TYPE_LEFT_CODE_INSET  1  //支持输入集

#define CONDITION_SERVICE_TYPE_REALACCT  1  //实时账务
#define CONDITION_SERVICE_TYPE_MONACCT   2  //月账
#define CONDITION_SERVICE_TYPE_ONCEFEE   3  //一次费
#define CONDITION_SERVICE_TYPE_DCLDR     4  //入库


class DCAtCondition
{
public:
	int Init(int nInsetId,DCDBManer *dbm);
	void Clear();
	DCAtCondition();
	~DCAtCondition();
	
	bool LoadCondtion();
	
	bool JudgeCond(long lnCondID);

    inline bool GetConditionById(long lnCondID, STATCondItem* vo_condition)
    { return LoadCondItem(lnCondID, vo_condition ); }

	void  SetElementValue(StInstElementValue stInfo);
protected:
	bool LoadCondItem(long lnCondID, int nJudgeFlag, multimap<int,STATCondItem> &mmapTmp);
	
	bool LoadCondItem(long lnCondID, STATCondItem* vo_condition);

	//替换一些常量， 返回替换的次数
    int  ReplaceCode(char* pszStr);

	void  GetElementValue(char* szDateCode,char* szValue);

	
private:
	
	int ReplaceStr(char *v_sString,const char *vi_sOld,const char *vi_sNew);

private:
	DCAtCompare* m_pCompare;    //比较对象指针

	std::map<long,STATCondItem* > m_mapCondition;
	std::map<long,STATCondItem* >::iterator m_itr;             //防止每次都申请

	STATCondItem m_condition[ONE_CONDITION_ITEM_NUMS];                           //一个条件

	int m_nInsetId;

	bool      m_user_load;
	DCDBManer *m_dbm;
	StInstElementValue m_stInfo;
};

#endif
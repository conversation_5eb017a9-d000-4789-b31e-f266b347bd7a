#include "DCConf.h"

DCConf* DCConf::m_inst = NULL;

DCConf* DCConf::Instance()
{
    if(!m_inst)
    {
        m_inst = new DCConf();
    }
    return m_inst;
}

DCConf::DCConf()
{

}

DCConf::~DCConf()
{

}

int DCConf::Init(const STConfigPara &cfgpara)
{	
	m_nRefreshIntr = cfgpara.nGrayRefreshIntr;
	m_strSubscriber = cfgpara.sSubscriber;

	//灰度配置数据初始化
	char sfile[1024] = {0};
	const char *path = getenv("OCS_CONFIG");
	sprintf(sfile, "%s/RentLoad.sql.xml", path);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "DCConf::Init", "Get sql config file from path[%s/RentLoad.sql.xml]", path);

	m_dbm = new DCDBManer();
	int ret = m_dbm->Init(sfile, true);
	if(ret < 0)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","sqlPath=[%s] sql init failed\n", sfile);
		return -1;
	}
	ret = DCGrayscaleRoute::instance()->init(m_dbm,m_strSubscriber.c_str()); //刷新数据
	if (ret < 0)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","DCGrayscaleRoute init data failed,Subscriber=[%s]",m_strSubscriber.c_str());
		return -1;
	}

    return 0;
}


//灰度定时刷新线程
void DCConf::routine()
{
	int ret = 0;
	while(nExit)
	{
		if(ret < m_nRefreshIntr)
		{
			sleep(1);
			ret++;
			continue;
		}

		//灰度配置数据刷新
		if(NULL==m_dbm)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","m_dbm not init.");
			return ;
		}
		ret = DCGrayscaleRoute::instance()->init(m_dbm,m_strSubscriber.c_str()); //刷新数据
		if (ret < 0)
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","DCGrayscaleRoute update data failed,Subscriber=[%s]",m_strSubscriber.c_str());
			return ;
		}
		ret = 0;
	}
	return ;
}


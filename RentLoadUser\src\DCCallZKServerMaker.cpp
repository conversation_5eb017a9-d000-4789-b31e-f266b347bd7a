#include <time.h>
#include <utility>
#include "rentThreeUserInfoJson.h"
#include "DCABMMsgDef.h"
#include "DCParseXml.h"
#include "UHead.h"
#include <sys/types.h>
#include <stdio.h>
#include <unistd.h>
#include "DCCallZKServerMaker.h"
#include "DCTCompress.h"

using namespace std;
using namespace dcf_new;

//初始化函数
int DCCallZKServerMaker::init(bool bDcfServMode,DCPairList *pairList,DCUidCacheManager *uidCacheManager,STConfigPara cfgPara)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","DCCallZKServerMaker::init Begin!!!");
    m_bDcfServMode = bDcfServMode;
	m_strZKAddr = cfgPara.strZKAddr;
	m_strZKServName = cfgPara.strZKServName;
	m_strZKRootDir = cfgPara.strZKRootDir;
	m_strZKLoginPwd = cfgPara.strZKLoginPwd;
	m_strZKAesPwd = cfgPara.strZKAesPwd;
	m_nBigAcctMsgSize = cfgPara.nBigAcctMsgSize;
	m_nDcaCacheMsgSize = cfgPara.nDcaCacheMsgSize;
	m_nOpenKpi = cfgPara.nOpenKpi;
	m_sRouteProcess = cfgPara.sRouteProcess;
	
	m_Compress = new Compressor();
	memset(m_szLogPath,0x00,sizeof(m_szLogPath));
	sprintf(m_szLogPath,"%s/RentLoad_output_message.log",cfgPara.strZKDcfLogDir.c_str());
		
	m_strHashKey = cfgPara.strHashKey;

	m_nSendType = cfgPara.nFlowControl;
	m_nSendType = (m_nSendType==0)?0:1;
	
	m_nFlowSleepUs = cfgPara.nFlowSleepUs;
	m_nFlowSleepUs = (m_nFlowSleepUs<=0)?100:m_nFlowSleepUs;
	
	m_pClient = new DCFLocalClient();
	m_pClient->setAESPassword(m_strZKAesPwd);
	m_pClient->setSerivcePassword(m_strZKLoginPwd);
	//m_pClient->setInitWait(cfgPara.ZKDCFSetWait);
	//m_pClient->setSerivceWorkNum(m_strZKServName, cfgPara.ZKDCFSetWorkNum);
	//m_pClient->setInitWaitTimeoutMills(cfgPara.ZKDCFWaitTimeOut);

	if (1 == m_nSendType)//流量控制
	{
		m_nQueueSizeOldUser = cfgPara.nQueueSizeOldUser;
		m_nQueueSizeOldUser = (m_nQueueSizeOldUser<=0)?512:m_nQueueSizeOldUser;
		
		m_nQueueSizeNewUser = cfgPara.nQueueSizeNewUser;
		m_nQueueSizeNewUser = (m_nQueueSizeNewUser<=0)?1024:m_nQueueSizeNewUser;
		m_nQueueSize = m_nQueueSizeNewUser;

		m_nQueueSizeFroUser = cfgPara.nQueueSizeFrozenUser;
		m_nQueueSizeFroUser = (m_nQueueSizeFroUser<=0)?1024:m_nQueueSizeFroUser;
		
		m_nFlowTimeOut = cfgPara.nFlowTimeOut;
		m_nFlowTimeOut = (m_nFlowTimeOut<=0)?100:m_nFlowTimeOut;

	}
	m_pairList = pairList;
	
	//启动消息回调线程 m_pClient
	m_pSendCallback = new SendCallbackAsyncHandler(m_pairList);
	m_pClient->addCallbackAsyncHandler(m_pSendCallback);
    m_pClient->setClientCallbackThreadsMax(1);//默认是3
	
	//启动消息返回后删除DCA缓存线程 m_pairList
	m_uidCacheManager = uidCacheManager;
	
	m_pClient->setZookAddrAndRootDir(m_strZKAddr,m_strZKRootDir);

	if (m_pClient->start())
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCCallZKServerMaker::init","start client failed{zook:%s}",m_strZKAddr.c_str());
		return -1;
	}
	if(m_pClient->subscribeService(m_strZKServName))
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCCallZKServerMaker::init","Access Services Failed{zook:%s}",m_strZKAddr.c_str());
		return -1;
	}

	if (m_ptrBPMon)
	{
		m_ptrBPMon = DCKpiSender::instance()->GetKPIHandle("BILLING", "REC", "RENDLOAD_B");//对应于大屏后台程序的处理方式
	}	
	
	DCBIZLOG(DCLOG_LEVEL_INFO, 0, "","DCCallZKServerMaker::init success");
	DCDATLOG("RE00001:");

	return 0;
}

int DCCallZKServerMaker::init(STConfigPara cfgPara)
{
	m_pairList = new DCPairList();

    //启动消息超时处理线程 m_pairList nTimeOut
	m_pairManager = new DCPairManager();
	m_pairManager->init(m_pairList,(cfgPara.nFlowTimeOut)*100);//超时时间 ,FlowTimeOut的单位为100ms
	m_pairManager->start();
	
	//启动消息返回后删除DCA缓存线程 m_pairList
	m_uidCacheManager = new DCUidCacheManager();
	m_uidCacheManager->init(m_pairList);
	m_uidCacheManager->start();
	
	int nRet = init(false,m_pairList,m_uidCacheManager,cfgPara);
	if (nRet < 0)
	{
		return nRet;
	}

	return 0;
}


DCCallZKServerMaker::~DCCallZKServerMaker()
{
	SAFE_DELETE_PTR(m_pSendCallback);	
	if(m_pClient!=NULL)
	{
		m_pClient->shutdown();
		SAFE_DELETE_PTR(m_pClient);
	}
    if(!m_bDcfServMode)
    {
        m_pairManager->exit();
    	SAFE_DELETE_PTR(m_pairManager);
        m_uidCacheManager->exit();
    	SAFE_DELETE_PTR(m_uidCacheManager);
        SAFE_DELETE_PTR(m_pairList);
    }
	pthread_mutex_destroy(&thread_mutex);
}

//获取uid
const char* DCCallZKServerMaker::GenUnifiledSeq()
{
	pthread_mutex_lock(&thread_mutex);

    //static int m_hseq = getpid()%1000000*100 + 99;
    long m_hseq = pthread_self()*100;
    static long m_tseq = time(NULL);
    static char m_sseq[36] = {0};
    m_tseq++;
    if(m_tseq >= 10000000000000000L)
	{
		m_tseq = 1;
	}
    sprintf(m_sseq, "R%016ld%016ld", m_hseq, m_tseq);//R140101591549696000000001541494385

	pthread_mutex_unlock(&thread_mutex);	
    return m_sseq;
}


//pInstUserInfo:消息结构体，nMsgCtrl：操作类型
//nMsgCtrl=0为正常发送消息
//nMsgCtrl=1为发送超时消息
//nMsgCtrl=2为定时流程
//nMsgCtrl=3为新装流程开始
int DCCallZKServerMaker::process(RentUserInfo* pInstUserInfo, int  nMsgCtrl, DCDBManer *p_dbm)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","DCCallZKServerMaker::process Begin,nMsgCtrl[%d],user_mode[%d],nMsgType[%d],BillingFlag[%d]",nMsgCtrl,pInstUserInfo->stAcctHead.user_mode,pInstUserInfo->nMsgType,pInstUserInfo->stAcctHead.BillingFlag);

	int nRet = 0;
    ocs::UHead uhd; 
    char buf[30] = {0}, extbuf[60] = {0};
	string strextbuf;
    struct timeval tmv;
	int nMessageNum = 0;
	bool bNeedSerialProcess = false; //是否需要串行处理
	int nBillingFlagOri = pInstUserInfo->stAcctHead.BillingFlag;

	if (MSGTYPE_IMMED == pInstUserInfo->nMsgType)
	{	
	    uhd.ext.insert(make_pair<string,string>("immed_flag","1"));
	}

	char group[50] = {0};
	if(nMsgCtrl!=MSGCTRL_MODE_NORMAL)
	{
		if(nMsgCtrl==MSGCTRL_MODE_LOADOLDBEG)//每次老用户处理前先清理队列
		{
			m_pairList->SetClear();
			m_nQueueSize = m_nQueueSizeOldUser;
            m_uidCacheManager->SetLogBegin(true,0);
		}
		else if(nMsgCtrl==MSGCTRL_MODE_LOADNEWBEG)//新装流程开始
		{
			m_nQueueSize = m_nQueueSizeNewUser;
		}
		else if(nMsgCtrl==MSGCTRL_MODE_LOADFROBEG)//复机流程开始
		{
			m_nQueueSize = m_nQueueSizeFroUser;
		}
		else if(nMsgCtrl==MSGCTRL_MODE_LOADOLDEND)//定时流程结束，writerentlog
		{
			m_uidCacheManager->SetLogBegin(false,pInstUserInfo->nGroupNum);
		}
		else
		{
			vector<SMsgPair> vecTimeOutList;
			m_pairList->GetTimeOutList(vecTimeOutList);
			
			DCBIZLOG(DCLOG_LEVEL_INFO,0,"DCCallZKServerMaker::process","Send timeout messaage begin.vecTimeOutList.size()=%d",vecTimeOutList.size());
			DCDATLOG("RE00002:%d",vecTimeOutList.size());
			
			for(int i=0;i<vecTimeOutList.size();i++)
			{
				if(1==m_nSendType)//流量控制
				{
					while(1)
					{
						if(m_pairList->size()<m_nQueueSize)//队列大小目前固定1024
						{
							gettimeofday(&tmv, NULL);
							vecTimeOutList[i].begin = tmv;
							vecTimeOutList[i].nReSend += 1;
							m_pairList->update(vecTimeOutList[i].uuid,vecTimeOutList[i]);
							break;
						}
						sleep(2);
					}
				}
				memset(extbuf,0x00,sizeof(extbuf));
				sprintf(extbuf,"{\"%s\":\"%ld\"}",m_strHashKey.c_str(),vecTimeOutList[i].lnSpecialId);
				strextbuf = extbuf;
				//服务化调用
				memset(group,0x0,sizeof(group));	
				if (m_nOpenKpi)
				{					
					DCKpiSender::instance()->GetFullGroup("TraceKpi", vecTimeOutList[i].nLatnId, group);//获取指标输出组标识符
					DCKpiSender::instance()->cycle_inc(m_ptrBPMon, group, "k1");
				}
				//string ZKServName = m_strZKServName;
				//GetGrayServName(head.latn_id,lnRouteAcctId,ZKServName);
				nRet = m_pClient->invokeAsync(vecTimeOutList[i].servName, vecTimeOutList[i].uuid, vecTimeOutList[i].sendMsg,"",0,strextbuf);//参数4为ZKAddr，为空自动获取，参数5为是否应答，=0为需要应答
				if(nRet < 0)
				{					
				    if (m_nOpenKpi)
				    {						
						DCKpiSender::instance()->cycle_inc(m_ptrBPMon, group, "k2");
					}
					DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCCallZKServerMaker::process","Send timeout messaage, ZKServName[%s] call invokeAsync failed: uid[%s] acctid[%ld] msgsize[%ld] ret[%d] ", vecTimeOutList[i].servName.c_str(),(vecTimeOutList[i].uuid).c_str(),vecTimeOutList[i].lnSpecialId,vecTimeOutList[i].sendMsg.size(),nRet);
					return -1;
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_INFO,0,"DCCallZKServerMaker::process","Send timeout messaage, ZKServName[%s] call invokeAsync sucess: uid[%s] acctid[%ld] msgsize[%ld] ", vecTimeOutList[i].servName.c_str(),(vecTimeOutList[i].uuid).c_str(),vecTimeOutList[i].lnSpecialId,vecTimeOutList[i].sendMsg.size());
					DCDATLOG("RE00003:%s!%s!%ld!%ld", vecTimeOutList[i].servName.c_str(),(vecTimeOutList[i].uuid).c_str(),vecTimeOutList[i].lnSpecialId,vecTimeOutList[i].sendMsg.size());
				}
				
				usleep(m_nFlowSleepUs);
				
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCCallZKServerMaker::process","Send timeout messaage end.");
		}
		return 0;
	}
	
	if (pInstUserInfo != NULL)
	{				
	    uhd.uid = GenUnifiledSeq();
		uhd.car = "8";
		if(DCLOG_GETLEVEL(DCLOG_CLASS_BIZ) >= DCLOG_LEVEL_DEBUG)
		{
			//打印结构体信息
			pt.clear();
            pt.print(uhd);
			pt.print(pInstUserInfo->stFmtHead);
			pt.print(pInstUserInfo->stAcctHead);
			pt.print(pInstUserInfo->stAcctBodyReq);
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCCallZKServerMaker::process","Send IRentUserInfo detial [RentUserInfo size=%ld] [VOfrInst size=%ld] [VDetailInst size=%ld]",pt.size(),(pInstUserInfo->stAcctBodyReq).VOfrInst.size(),(pInstUserInfo->stAcctBodyReq).VDetailInst.size());
		
			//临时输出文件用于测试
			FILE* pFile = fopen(m_szLogPath,"w");
			if (NULL != pFile)
			{
				fprintf(pFile,"%s\n",pt.data());
				fclose(pFile);
				pFile = NULL;
			}
		}
		
		//编码结构体信息
		en.clear();
		enBody.clear();
        en.encode(uhd);
		en.encode(pInstUserInfo->stFmtHead);
		en.encode(pInstUserInfo->stAcctHead);
		en.encode(pInstUserInfo->stAcctBodyReq);
		enBody.encode(pInstUserInfo->stAcctBodyReq);
		string sEn = HexEncode(en.data(),en.size());
		string sEnBody = HexEncode(enBody.data(),enBody.size());

		//消息超过64K需要压缩，压缩后前加10位数表长度，再加$$$$$$用于区分是否压缩
		//20180705 将64K 修改成 10K 减少框架的压力和CPU消耗
		if(sEn.size()>(10*1024))
		{		
		    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","msg size[%d] > 10*1024,need Compress",sEn.size());
			int lnCmpSize = m_Compress->Compress(sEn.c_str(),sEn.size());
			char *szCmpMsg = m_Compress->GetCompress();

			string strbuf="";
			strbuf.resize(lnCmpSize+1,0);
			memcpy((void*)strbuf.c_str(),(void*)szCmpMsg,lnCmpSize);
			//string strbuf(szCmpMsg,0,lnCmpSize+1);

			//编码结构体信息
			en.clear();
	        en.encode(strbuf);
			sEn.clear();
			sEn = HexEncode(en.data(),en.size());
			
			//开关设置超过多少字节写入dca 
			if(sEn.size() > m_nBigAcctMsgSize)
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCCallZKServerMaker::process","The messaage size[%d] > %d, Insert  messaage to DCA", sEn.size(), m_nBigAcctMsgSize);
				DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCCallZKServerMaker::process","Accord to Acct_id[%ld], Messagesource[%d], BatchNo[%ld] insert DCA",pInstUserInfo->stAcctHead.acct_id, pInstUserInfo->stAcctHead.messagesource, pInstUserInfo->stAcctHead.BatchNo);
				//DCA VALUE以$符号为起始，数据插入会失败
				//memset(buf,0x00,sizeof(buf));
				//sprintf(buf, "$$$$$$%010ld", lnCmpSize);
        		//sEn.insert(0, buf,16);
				int lnCmpSizeBody = m_Compress->Compress(sEnBody.c_str(),sEnBody.size());
				char *szCmpMsgBody = m_Compress->GetCompress();
				strbuf.resize(lnCmpSizeBody+1,0);
				memcpy((void*)strbuf.c_str(),(void*)szCmpMsgBody,lnCmpSizeBody);
				enBody.clear();
				enBody.encode(strbuf);
				sEnBody.clear();
				sEnBody = HexEncode(enBody.data(),enBody.size());

				//char szbuftmp[262145]={0};
				char *szbuftmp;
				szbuftmp = (char *)malloc(sizeof(char)*(m_nDcaCacheMsgSize+1));
				int lnCmpSizetmp = sEnBody.size();
				
				char sqlname[64] = {0};
				sprintf(sqlname,"InsertBigAcctMsg");
				string SQL;
				UDBSQL *pInsertDca = p_dbm->GetSQL(sqlname);
				if(NULL == pInsertDca)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "not find sql %s", sqlname);
					free(szbuftmp);
					return -1;
				}

				//截断写入dca，减少压力，每256K(256*1024=262144)写一次//改成配置m_nDcaCacheMsgSize
				int nValueCounts = lnCmpSizetmp / m_nDcaCacheMsgSize;
				int nValueMinLen = lnCmpSizetmp % m_nDcaCacheMsgSize;
				if(nValueMinLen != 0)
					nValueCounts = nValueCounts + 1;
				for(int idx=0;idx<nValueCounts;idx++)
				{
					memset(szbuftmp,0x00,sizeof(char)*(m_nDcaCacheMsgSize+1));
					if(idx == nValueCounts-1 && nValueMinLen != 0)
						lnCmpSize = nValueMinLen;
					else
						lnCmpSize = m_nDcaCacheMsgSize;
					sEnBody.copy(szbuftmp,lnCmpSize,idx*m_nDcaCacheMsgSize);
					//memcpy(szbuftmp,(void*)sEnBody.c_str()+idx*262144,lnCmpSize);
					
					DCBIZLOG(DCLOG_LEVEL_INFO,0,"DCCallZKServerMaker::process","Get insert DCA AcctBodyReq[acct_id=%ld,uid=%s,idx=%d] messages size[%d]",pInstUserInfo->stAcctHead.acct_id,(uhd.uid).c_str(), idx,lnCmpSize);
					
					try
					{
						pInsertDca->UnBindParam();
						pInsertDca->BindBlobParam(1, szbuftmp);
						pInsertDca->BindParam(2, pInstUserInfo->stAcctHead.acct_id);//跨账户就是mod_group_id
						pInsertDca->BindParam(3, pInstUserInfo->stAcctHead.messagesource);
						pInsertDca->BindParam(4, pInstUserInfo->stAcctHead.BatchNo);
						pInsertDca->GetSqlString(SQL);
						
						DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "SQL name[%s], Accord to SQL[%s] to insert DCA messages", sqlname, SQL.c_str());
						pInsertDca->Execute();

					}
					catch(UDBException &e)
					{
						if(e.GetErrorCode() != 0)
						{
							free(szbuftmp);
							pInsertDca->Close();
							DCBIZLOG(DCLOG_LEVEL_ERROR, e.GetErrorCode(), "", "SQL[%s] Exception[%s]", SQL.c_str(), e.ToString());
							return e.GetErrorCode();
						}
					}
				}
				pInsertDca->Connection()->Commit();
				pInsertDca->Close();
				free(szbuftmp);

				en.clear();
       			en.encode(uhd);
				pInstUserInfo->stAcctHead.BillingFlag = 4;//消息走DCA赋值为4
				
				en.encode(pInstUserInfo->stFmtHead);
				en.encode(pInstUserInfo->stAcctHead);
				pInstUserInfo->stAcctBodyReq.VDetailInst.clear();
				pInstUserInfo->stAcctBodyReq.VOfrInst.clear();
				pInstUserInfo->stAcctBodyReq.VRealRatingFee.clear();
				pInstUserInfo->stAcctBodyReq.VReqRatingFee.clear();
				en.encode(pInstUserInfo->stAcctBodyReq);

				sEn.clear();
				sEn = HexEncode(en.data(),en.size());

				lnCmpSize = m_Compress->Compress(sEn.c_str(),sEn.size());
				szCmpMsg = NULL;
				szCmpMsg = m_Compress->GetCompress();

				string tempbuf="";
				tempbuf.resize(lnCmpSize+1,0);
				memcpy((void*)tempbuf.c_str(),(void*)szCmpMsg,lnCmpSize);

				en.clear();
		        en.encode(tempbuf);
				sEn.clear();
				sEn = HexEncode(en.data(),en.size());
					
			}
			
			memset(buf,0x00,sizeof(buf));
			sprintf(buf, "$$$$$$%010d", lnCmpSize);
        	sEn.insert(0, buf,16);
			DCBIZLOG(DCLOG_LEVEL_INFO,0,"DCCallZKServerMaker::process","Compress this messaage, Compress size[%d]", lnCmpSize);
		}

		//增加账户分流信息
		memset(buf,0x00,sizeof(buf));
        sprintf(buf, "$$$$$$%016ld%02d", pInstUserInfo->lnSpecialId,pInstUserInfo->nAcctType);
        sEn.insert(0, buf,24);

		/*// 头部加固定64位uid
		string strbuf(39,' ');
		strbuf+=uhd.uid;//uid长度为25
        sEn.insert(0, strbuf.c_str(),64);*/
		
        // 头部加固定16位微妙时间戳
        gettimeofday(&tmv, NULL);
		memset(buf,0x00,sizeof(buf));
        sprintf(buf, "%016ld", tmv.tv_sec*1000000+tmv.tv_usec);
        sEn.insert(0, buf,16);

		/*//头部固定25位IP&PORT，框架自动加
		memset(buf,0,sizeof(buf));
		sprintf(buf, "%025ld", tmv.tv_sec*1000000+tmv.tv_usec);
        sEn.insert(0, buf,25);*/
		string ZKServName = m_strZKServName;
		GetGrayServName(pInstUserInfo->nLatnId,pInstUserInfo->lnRouteAcctId,ZKServName);
		
		SMsgPair sMsg;
		sMsg.nReSend = 0;
		sMsg.nAcctType = pInstUserInfo->nAcctType;
		sMsg.lnSpecialId = pInstUserInfo->lnSpecialId;
		sMsg.nLatnId = pInstUserInfo->nLatnId;
		sMsg.nLogBillingCycleId = pInstUserInfo->nBillingCycleId;
		sMsg.nPrdInstNum = pInstUserInfo->nPrdInstNum;
		sMsg.nSubInstNum = pInstUserInfo->nSubInstNum;
		sMsg.nOfrInstNum = pInstUserInfo->nOfrInstNum;
		sMsg.nGroupNum = pInstUserInfo->nGroupNum;
		sMsg.lnPidId = pInstUserInfo->lnPidId;
		sMsg.vecTransferId = pInstUserInfo->vecTransferId;
		sMsg.uuid = uhd.uid;
		sMsg.servName = ZKServName;
		sMsg.sendMsg = sEn;
		sMsg.nMsgType = pInstUserInfo->nMsgType;
		sMsg.nMessagesource = pInstUserInfo->stAcctHead.messagesource;
		sMsg.nBatchNo = pInstUserInfo->stAcctHead.BatchNo;
		sMsg.nBillingFlag = pInstUserInfo->stAcctHead.BillingFlag;

		if(1==m_nSendType)//流量控制
		{
			while(1)
			{
				if(m_pairList->size()<m_nQueueSize)//队列大小目前固定1024
				{
					gettimeofday(&tmv, NULL);
					sMsg.begin = tmv;
					m_pairList->in(sMsg.uuid,sMsg);
					break;
				}
				sleep(2);
			}
		}
		if(0==nBillingFlagOri) //一组消息中的最后一条
		{
			m_vecGroupUid.push_back(uhd.uid);
			m_pairList->InGroupUid(uhd.uid,m_vecGroupUid);
			m_vecGroupUid.clear();
		}
		else
		{
			m_vecGroupUid.push_back(uhd.uid);
		}
		//服务化调用
		memset(extbuf,0x00,sizeof(extbuf));
		sprintf(extbuf,"{\"%s\":\"%ld\"}",m_strHashKey.c_str(),sMsg.lnSpecialId);
		strextbuf = extbuf;
		memset(group,0x0,sizeof(group));
		
		if (m_nOpenKpi)
		{			
			DCKpiSender::instance()->GetFullGroup("TraceKpi", pInstUserInfo->nLatnId, group);//获取指标输出组标识符
			DCKpiSender::instance()->cycle_inc(m_ptrBPMon, group, "k1");
		}
		
		nRet = m_pClient->invokeAsync(ZKServName, uhd.uid, sEn,"",0,strextbuf);//参数4为ZKAddr，为空自动获取，参数5为是否应答，=0为需要应答
		if(nRet < 0)
		{			
			if (m_nOpenKpi)
			{
				DCKpiSender::instance()->cycle_inc(m_ptrBPMon, group, "k2");
			}
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCCallZKServerMaker::process","ZKServName[%s] call invokeAsync failed: uid[%s] acctid[%ld] msgsize[%ld] nBillingCycleId[%d] ret[%d] ", ZKServName.c_str(),(uhd.uid).c_str(),sMsg.lnSpecialId,sEn.size(),pInstUserInfo->nBillingCycleId,nRet);
			return -1;
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_INFO,0,"DCCallZKServerMaker::process","ZKServName[%s] call invokeAsync sucess: uid[%s] acctid[%ld] msgsize[%ld] nBillingCycleId[%d] ", ZKServName.c_str(),(uhd.uid).c_str(),sMsg.lnSpecialId,sEn.size(),pInstUserInfo->nBillingCycleId);
			DCDATLOG("RE00005:%s!%s!%ld!%ld",ZKServName.c_str(),(uhd.uid).c_str(),sMsg.lnSpecialId,sEn.size());
		}
		
		usleep(m_nFlowSleepUs);
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","DCCallZKServerMaker::process End!!!");

	return 0;
}

//根据灰度路由规则表获取服务名
int DCCallZKServerMaker::GetGrayServName(const int &nLatnId,const long &lnRouteAcctId,string &serviceBILL)
{
	//路由规则获取服务名称
	string sServiceName = "";
	char szTmp[128] = {0};
	map<string,string> inMapParam;
	sprintf(szTmp,"%d",nLatnId);
	inMapParam.insert(map<string,string>::value_type("LatnID",szTmp));
	sprintf(szTmp,"%ld",lnRouteAcctId);
	inMapParam.insert(map<string,string>::value_type("ACCTID",szTmp));

	int ret = DCGrayscaleRoute::instance()->GetRouteServiceName(m_sRouteProcess.c_str(), inMapParam, sServiceName);
	if (ret < 0 || 0 == sServiceName.length())
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Get RouteServiceName failed.");
		return -1;
	}
	serviceBILL = sServiceName;
	return 0;
}


/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					ocs项目组
*All rights reserved.
*
*Filename：
*		DCLoadOldUser.h
*Indentifier：
*
*Description：
*		月租用户数据加载实现类
*Version：
*		V1.0
*Author:
*		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_LOADOLDUSER_H__
#define __DC_LOADOLDUSER_H__
#include <list>
#include <map>
#include <vector>
#include <string>
#include "DCLoadUserBase.h"
#include "DCEventTime.h"
#include "DCOBJSet.h"
#include "publiclib.h"
#include "DCDataCenter.h"
#include "DCRDEventType.h"
#include "DCComboAdaptPrdInst.h"
#include "DCCallZKServerMaker.h"

#include "DCKpiSender.h"



class DCLoadOldUser : public DCLoadUserBase
{
	public:	
		DCLoadOldUser()
		{
            m_writeLogCount=0;
            m_nPrdInstNum=0;
            m_nSubInstNum=0;
            m_nOfrInstNum=0;
		}
		
		virtual ~DCLoadOldUser();
		
		virtual int InitSub();
		virtual int process();
		virtual int processDcfs();
		
		int InitSubLatnId();

    private:
		int initCycEvtPro(map<long,STEventPro> &MapEvtType,int nProcId,int nProcType);
		
		int DealNewUser();
		int DealFrezenUser();
		int DealOldUser();

		int DealTrialUser();//处理试算

		int LoadFrozenUser(int nLatnId);
		int LoadFrozenUserDcfServ(int nLatnId, std::vector<string> &vecSQLFields);
		int loadOldUser(DCDateTime &cur_time,int nLatnId,bool isTrial);//增加试算标识
		bool IsNeedDeal(DCDateTime dtLast, DCDateTime dtcur,const int nSetHour,DCDateTime dtLastStart);
		int SetLastDealTime(const DCDateTime cur_time,const STEventPro stEvn,bool isStart);
		int SetLastDealTimeDcfServ(const DCDateTime cur_time, const int nProType, const int nProcId, const int nLatnId, bool isStart);
		int AcctChangeDeal(long lnAcctId, int nLatnId, long lnAcctCounts, int nCurAcctPrdCount,long lnLastSerId,long &lnSendMsgCount,T_UserInfo tUserInfo);
		bool WriteRentLog(int nLatnId,long lnPidId,int nPrdInstNum,int nSubInstNum,int nOfrInstNum, bool bEnd);
		int QueryInstChange(int nLatnId);

		int UpdateInstDealed(long lnOfrInstChangeID, int nState,int nLatnId);
		int UpdateFrozenDealed(long lnFrozenID, int nState,int nLatnId);
		int DealTransferPrd(int nLatnId);
		int UpdateTransferState(long lnTransferID, int nState,int nLatnId);
		int AssemblyMessage(int nLatnId,int nBillingCycleID,long lnAcctId,set<long>& setPrdGroup,vector<long>& vecTransferId,int nMsgType);
		int loadOldCrossUser(DCDateTime &cur_time,int nLatnId,bool isTrial);
		int CrossOfrInstChangeDeal(long lnModGroupId, int nLatnId, long lnLastAcctId,long &lnSendMsgCount,bool isTrial);		
		int LoadCycOfrRel(std::multimap<int,ST_CycOfrRel> & mapCycOfrRel);
		int MutexOfr();
		bool FindOfrId(long lnOfrId);
	    int UpdateDealState(const long ofr_inst_change_id, const int nLatnID);
		int UpdateDealStateFrozen(const long lFrozenId);

		//统一任务分发
		int DealFrezenUserDcfServ();
		int DealOldUserDcfServ();
		int DealTrialUserDcfServ();//处理试算
		int DealNewUserDcfServ();
		int QueryInstChangeDcfServ(int nLatnId);
	private:

		std::list<string> m_ltLatn;

		DCCallZKServerMaker* m_pCallServer;

		std::vector<long> m_vecCurAcctList;
		std::set<long> m_setLastSendTransferPrdRec;
		
		DCKpiMon* m_ptrBPMon;

        //日志表缓存数据
        int m_writeLogCount;
        int m_nPrdInstNum;
        int m_nSubInstNum;
        int m_nOfrInstNum;
};
#endif


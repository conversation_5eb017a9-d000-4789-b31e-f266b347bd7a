#include <stdio.h>
#include "paramcfg.h"
#include "DCLogMacro.h"




#define DB_INVALID_CONN 1  //连接数据库的指针无效
#define DB_NO_CONFIG   2  //配置文件没有配置该数据库的选项
#define DB_INVALID_CONN_STR  -210 //配置的数据库选项无效，没有带@,/等标记
#define DB_CONNECT_FAILED    -211 //按照配置的数据库名，用户名及密码，连接不上数据库

DCParamCfg* DCParamCfg::m_pParamCfg = NULL;

DCParamCfg::DCParamCfg()
{
    //m_pParamCfg = NULL;
    m_iReadDb = 0;
    m_ParamCfgOldMap.clear();
    m_ParamCfgNewMap.clear();
    m_pParamCfgCurrentMap = NULL;
    m_iWhichMapUse = 0;
    m_nReadConfigState = 1;
}
int DCParamCfg::Init(int nReadConfigState)
{
    m_nReadConfigState =nReadConfigState;
}

int DCParamCfg::AlterPathFormat(char* szpSectionPath, char* szNewSectionPath)
{
    while(*szpSectionPath)
    {
        //
        if (*szpSectionPath != '\\' )
        {
            *szNewSectionPath = *szpSectionPath;
        }
	 else
	 {
	     *szNewSectionPath = '/';
	 }
	 szpSectionPath++;
	 szNewSectionPath++;
    }
	
    return 0;
}

int DCParamCfg::CheckPathFormat(char*  szPath)	
{
    if (NULL == szPath)
    {
        return -1;
    }
	
    int nLen = strlen(szPath);
	
    char szTmp[128]={0};

    //第一个字段不为'/',则添加第一个字段为'/'
    if (szPath[0] != '/')
    {
        szTmp[0] = '/';
	 memcpy(szTmp+1, szPath, nLen);
    }
	
    //最后一个字段不为'/',则添加第一个字段为'/'
    if (szPath[nLen-1] != '/')
    {
        szTmp[strlen(szTmp)] = '/';
    }

    //
    memcpy(szPath, szTmp, strlen(szTmp));
	
    return 0;
}
/*-----------------------------------------------------
 *  读取没有子Section的配置
 * strValue:输出要读取的配置项值
 * szpName:配置项名
 * szpSectionPath:配置项所在的根配置项名
---------------------------------------------------*/
bool DCParamCfg::OCS_readConfig(string& strValue,const char* szpName,const char* szpSectionPath)
{
/*
    //调用老的读取配置文件的接口
    if (false == bIsDB)
    {
	 DC_DEBUG((MY_DEBUG_02 ACE_TEXT("PublicLib::OCS_readConfig, Name=%s, SectionPath=%s!"), 
	 	               szpName, szpSectionPath));  
	 
        return PublicLib::OCS_readConfig(strValue, szpName, szpSectionPath);
    }
*/	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "szpSectionPath[%s],szpName[%s]!",szpSectionPath, szpName);
	
    if ((szpName == NULL) || (szpSectionPath == NULL))
    {
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "input param is null!!");
        return false;
    }
	
    if ('\0' == *szpName)
    {
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "szpSectionPath[%s],szpName[%s], read failed!",szpSectionPath, szpName);
        return false;
    }

    char szNewSectionPath[128] = {0};
	
    //如果路径名称中有'\'符号,统一转换成'/'
    AlterPathFormat(const_cast<char*>(szpSectionPath), szNewSectionPath);

    int iRst;
    //首先连接数据库,把表tb_param_cfg中的数据读取到
    if (m_iReadDb == 0)
    {
        //不论下面连接数据库和读取数据成功与否,都置标识位
        //如果读取成功,则不需要再次连接数据库读取数据
        //如果失败,则所有的读取配置都失败,这样程序就启动失败
	    m_iReadDb = 1;		 
       
    }

    //生成配置项的绝对路径,如:/oracle/oraclestring
    char szCfgName[200] = {0};
    if (szNewSectionPath[0] != '\0')
    {
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "szpSectionPath[%s]\n",szNewSectionPath);

        sprintf(szCfgName,"/%s/%s", szNewSectionPath, szpName);
    }
    else
    {
        sprintf(szCfgName,"%s", szpName);
    }

    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "szCfgName[%s]\n",szCfgName);

    string strtmp = szCfgName;
	
    //不读配置文件，从数据库中读取配置信息
    iRst  = FindParamCfgValue(strValue, szCfgName);
    if (iRst < 0)
    {
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "FindParamCfgValue failed!");
        return false;
    }
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "Read %s/%s: [%s]",szNewSectionPath,szpName,strValue.c_str());
    return true;
}

/*---------------------------------------------------
 * 读取有一个子Section的配置
 * strValue:输出要读取的配置项值
 * szpName:配置项名
 * szpSubSectionPath:所要读取的配置的父配置项名
 * szpSectionPath:配置项所在的根配置项名
----------------------------------------------------*/
bool DCParamCfg::OCS_readConfig(string& strValue,const char* szpName,const char* szpSubSectionPath,const char* szpSectionPath)
{
/*
    if (false == bIsDB)
    {
	 DC_DEBUG((MY_DEBUG_02 ACE_TEXT("PublicLib::OCS_readConfig, Name=%s, SubSectionPath=%s, SectionPath=%s!"), 
	 	               szpName, szpSubSectionPath, szpSectionPath));  
	 
        return PublicLib::OCS_readConfig(strValue, szpName, szpSubSectionPath, szpSectionPath);
    }
*/
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "szpSubSectionPath[%s],szpName[%s]!\n",szpSubSectionPath, szpName);

    if ((szpName == NULL) || (szpSubSectionPath == NULL) || (szpSectionPath == NULL))
    {
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "input param is null!");
        return false;
    }

    if (('\0' == *szpSubSectionPath) || ('\0'== *szpName))
    {
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "szpSubSectionPath[%s],szpName[%s], read failed!",szpSubSectionPath, szpName);
        return false;
    }

    char szNewSectionPath[128] = {0};
	
    //如果路径名称中有'\'符号,统一转换成'/'
    AlterPathFormat(const_cast<char*>(szpSectionPath), szNewSectionPath);

    //
    char szNewSubSectionPath[128] = {0};
    AlterPathFormat(const_cast<char*>(szpSubSectionPath), szNewSubSectionPath);
	
    int iRst;
	
    //首先连接数据库,把表tb_param_cfg中的数据读取到
    if (m_iReadDb == 0)
    {
        //不论下面连接数据库和读取数据成功与否,都置标识位
        //如果读取成功,则不需要再次连接数据库读取数据
        //如果失败,则所有的读取配置都失败,这样程序就启动失败
	    m_iReadDb = 1;	
       
    }

    //生成配置项的绝对路径,如:/offline/collectpt/collectid
    char szCfgName[200] = {0};
    if (szNewSectionPath[0] !='\0')
    {
    	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "szpSectionPath[%s]",szNewSectionPath);
        sprintf(szCfgName,"/%s/%s/%s", szNewSectionPath, szNewSubSectionPath, szpName);
    }
    else
    {
        sprintf(szCfgName,"/%s/%s", szNewSubSectionPath, szpName);
    }
	
    //不读配置文件，从数据库中读取配置信息
    iRst = FindParamCfgValue(strValue, szCfgName);
    if (iRst < 0)
    {
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "FindParamCfgValue failed!");
        return false;
    }
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "Read %s: [%s]",szCfgName,strValue.c_str());

    return true;
}

int DCParamCfg::FindParamCfgValue(string& strValue, const char* szpName)
{
    //容器为空，打印日志，直接返回
    if (0 == m_pParamCfgCurrentMap->size())
    {
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "FindParamCfgValue failed, m_ParamCfgMap.size=0, szpName:%s!",szpName);
	    return -1;
    }
	
    //根据配置项的绝对路径查询配置项的值
    map<string, stParamCfg>::iterator iter;
    iter = m_pParamCfgCurrentMap->find(szpName);
    if (iter == m_pParamCfgCurrentMap->end())
    {
        //
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "FindParamCfgValue not find config:%s!",szpName);
	    return -1;	
    }
    else
    {
        //配置的值若为空,则
        if ('\0' == (iter->second).szItemValue[0])
        {
            strValue = (iter->second).szItemDefaultValue;
        }
	 else
	 {
	     strValue = (iter->second).szItemValue;
	 }
    }
	
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "%s:%s!",szpName, strValue.c_str());
	
    return 0;
}



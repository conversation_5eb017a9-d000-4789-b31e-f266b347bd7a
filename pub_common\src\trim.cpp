/****************************************************************************************
*Copyrights  2006，深圳天源迪科计算机有限公司
*						OCS项目组
*All rights reserved.
*
* Filename：	trim.cpp		
* Indentifier：		
* Description： 		
* Version：		V1.0
* Author:		guoxd
* Finished：	2006年12月27日
* History:		
******************************************************************************************/

#include <string.h>
#include "trim.h"

void trim(string& str, const TRIM_TYPE trimtype, const string& trimchar)
{
  string::size_type bpos, epos;

  if ((trimtype == LTRIM) || (trimtype == LRTRIM))
  {
    bpos = str.find_first_not_of(trimchar);
    if (bpos != string::npos)
      str = str.substr(bpos);
    else str = "";
  }

  if ((trimtype == RTRIM) || (trimtype == LRTRIM))
  {
    epos = str.find_last_not_of(trimchar);
    if (epos != string::npos)
      str = str.substr(0, epos + 1);
    else str = "";
  }
}


void trim(string& str, const TRIM_TYPE trimtype)
{
  trim(str, trimtype, WHITE_SPACE);
}


void trim(char *dest, const char *src, const TRIM_TYPE trimtype)
{
  string str(src);
  trim(str, trimtype);
  str.copy(dest, str.length());
}

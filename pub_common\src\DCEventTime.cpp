/*******************************************
*Copyrights  2008，深圳天源迪科信息技术股份有限公司
*					OCS项目组
*All rights reserved.
*
* Filename：	T_eventTime.h
* Indentifier：		
* Description:	时间处理类
* Version：		V1.0
* Author:		
* Modified by: 
* Finished：	2008年5月7日
* History:
********************************************/
#include <string.h>
#include "DCEventTime.h"

DCEventTime::DCEventTime()
{
    m_year = -1;
    m_mon = -1;
    m_day = -1;
    m_hour = -1;
    m_min = -1;
    m_sec = -1;
}

/************************************************************************
函数说明:	取该事件下次发生的确切时间
调用时机:	本次事件处理完毕的时候
输入:	根据该事件的处理时间规则顺序查找
输出:	
返回值:	该事件下次发生的确切时间
************************************************************************/
DCDateTime DCEventTime::getNextEventTime(const DCDateTime &v_lastTime)
{
    int nYear   = v_lastTime.GetYear();
    int nMon    = v_lastTime.GetMonth();
    int nDay    = v_lastTime.GetDay();
    int nHour   = v_lastTime.GetHour();
    int nMin    = v_lastTime.GetMin();
    int nSec    = v_lastTime.GetSec();

    //ACE_DEBUG((MY_DEBUG_01 ACE_TEXT("year[%d],mon[%d],day[%d],hour[%d],min[%d],sec[%d]"),m_year,m_mon,m_day,m_hour,m_min,m_sec));
    //ACE_DEBUG((MY_DEBUG_01 ACE_TEXT("v_last_time [%s]"),v_lastTime.ToString(string("YYYYMMDDHHNNSS")).c_str()));

   // printf("year[%d],mon[%d],day[%d],hour[%d],min[%d],sec[%d]",m_year,m_mon,m_day,m_hour,m_min,m_sec);
   // printf("v_last_time [%s]",v_lastTime.ToString("YYYYMMDDHHNNSS").c_str());

    bool bEnough = true;
    bool bFreeTime = true;
    DCDateTime dt;

    if(m_sec == -1)
    {      
        nSec = 0;
        bEnough = false;
    }
    else if(nSec < m_sec) 
    {
        bFreeTime = false;
        nSec = m_sec;
    }
    else
    {
        bFreeTime = false;
        bEnough = false;
        nSec = m_sec;
    }

    if(m_min == -1)
    {
        if(bFreeTime)   //即m_sec == -1
        {
            nMin = 0;
            bEnough = false;
        }
        else if(!bEnough)//即nSec > m_sec 
        {
            ++nMin;
            if(nMin < 60) 
            {
                bEnough = true;
            }
            else 
            {
                nMin -= 60;
            }
        }
    }
    else if(nMin < m_min)
    {
        nMin = m_min;
        bEnough = true;
        bFreeTime = false;
    }
    else if(nMin == m_min)
    {
        bFreeTime = false;
    }
    else  //nMin > m_min
    {
        bFreeTime = false;
        bEnough = false;
        nMin = m_min;
    }

    if(m_hour == -1)
    {
        if(bFreeTime)
        {
            nHour = 0;
            bEnough = false;
        }
        else if (!bEnough)
        {
            ++nHour;
            if(nHour < 24) 
            {
                bEnough = true;
            }
            else 
            {
                nHour -= 24;
            }
        }
    }
    else if(nHour < m_hour)
    {
        nHour = m_hour;
        bEnough = true;
        bFreeTime = false;
    }
    else if(nHour == m_hour)
    {
        bFreeTime = false;
    }
    else
    {
        bFreeTime = false;
        nHour = m_hour;
        bEnough = false;
    }

    if(m_day == -1)
    {
        if(bFreeTime)
        {
            nDay = 1;
            bEnough = false;
        }
        else if (!bEnough)
        {
            ++nDay;
            if(nDay > DCDateTime::GetMaxDay(nYear,nMon) )
            {
                nDay = 1;
            }
            else 
            {
                bEnough = true;
            }
        }
    }
    else if(nDay < m_day)
    {
        nDay = m_day;
        bEnough = true;
        bFreeTime = false;
    }
    else if(nDay == m_day)
    {
        bFreeTime = false;
    }
    else
    {
        bFreeTime = false;
        bEnough = false;
        nDay = m_day;
    }

    if(m_mon == -1)
    {
        if(bFreeTime)
        {
            nMon = 1;
            bEnough = false;
        }
        else if(!bEnough)
        {
            ++nMon;
            if(nMon > 12) 
            {
                nMon = 1;
            }
            else 
            {
                bEnough = true;
            }
        }
    }
    else if(nMon < m_mon)
    {
        nMon = m_mon;
        bEnough = true;
        bFreeTime = false;
    }
    else if(nMon == m_mon)
    {
        bFreeTime = false;
    }
    else
    {
        bEnough = false;
        bFreeTime = false;
        nMon = m_mon;
    }

    if(m_year == -1)
    {
        if(!bEnough)
        {
            ++nYear;
        }
    }
    else 
    {
        nYear = m_year;
    }

    char sTime[20];
    sprintf(sTime,"%04d%02d%02d%02d%02d%02d",nYear,nMon,nDay,nHour,nMin,nSec);
    dt.FromString(sTime);
    return dt;
}


/************************************************************************
函数说明:	取该事件最近一次发生的确切时间
调用时机:	
输入:	根据该事件的处理时间规则顺序查找
输出:	
返回值:	该事件最近一次发生的确切时间
*************************************************************************/
DCDateTime DCEventTime::getLastEventTime( const DCDateTime &v_currTime )
{
    int nYear   = v_currTime.GetYear();
    int nMon    = v_currTime.GetMonth();
    int nDay    = v_currTime.GetDay();
    int nHour   = v_currTime.GetHour();
    int nMin    = v_currTime.GetMin();
    int nSec    = v_currTime.GetSec();

    bool bEnough = true;
    bool bFreeTime = true;
    DCDateTime dt;

    if(m_sec == -1)
    {      
        nSec = 0;
        bEnough = false;
    }
    else if(nSec > m_sec) 
    {
        bFreeTime = false;
        nSec = m_sec;
    }
    else
    {
        bFreeTime = false;
        bEnough = false;
        nSec = m_sec;
    }

    if(m_min == -1)
    {
        if(bFreeTime)
        {
            nMin = 0;
            bEnough = false;
        }
        else if(!bEnough)
        {
            --nMin;
            if(nMin >= 0) 
            {
                bEnough = true;
            }
            else 
            {
                nMin += 60;
            }
        }
    }
    else if(nMin > m_min)
    {
        nMin = m_min;
        bEnough = true;
        bFreeTime = false;
    }
    else if(nMin == m_min)
    {
        bFreeTime = false;
    }
    else
    {
        bFreeTime = false;
        bEnough = false;
        nMin = m_min;
    }

    if(m_hour == -1)
    {
        if(bFreeTime)
        {
            nHour = 0;
            bEnough = false;
        }
        else if (!bEnough)
        {
            --nHour;
            if(nHour >= 0) 
            {
                bEnough = true;
            }
            else 
            {
                nHour += 24;
            }
        }
    }
    else if(nHour > m_hour)
    {
        nHour = m_hour;
        bEnough = true;
        bFreeTime = false;
    }
    else if(nHour == m_hour)
    {
        bFreeTime = false;
    }
    else
    {
        bFreeTime = false;
        nHour = m_hour;
        bEnough = false;
    }

    if(m_day == -1)
    {
        if(bFreeTime)
        {
            nDay = 1;
            bEnough = false;
        }
        else if (!bEnough)
        {
            --nDay;
            if(nDay <=0 ) 
            {
                bEnough = false;
            }
            else 
            {
                bEnough = true;
            }
        }
    }
    else if(nDay > m_day)
    {
        nDay = m_day;
        bEnough = true;
        bFreeTime = false;
    }
    else if(nDay == m_day)
    {
        bFreeTime = false;
    }
    else
    {
        bFreeTime = false;
        bEnough = false;
        nDay = m_day;
    }

    if(m_mon == -1)
    {
        if(bFreeTime)
        {
            nMon = 1;
            bEnough = false;
        }
        else if(!bEnough)
        {
            --nMon;
            if(nMon <= 0) 
            {
                nMon = 12;
            }
            else 
            {
                bEnough = true;
            }
        }
    }
    else if(nMon > m_mon)
    {
        nMon = m_mon;
        bEnough = true;
        bFreeTime = false;
    }
    else if(nMon == m_mon)
    {
        bFreeTime = false;
    }
    else
    {
        bEnough = false;
        bFreeTime = false;
        nMon = m_mon;
    }

    if(m_year == -1)
    {
        if(!bEnough)
        {
            --nYear;
        }
    }
    else nYear = m_year;

    if(nDay <= 0)
    {
        nDay = DCDateTime::GetMaxDay(nYear,nMon) ;
    }

    char sTime[20];
    sprintf(sTime,"%04d%02d%02d%02d%02d%02d",nYear,nMon,nDay,nHour,nMin,nSec);
    dt.FromString(sTime);
    return dt;
}



/*******************************************
 *Copyrights   2007，深圳天源迪科计算机有限公司
 *                   技术平台项目组
 *All rights reserved.
 *
 *Filename：
 *       DCPairList.cpp
 *Indentifier：
 *
 *Description：
 *       消息路由序列号对队列
 *Version：
 *       V1.0
 *Author:
 *       YF.Du
 *Finished：
 *       2008年10月10日
 *History:
 *       2008/10/10  V1.0 文件创建
 ********************************************/
#include "DCPairList.h"

using namespace std;

DCPairList::DCPairList()
{
	pthread_mutex_init(&m_mutex, NULL);
	pthread_mutex_init(&m_mutexUid, NULL);
}

DCPairList::~DCPairList()
{
	pthread_mutex_destroy(&m_mutex);
	pthread_mutex_destroy(&m_mutexUid);
}

int DCPairList::size()
{
	//return m_PairMap.size();
	return m_setInvokeAsyncUid.size();
}

int DCPairList::in(const string uuid,const SMsgPair sMsg)
{
	int nRet = 0;
	nRet = lock();
	if (nRet)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCPairList::in","list in lock error[%s]", uuid.c_str());
		return -1;
	}
	
	pair<map<string, SMsgPair>::iterator, bool> iter;
	iter = m_PairMap.insert(pair<string, SMsgPair>(uuid, sMsg));
	if (!(iter.second))
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCPairList::in","list in error[%s]", uuid.c_str());
		nRet = -1;
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCPairList::in","list in [%s]", uuid.c_str());
		nRet = 0;
	}
	m_setInvokeAsyncUid.insert(uuid);
	
	unlock();
	return nRet;
}

int DCPairList::out(const string uuid,SMsgPair &sMsg,string ipErrorCode)
{
	int nRet = 0;
	map<string, SMsgPair>::iterator iter;
	map<string, vector<string> >::iterator  iterGroupUid;
	vector<SMsgPair> vecMsg;
	
	nRet = lock();
	if (nRet)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCPairList::out","list out lock error.keyuid=[%s]", uuid.c_str());
		return -1;
	}
	
	m_setInvokeAsyncUid.erase(uuid);	

	iter = m_PairMap.find(uuid);
	if(iter != m_PairMap.end())
	{
		if(ipErrorCode.length()> 0)
		{
			size_t npos = ipErrorCode.rfind(";");
			if(npos != string::npos)
			{
				string errorCode = ipErrorCode.substr(npos + 1);
				iter->second.nCallBackRetCode = atoi(errorCode.c_str());
			}			
		}
	}	
	
	iterGroupUid = m_mapGroupUid.find(uuid);
	if(iterGroupUid == m_mapGroupUid.end())//返回非关键uid,空出队列即可
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","can not find uuid[%s] in m_mapGroupUid", uuid.c_str());
		unlock();
		return 0;
	}
	else
	{
		//取出同组所有uid
		vector<string> vecOtherUid = iterGroupUid->second;
		for(int idx=0;idx<vecOtherUid.size();idx++)
		{
			iter = m_PairMap.find(vecOtherUid[idx]);
			if(iter!=m_PairMap.end())
			{
				if(m_setCallbackUid.find(vecOtherUid[idx])!=m_setCallbackUid.end())//uid已经返回过
				{
					iter->second.nPrdInstNum = 0;
					iter->second.nSubInstNum = 0;
					iter->second.nOfrInstNum = 0;
				}
				else
				{
					m_setCallbackUid.insert(vecOtherUid[idx]);
				}
				sMsg = iter->second;
				vecMsg.push_back(iter->second);
				iter->second.vecTransferId.clear();
				m_PairMap.erase(iter);
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCPairList::out","list out[%s]", vecOtherUid[idx].c_str());
		}
		iterGroupUid->second.clear();
		m_mapGroupUid.erase(iterGroupUid);	
	}
	
	unlock();
	
	nRet = UidQueLock();
	if (nRet)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCPairList::out","UidQueLock .keyuid=error[%s]", uuid.c_str());
		return -1;
	}	

	for(int idx=0;idx<vecMsg.size();idx++)
	{				
		m_uidPairMap.push_back(vecMsg[idx]);
	}
	UidQueUnLock();
	
	return nRet;
}

void DCPairList::clearTimeOut(long timeOut, vector<SMsgPair>& vecTank)
{
	int nRet = 0;
	long nusec = 0;
	int num = 0;
	map<string, SMsgPair>::iterator iter;
	map<string, vector<string> >::iterator  iterGroupUid;
	set<string>::iterator iterAsyncUid;
	
	vecTank.clear();
	
	nRet = lock();
	if (nRet)
	{
		return;
	}
	struct timeval current;
	gettimeofday(&current, NULL);
	
	//timeOut 改成微秒级	
	for (iterAsyncUid = m_setInvokeAsyncUid.begin(); iterAsyncUid!=m_setInvokeAsyncUid.end();)
	{
		iter = m_PairMap.find(*iterAsyncUid);
		if(iter== m_PairMap.end())
		{
			m_setInvokeAsyncUid.erase(iterAsyncUid++);//找不到资料的就直接从发送队列清除
			continue;
		}
		nusec = (current.tv_sec-iter->second.begin.tv_sec)*1000000+(current.tv_usec-iter->second.begin.tv_usec);
		if (nusec>timeOut)//已超时
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Msg TimeOut:ModGroupId[%ld],uuid[%s],nReSend[%d],sendsec[%ld.%06ld], nowsec[%ld.%06ld], diffsec[%ld.%06ld].",
				iter->second.lnSpecialId,(*iterAsyncUid).c_str(),iter->second.nReSend,iter->second.begin.tv_sec,iter->second.begin.tv_usec,current.tv_sec,current.tv_usec,nusec/1000000,nusec%1000000);
			
			iterGroupUid = m_mapGroupUid.find(*iterAsyncUid);
			if(iterGroupUid != m_mapGroupUid.end())//关键uid超时
			{
				if(iter->second.nReSend < 2) // 超时重发两次
				{
					m_setTimeOutUid.insert(iterGroupUid->first);//超时需重发队列，同组全都需重发
				}
				else
				{
					vecTank.push_back(iter->second);//同组超时只看关键uid
					m_setNoResUid.insert(iterGroupUid->first);

					iterGroupUid->second.clear();
					m_mapGroupUid.erase(iterGroupUid);
					iter->second.vecTransferId.clear();
					m_PairMap.erase(iter);
				}
			}
			m_setInvokeAsyncUid.erase(iterAsyncUid++);
			continue;
		}
		iterAsyncUid++;
	}

	unlock();

}

int DCPairList::lock()
{
	return pthread_mutex_lock(&m_mutex);
}

int DCPairList::unlock()
{
	return pthread_mutex_unlock(&m_mutex);
}


int DCPairList::GetTimeOutList(vector<SMsgPair> 	  &vecTimeOutList)
{
	int nRet = 0;
	map<string, SMsgPair>::iterator iter;
	
	nRet = lock();
	if (nRet)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCPairList::GetTimeOutList","list get lock error.");
		return -1;
	}

	vecTimeOutList.clear();
	for(std::set<std::string>::iterator it = m_setTimeOutUid.begin(); it != m_setTimeOutUid.end(); ++it)
	{
		iter = m_PairMap.find(*it);
		if(iter==m_PairMap.end())
			continue;
		vecTimeOutList.push_back(iter->second);
	}
	m_setTimeOutUid.clear();
	
	unlock();
	return nRet;
}

bool DCPairList::UidQueOut(SMsgPair &msgpair)
{
	int nRet = 0;
	bool isReturn=false;
	
	nRet = UidQueLock();
	if (nRet)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCPairList::UidQueOut","UidQueOut out lock error");
		return false;
	}
	
	if (!m_uidPairMap.empty())
	{
		vector<SMsgPair>::iterator iter = m_uidPairMap.end()-1;
		msgpair = *iter;
		iter->vecTransferId.clear();
		m_uidPairMap.erase(iter);
		isReturn = true;		
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","UidQueOut a record,uuid[%s],lnSpecialId[%ld],nCallBackRetCode[%d]",msgpair.uuid.c_str(),msgpair.lnSpecialId,msgpair.nCallBackRetCode);
	}
	
	UidQueUnLock();

	return isReturn;
}

int DCPairList::UidQueLock()
{
	return pthread_mutex_lock(&m_mutexUid);
}

int DCPairList::UidQueUnLock()
{
	return pthread_mutex_unlock(&m_mutexUid);
}

int DCPairList::SetClear()
{
	if (lock())
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCPairList::GetTimeOutList","SetClear lock error.");
		return -1;
	}

	m_setCallbackUid.clear();
	m_setTimeOutUid.clear();
	m_setNoResUid.clear();
	m_PairMap.clear();
	m_mapGroupUid.clear();
    m_setInvokeAsyncUid.clear();

	unlock();
	
	return 0;
}


int DCPairList::InGroupUid(const string uuid,const vector<string> vecGroupUid)
{
	int nRet = 0;
	nRet = lock();
	if (nRet)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCPairList::InGroupUid","list in lock error[%s]", uuid.c_str());
		return -1;
	}
	
	pair<map<string, vector<string> >::iterator, bool> iter;
	iter = m_mapGroupUid.insert(pair<string, vector<string> >(uuid, vecGroupUid));
	if (!(iter.second))
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCPairList::InGroupUid","list in error[%s]", uuid.c_str());
		nRet = -1;
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCPairList::InGroupUid","list in [%s]", uuid.c_str());
		nRet = 0;
	}
	
	unlock();
	return nRet;
}



int DCPairList::update(const string uuid,const SMsgPair sMsg)
{
	int nRet = 0;
	map<string, SMsgPair>::iterator iter;
	
	nRet = lock();
	if (nRet)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCPairList::update","list update lock error[%s]", uuid.c_str());
		return -1;
	}
	
	iter = m_PairMap.find(uuid);
	if (iter == m_PairMap.end())
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCPairList::update","list update error[%s]", uuid.c_str());
		nRet = -1;
	}
	else
	{
		m_PairMap[iter->first] = sMsg;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCPairList::update","list update [%s]", uuid.c_str());
		nRet = 0;
	}
	m_setInvokeAsyncUid.insert(uuid);
	
	unlock();
	return nRet;
}



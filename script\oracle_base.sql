-----------------------20170930 begin------------------------------


-- Create table
create table CYCLE_EVENT_PROFILE
(
  EVENT_TYPE_ID     NUMBER(9) not null,
  DEDUCT_TYPE       NUMBER(2) not null,
  EVENT_TIME_MONTH  NUMBER(4) not null,
  EVENT_TIME_DAY    NUMBER(4) not null,
  EVENT_TIME_HOUR   NUMBER(4) not null,
  PROCESS_TYPE      CHAR(1) default '0' not null,
  EVENT_TIME_MINUTE NUMBER(4) default 0
);
-- Add comments to the table 
comment on table CYCLE_EVENT_PROFILE
  is '月租事件属性表';
-- Add comments to the columns 
comment on column CYCLE_EVENT_PROFILE.EVENT_TYPE_ID
  is '事件类型Id,主键';
comment on column CYCLE_EVENT_PROFILE.DEDUCT_TYPE
  is '事件对应的扣费类型,0：月租,1：日租,2：试算,3:  按学期扣费, 6:停机保号';
comment on column CYCLE_EVENT_PROFILE.EVENT_TIME_MONTH
  is '事件对应的扣费月份,保留字段';
comment on column CYCLE_EVENT_PROFILE.EVENT_TIME_DAY
  is '事件对应的扣费日期,按自然月,如取值20表示每月20号';
comment on column CYCLE_EVENT_PROFILE.EVENT_TIME_HOUR
  is '事件对应的扣费时间点,如取值0表示每天0点,默认值0';
-- Create/Recreate primary, unique and foreign key constraints 
alter table CYCLE_EVENT_PROFILE
  add primary key (EVENT_TYPE_ID);

-----------------------20170930 end--------------------------------------
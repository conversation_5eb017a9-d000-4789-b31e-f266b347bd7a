/*******************************************
*Copyrights  2008，深圳天源迪科信息技术股份有限公司
*					OCS项目组
*All rights reserved.
*
* Filename：	T_eventTime.h
* Indentifier：		
* Description:	时间处理类
* Version：		V1.0
* Author:		
* Modified by: 
* Finished：	2008年5月7日
* History:
********************************************/
#ifndef DCEVENTTIME_H
#define DCEVENTTIME_H


#include "DCDateTime.h"

class DCEventTime
{

public:
    DCEventTime();
    DCDateTime getNextEventTime(const DCDateTime &v_lastTime);
    DCDateTime getLastEventTime(const DCDateTime &v_currTime);

public:
    int m_evttype;
    int m_year;
    int m_mon;
    int m_day;
    int m_hour;
    int m_min;
    int m_sec;
} ;

#endif


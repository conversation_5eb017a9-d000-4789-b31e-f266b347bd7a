/******************************************************************************************
Copyright           : 2004-5-16, Shenzhen Tianyuan DIC  Co.,Ltd
FileName            : publiclib.cpp
Author              : guoxd
Version             : 
Date Of Creation    : 2004-5-16
Description         : 公共函数实现文件
Others              : 
Function List       : 
    1.  ......
Modification History:
    1.Date  :
      Author  :
      Modification  :
******************************************************************************************/

#include <string.h>
#include <ctype.h>
#include <assert.h>
#include <errno.h>
#include <ace/OS.h>
#include "ace/OS_NS_unistd.h"
#include "ace/OS_NS_netdb.h"
#include <regex.h>
#ifndef WIN32
	#include <sys/types.h>
	#include <sys/stat.h>
	#include <unistd.h>
	#include <fcntl.h>
	#include <sys/socket.h>
	#include <netinet/in.h>
    #include <arpa/inet.h>
//	#include <ace/OS_NS_sys_time.h >
#else
	#include <WINSOCK.H>
	#include <sys\types.h>
	#include <sys\stat.h>
//	#include <ace\os_include\sys\os_time.h >
	#include <io.h>
	#include <fcntl.h>
	#include <ace\OS_NS_sys_time.h>
	#include <ace\OS_NS_stdio.h>
#endif

#include "predefine.h"
#include "publiclib.h"
#include "UtilLocal.h"
#include "paramcfg.h"
#include "ConfigureFile.h"
#include "DCLogMacro.h"


namespace PublicLib
{
/******************************************************************************************
Author          : guoxd  Date Of Creation: 2004-5-16
Founction       :void RTrim(char *pszStr,char ch)
Return          : void
Parameter List  : 
  Param1: char* pszStr 字符串
  Param2: char ch 需要trim的字符
Description     : Trim到字符串右边的指定字符
Calls           :
Called By       :
Other           :
Modify Record   :
******************************************************************************************/  
void RTrim(char *pszStr,char ch)
{
	int nLen = strlen(pszStr);

	while(nLen > 0 && pszStr[nLen - 1] == ch)
	{
		pszStr[nLen - 1] = 0;
		nLen--;
	}
	return;
}



/******************************************************************************************
Author          : guoxd  Date Of Creation: 2004-5-16
Founction       :void LTrim(char *pszStr,char ch)
Return          : void
Parameter List  : 
  Param1: char* pszStr 字符串
  Param2: char ch 需要trim的字符
Description     : Trim到字符串左边的指定字符
Calls           :
Called By       :
Other           :
Modify Record   :
******************************************************************************************/  
void LTrim(char *pszStr,char ch)
{
	int nLen = strlen(pszStr);
	int i = 0;
	while(pszStr[i] == ch && i < nLen)
	{
		i++;
	}

	memmove(pszStr,(pszStr + i),nLen - i +1);

	return ;
}



/******************************************************************************************
Author          : guoxd  Date Of Creation: 2004-5-16
Founction       :void Trim(char *pszStr,char ch)
Return          : void
Parameter List  : 
  Param1: char* pszStr 字符串
  Param2: char ch 需要trim的字符
Description     : Trim到字符串两边的指定字符
Calls           :
Called By       :
Other           :
Modify Record   :
******************************************************************************************/  
void Trim(char *pszStr,char ch)
{
	LTrim(pszStr,ch);
	RTrim(pszStr,ch);
}



/******************************************************************************************
Author          : guoxd  Date Of Creation: 2004-5-16
Founction       :void itoa(int n,std::string &str)
Return          : void
Parameter List  : 
  Param1: int n	 
  Param2: string &str
Description     : 把整数转换成字符串
Calls           :
Called By       :
Other           :
Modify Record   :
******************************************************************************************/  
void itoa(int n,string &str)
{
	char szBuf[10];
	sprintf(szBuf,"%d",n);
	str = szBuf;
}



/******************************************************************************************
Author          : guoxd  Date Of Creation: 2004-5-16
Founction       :GetTime(char *pszTime, int nType)
Return          : void
Parameter List  : 
  Param1: char *pszTime	 
  Param2: int nType  YYYYMMDDHHMMSSssss YYYYMMDDHHMMSS YYYYMMDD
Description     : 按指定格式得到当前时间
Calls           :
Called By       :
Other           :
Modify Record   :
******************************************************************************************/  
void GetTime(char *pszTime, int nType)
{
     /*************************
     * tm 的结构
     * int tm_sec;      // seconds after the minute - [0,61] 
     * int tm_min;      // minutes after the hour - [0,59] 
     * int tm_hour;     // hours - [0,23] 
     * int tm_mday;     // day of month - [1,31] 
     * int tm_mon;      // month of year - [0,11] 
     * int tm_year;     // years since 1900 
     * int tm_wday;     // days since Sunday - [0,6] 
     * int tm_yday;     // days since January 1 - [0,365] 
     * int tm_isdst;    // daylight savings time flag 
     ***************************/
    struct tm SystemTime;    

    time_t Time=time(NULL);  

    //SystemTime = localtime(&Time);    
    localtime_r(&Time,&SystemTime);

    switch(nType)
    {
    case YYYYMMDDHHMMSSssssUs:
        {
            sprintf(pszTime, "%0.4d%0.2d%0.2d%0.2d%0.2d%0.2d%0.6d",
                1900+SystemTime.tm_year, SystemTime.tm_mon+1, SystemTime.tm_mday,
                SystemTime.tm_hour, SystemTime.tm_min, SystemTime.tm_sec,(ACE_OS::gettimeofday().usec())%1000000);
        }
        break;
    case YYYYMMDDHHMMSSssss:
        {
            sprintf(pszTime, "%0.4d%0.2d%0.2d%0.2d%0.2d%0.2d%0.3d",
                1900+SystemTime.tm_year, SystemTime.tm_mon+1, SystemTime.tm_mday,
                SystemTime.tm_hour, SystemTime.tm_min, SystemTime.tm_sec,(ACE_OS::gettimeofday().msec())%1000);
        }
        break;
    case YYYYMMDDHHMMSS:
        {
            sprintf(pszTime, "%0.4d%0.2d%0.2d%0.2d%0.2d%0.2d",
                1900+SystemTime.tm_year, SystemTime.tm_mon+1, SystemTime.tm_mday,
                SystemTime.tm_hour, SystemTime.tm_min, SystemTime.tm_sec);
        }
        break;
    case YYYYMMDD:
        {
            sprintf(pszTime, "%0.4d%0.2d%0.2d",SystemTime.tm_year+1900,SystemTime.tm_mon+1,SystemTime.tm_mday);
        }
        break;
    case YYYYMM:
    	sprintf(pszTime, "%0.4d%0.2d",SystemTime.tm_year+1900,SystemTime.tm_mon+1);
    }

}



/******************************************************************************************
Author          : guoxd  Date Of Creation: 2004-5-16
Founction       :int IsDate(char* pcDate,int nDateType)
Return          :int 0:成功 -1:失败
Parameter List  : 
  Param1: char *pcDate
  Param2: int ndDateType
Description     : 按指定格式,判断是否是合法的时间
Calls           :
Called By       :
Other           :
Modify Record   :
******************************************************************************************/  
int IsDate(char* pcDate,int nDateType)
{
    int yy,mm,dd,hh,mi,ss;
    char year[5],month[3],day[3],hour[3],min[3],sec[3];
	char szLongDate[17];//14 + 2 +1
    short mdays[2][12]= {
        { 31,28,31,30,31,30,31,31,30,31,30,31 },
        { 31,29,31,30,31,30,31,31,30,31,30,31 } 
    };
	int nInputLen = strlen(pcDate);
	int nTypeLen = 0;
	bool bShort = false;

    switch(nDateType)
	{
	case YYMMDD:
	case YYYYMM:
		nTypeLen = 6;
		bShort = true;
		break;
	case YYYYMMDD:
		nTypeLen = 8;
		bShort = true;
		break;
	case YYMMDDHHMMSS:
		nTypeLen = 12;
		bShort = false;
		break;
	case YYYYMMDDHHMMSS:
		nTypeLen = 14;
		bShort = false;
		break;
	default:/*未知的日期类型*/
		return -1;
	}

	if(nInputLen < nTypeLen)/*字符串长度不足*/
	{
		return -1;
	}

	if(YYMMDDHHMMSS == nDateType || YYMMDD == nDateType)
	{
		memset(szLongDate,0,17);
		strncpy(szLongDate,"20",2);
		strncpy(szLongDate + 2,pcDate,nTypeLen);
		pcDate = szLongDate;
		nTypeLen += 2;
	}

    if( IsNumber(pcDate,nTypeLen) != 1 ) return -1 ;    

    strncpy(year,pcDate,4);   year[4]=0;
    strncpy(month,pcDate+4,2);month[2]=0;

    if(YYYYMM != nDateType)
    {
		strncpy(day,pcDate+6,2);  day[2]=0;
	}
    else
	{
		strncpy(day,"15",2);  day[2]=0;
	}

    yy=atoi(year);
    mm=atoi(month);
    dd=atoi(day);
    

    if( (yy < 2000)||(yy>2050) ) return -1 ;
    if( (mm < 1)||(mm>12) ) return -1 ;
	int nMonArrayIndex = (IsLeapYear(yy) == 1)?1:0;
    if( (dd < 1)||(dd>mdays[nMonArrayIndex][mm-1])) return -1 ;

    if(bShort)
	{
		return 0;
	}

    strncpy(hour,pcDate+8,2);hour[2]=0;
    strncpy(min,pcDate+10,2);min[2] =0;
    strncpy(sec,pcDate+12,2);sec[2] =0;

    hh=atoi(hour);
    mi=atoi(min);
    ss=atoi(sec);	

    if( (hh < 0)||(hh>23) ) return -1 ;
    if( (mi < 0)||(mi>59) ) return -1 ;
    if( (ss < 0)||(ss>59) ) return -1 ;

    return 0 ;
}



void Change24(char* pTime)
{
	char year[5], month[3], day[3], hour[3];

	strncpy(year, pTime, 4);
	year[4] = 0;	

	strncpy(month, &pTime[4], 2);
	month[2] = 0;	

	strncpy(day, &pTime[6], 2);
	day[2] = 0;


	strncpy(hour, &pTime[8], 2);
	hour[2] = 0;	

//	printf("要处理的时间：[%s]-->[%s][%s][%s][%s]", pTime, year, month, day, hour);

	if(strncmp(hour, "24", 2) != 0)
	{
		return;	
	}	

	int total_days = 0;
	switch(atoi(month))
	{
		case 1:
		case 3:
		case 5:
		case 7:
		case 8:
		case 10:
		case 12:
			total_days = 31;
			break;
		case 4:
		case 6:
		case 9:
		case 11:
			total_days = 30;
			break;
		case 2:
			if(IsLeapYear(atoi(year)) == 0)
			{
				total_days = 29;
			}	
			else
			{
				total_days = 28;
			}
			break;
		default:
			break;
	}
	
	if(atoi(day) == total_days)
	{
		if(atoi(month) == 12)
		{
			int nYear = atoi(year);
			nYear++;
			sprintf(pTime, "%d0101000000", nYear);	
			return;
		}

		int nMonth = atoi(month);
		nMonth++;
		sprintf(pTime, "%s%02d01000000", year, nMonth);
		return;		
	}
	else
	{
		int nDay = atoi(day);
		nDay++;
		sprintf(pTime, "%s%s%02d000000", year, month, nDay);
		return;	
	}
}



/******************************************************************************************
Author          : guoxd  Date Of Creation: 2004-5-16
Founction       :int IsNumber(char *pcNumber,int nLen)
Return          :int 0:成功 -1:失败
Parameter List  : 
  Param1: char *pcDate
  Param2: int ndDateType
Description     : 按指定格式,判断是否是合法的时间
Calls           :
Called By       :
Other           :
Modify Record   :
******************************************************************************************/  
int IsNumber(char *pcNumber,int nLen)
{
	char strNumber[256];
	char* pstrNumLocal = strNumber;
    memset(pstrNumLocal,0,256);
	if(-1 == nLen)
	{
		nLen = strlen(pcNumber);
	}

	strncpy(pstrNumLocal,pcNumber,nLen);
	Trim(pstrNumLocal);

    while(*pstrNumLocal)
	{
		if((*pstrNumLocal>'9')||(*pstrNumLocal<'0')) return -1 ;
		pstrNumLocal ++ ;
	}

    return 0 ;
}


/******************************************************************************************
Author          : guoxd  Date Of Creation: 2004-5-16
Founction       :int IsLeapYear(int nYear)
Return          :int 0:成功 -1:失败
Parameter List  : 
  Param1: int nYear
Description     : 是否是闰年
Calls           :
Called By       :
Other           :
Modify Record   :
******************************************************************************************/  
int IsLeapYear(int nYear)
{
    if ((nYear % 4 == 0 && nYear % 100 != 0) || (nYear % 400 == 0))
        return(0);
    else
        return(-1);
}


/******************************************************************************************
Author          : guoxd  Date Of Creation: 2004-5-16
Founction       :void GetDateTime(char *pszTime, time_t nRec)
Return          :void
Parameter List  : 
  Param1: char *pszTime
  time_t: nRec
Description     : 根据输入的nRec(秒),得到YYYYMMDDHHMMSS格式的时间
Calls           :
Called By       :
Other           :
Modify Record   :
******************************************************************************************/  
void GetDateTime(char *pszTime, time_t nRec)
{
     /*************************
     * tm 的结构
     * int tm_sec;      // seconds after the minute - [0,61] 
     * int tm_min;      // minutes after the hour - [0,59] 
     * int tm_hour;     // hours - [0,23] 
     * int tm_mday;     // day of month - [1,31] 
     * int tm_mon;      // month of year - [0,11] 
     * int tm_year;     // years since 1900 
     * int tm_wday;     // days since Sunday - [0,6] 
     * int tm_yday;     // days since January 1 - [0,365] 
     * int tm_isdst;    // daylight savings time flag 
     ***************************/
    struct tm SystemTime;    

	//nRec = nRec;
    //SystemTime = localtime(&nRec);    
    localtime_r(&nRec,&SystemTime);

	char szTmp[16];
	sprintf(szTmp, "%0.4d%0.2d%0.2d%0.2d%0.2d%0.2d",
		1900+SystemTime.tm_year, SystemTime.tm_mon+1, SystemTime.tm_mday,
		SystemTime.tm_hour, SystemTime.tm_min, SystemTime.tm_sec);

	memcpy(pszTime,szTmp,14);
}



/******************************************************************************************
Author          : wangds
Founction       :void getLBSConfig(char* result,const char* flag,int *status)
Return          :void
Parameter List  : 
  Param1: char* result
  Param2: const char* flag
  Param3: int *status
Description     : 从配置文件中读取配置项
Calls           :
Called By       :
Other           :
Modify Record   :
******************************************************************************************/  
void getLBSConfig(char* result,const char* flag,int *status)
{

    FILE *fp;
    char* fname;
    char content[128];
    int _flag=-1,_add=0;

#ifndef WIN32
    fname=getenv("LBSConfig");
#else
	char szConfPath[256];
	strcpy(szConfPath,"c:\\LBSConfig");
	fname = (char*)szConfPath;
#endif

    *status=-1;    

    if(fname==NULL)
       return;

    fp=fopen(fname,"r");
    if(fp==NULL)
        return;

    fscanf(fp,"%s",content);
    while(!feof(fp))
    {
        if(content[0]=='[')
        {
            fscanf(fp,"%s",content);            

            if(strncmp(content,flag,strlen(flag))==0)
            {
                _flag=1;       
                fscanf(fp,"%s",content);
                break;
            }
         }
        fscanf(fp,"%s",content);
     }

     fclose(fp);
     if(content[0]=='/')
     {
        Trim(content);
        _add=strlen(content);        

        if(content[_add-1]!='/')
        {
            content[_add]='/';
            content[_add+1]='\0';
        }
      }

      strcpy(result,content);
      result[strlen(result)]='\0';
      *status=_flag;      

      return;
}

/******************************************************************************************
Author          : guoxd
Founction       :int GetCfg(char *pszFileName, 
					   char *pszVarName, 
					   char *pszData,
					   char *pszErr)
Return          :int
Parameter List  : 
  Param1: char *pszFileName
  Param2: char *pszVarName
  Param3: char *pszDat
  Param4: char *pszErr
Description     : 从配置文件中读取配置项
Calls           :
Called By       :
Other           :
Modify Record   :
******************************************************************************************/  
int GetCfg(char *pszFileName, 
					   char *pszVarName, 
					   char *pszData,
					   char *pszErr)
{
    FILE * pCFGFile ;
    char szBuffTmp[1024];
    char szVarNameTmp[256];
    char szTitle[40];
    char szDateTmp[1024];
    int ret_code = -1;	

	if(!(pszFileName && pszVarName && pszData && pszErr))
	{
		return -1;
	}	

    strcpy(szVarNameTmp,pszVarName);
    pCFGFile=fopen(pszFileName,"r");
    if(NULL == pCFGFile)
    {
        sprintf(pszErr, "Open %s file fail!", pszFileName);
        return -1 ;
    }

    while (!feof(pCFGFile))
    {
        memset(szBuffTmp, 0, 1024);
        if(NULL != fgets(szBuffTmp, 1023, pCFGFile))
        {
            LTrim(szBuffTmp);
            if(szBuffTmp[0] == COMMENT_SIG) 
                continue ;
            if(0 == strncmp(szBuffTmp, szVarNameTmp, strlen(szVarNameTmp)))
            {
                ret_code = GetVarValue(szBuffTmp, szTitle, szDateTmp,pszErr);
                if(0 == ret_code) 
                {
                    if(0 == strcmp(szTitle, szVarNameTmp))
                    {
                        strcpy(pszData, szDateTmp);
                        break;
                    }
                    else
                    {
                        continue;
                    }
                }
                else
                {
                    sprintf(pszErr, "%s in %s", pszErr, pszFileName);
                    ret_code = -1;
                    break;
                }
            }
            else
            {
                continue;
            }
        }
        else
        {
            continue;
        }
    }    

    if(feof(pCFGFile))
    {
        sprintf(pszErr, "Not find %s in %s", pszVarName, pszFileName);
        ret_code = -1;
    }

    fclose(pCFGFile);

    return ret_code;
}


/******************************************************************************************
Author          : guoxd
Founction       :int GetVarValue(char *pszBuff, 
							char *pszTitle, 
							char *pszData,
							char *pszErr)
Return          :int
Parameter List  : 
  Param1: char *pszBuff
  Param2: char *pszTitle
  Param3: char *pszData
  Param4: char *pszErr
Description     : 把配置文件中指定行,转化为字段名和字段值
Calls           :
Called By       :
Other           :
Modify Record   :
******************************************************************************************/ 
int GetVarValue(char *pszBuff, 
							char *pszTitle, 
							char *pszData,
							char *pszErr)
{
    int i ;
	int nTitleLen = strlen(pszTitle);
    char szBuffTmp[1024];
    strcpy(szBuffTmp, pszBuff);

	//去掉";"后面的部分
    for(i = strlen(szBuffTmp) - 1; i >= 0; i--)
    {
        if(szBuffTmp[i] == COMMENT_SIG || szBuffTmp[i] == '\r'  || szBuffTmp[i] == '\n' || szBuffTmp[i] == ';' ) 
            szBuffTmp[i] = 0;
    }

    RTrim(szBuffTmp);

    i = 0 ;
    while((szBuffTmp[i] != '=')&& (i < MAX_CONFIG_ITEM_LEN))
    {
        pszTitle[i] = szBuffTmp[i];
        i++ ;
    }

    if (i >= MAX_CONFIG_ITEM_LEN )
    {
        sprintf(pszErr, "Bad line %s",szBuffTmp);
        return -1 ;
    }
    else
    {
        pszTitle[i] = 0 ;
        Trim(pszTitle);
    }

    Trim(szBuffTmp + i + 1);
    strcpy(pszData, szBuffTmp + i + 1);

    return 0 ;
}


/******************************************************************************************
Author          : guoxd
Founction       :time_t GetTimeFromShortStr(const char *strtm)
Return          :time_t
Parameter List  : 
  Param1: const char *strtm
Description     : 从字符串转换时间函数，只转换年月日
Calls           :
Called By       :
Other           :
Modify Record   :
******************************************************************************************/ 
time_t GetTimeFromShortStr(const char *strtm)
{
	//校验参数
	ZASSERT(strtm);		

	int year =-1,mon=-1,day=-1;
	//
	sscanf(strtm,"%4d%2d%2d",&year,&mon,&day);
	tm tmtmp = { 0, 0, 0, day,mon-1,year-1900};
	return mktime(&tmtmp);
}


/******************************************************************************************
Author          : guoxd
Founction       :time_t GetTimeFromShortStr(const char *strtm)
Return          :time_t
Parameter List  : 
  Param1: const char *strtm
Description     : 从字符串转换时间函数，转换年月日时分秒
Calls           :
Called By       :
Other           :
Modify Record   :
******************************************************************************************/ 
time_t GetTimeFromLongStr(const char *strtm)
{
	//校验参数
	ZASSERT(strtm);	

	int year=-1,mon=-1,day=-1,hour=0,min=0,sec=0;
	sscanf(strtm,"%4d%2d%2d%2d%2d%2d",&year,&mon,&day,&hour,&min,&sec);
	tm tmtmp = { sec, min,hour , day,mon-1,year-1900};	

	return mktime(&tmtmp);
}


/******************************************************************************************
Author          : guoxd
Founction       :char *strupr(char *str)
Return          :char*
Parameter List  : 
  Param1: char *str
Description     : 将字符串全部转换为大写字符
Calls           :
Called By       :
Other           :
Modify Record   :
******************************************************************************************/ 
char *strupr(char *str)
{   
    ZASSERT(str);
    char *lstr=str;
    while(*lstr!='\0')
    {
        *lstr= static_cast<char> (toupper(*lstr));
		lstr++;
    }

    return str;
}


/******************************************************************************************
Author          : guoxd
Founction       :char *strlwr(char *str)
Return          :char*
Parameter List  : 
  Param1: char *str
Description     : 将字符串全部转换为小写字符
Calls           :
Called By       :
Other           :
Modify Record   :
******************************************************************************************/ 
char *strlwr(char *str)
{
    ZASSERT(str);
    char *lstr=str;

    while(*lstr!='\0')
    {
        *lstr=static_cast<char>(tolower(*lstr));
		lstr++;
    }

    return str;
}


/******************************************************************************************
Author          : guoxd
Founction       :int stricmp(const char *string1,const char *string2)
Return          :int 
Parameter List  : 
  Param1: const char *string1
  Param2: const char *string2
Description     : 字符串比较，忽视大小写
Calls           :
Called By       :
Other           :
Modify Record   :
******************************************************************************************/ 
int stricmp(const char *string1,const char *string2)
{
    //如果有空指针，assert
    ZASSERT(string1!=NULL && string2!=NULL);
    for( ;toupper(*string1) == toupper(*string2) && 
        *string1 != '\0' &&  *string2!= '\0'; 
        ++string1,++string2);

    //如果最后一个字符相等，返回0，     
    return *string1- *string2;
}


/******************************************************************************************
Author          : guoxd
Founction       :int strnicmp(const char *string1, const char *string2, size_t maxlen)
Return          :int 
Parameter List  : 
  Param1: const char *string1
  Param2: const char *string2
  Param3: size_t maxlen
Description     : 字符串定长比较，忽视大小写
Calls           :
Called By       :
Other           :
Modify Record   :
******************************************************************************************/ 
int strnicmp(const char *string1, const char *string2, size_t maxlen)
{
    //如果有空指针，assert
    ZASSERT(string1!=NULL && string2!=NULL);

    //长度为0，不用比较
    if(maxlen<=0)
        return 0;

    for( size_t n=0;toupper(*string1) == toupper(*string2) && 
        n<maxlen -1;
        ++string1,++string2,++n);
		
    //如果最后一个字符相等，返回0，
    return *string1- *string2;
}



/******************************************************************************************
Author          : guoxd
Founction       :int SplitStr(const char *pszStr,const char cSplit,std::list<std:string> ltStr)
Return          :int 
Parameter List  : 
  Param1: const char *pszStr
  Param2: const char cSplit
  Param3: std::list<std:string>& ltStr
Description     : 按照指定分隔符拆分字符串
Calls           :
Called By       : 
Other           :
Modify Record   :
******************************************************************************************/ 
int SplitStr(const char *pszStr,const char cSplit,list<string>& ltStr)
{
	string str;
	char *pch=NULL;
	char szBuf[1024*4]={0};
	char szTmp[512]={0};
	bool bIsFound = false;	

	strncpy(szBuf,pszStr,sizeof(szBuf)-1);
	szBuf[sizeof(szBuf)-1] = 0;
	char *pBase = szBuf;
	
	while (1)
	{
		pch = strchr(pBase, cSplit);
		if ( NULL==pch)
		{
			break;
		}
		
		bIsFound = true;
		
		memset(szTmp, 0, sizeof(szTmp));
		strncpy(szTmp, pBase, pch-pBase);
		szTmp[pch-pBase] = '\0';
		str = szTmp;
		ltStr.push_back(str);
		//strcpy(szBuf, pch+1);  // è??é????…?-?é???????????????????–èˉ‘??ˉ?￠??????ˉè???????oé—?é￠?
		pBase = pch + 1;
	}
	
	if ((true==bIsFound)||strlen(pBase)>0)
	{
		str = pBase;
		ltStr.push_back(str);
	}

	return 0;
}

int SplitStr(const char *pszStr, const char cSplit, vector<string>& ltStr)
{
	string str;
	char *pch=NULL;
	char szBuf[1024*10]={0};
	char szTmp[512]={0};
	bool bIsFound = false;
	
	strncpy(szBuf, pszStr, sizeof(szBuf)-1);
	szBuf[sizeof(szBuf)-1] = 0;
	char *pBase = szBuf;
	
	while (1)
	{
		pch = strchr(pBase, cSplit);
		if ( NULL==pch)
		{
			break;
		}
		
		bIsFound = true;
		
		memset(szTmp, 0, sizeof(szTmp));
		strncpy(szTmp, pBase, pch-pBase);
		szTmp[pch-pBase] = '\0';
		str = szTmp;
		ltStr.push_back(str);
		//strcpy(szBuf, pch+1);
		pBase = pch + 1;
	}
	
	if ((true==bIsFound)||strlen(pBase)>0)
	{
		str = pBase;
		ltStr.push_back(str);
	}
	
	return 0;
}



/******************************************************************************************
Author          : guoxd
Founction       :int SplitStr(const char *pszStr,const char cSplit,std::list<std:string> ltStr)
Return          :int 
Parameter List  : 
  Param1: const char *pszStr
  Param2: const char cSplit
  Param3: std::list<std:string>& ltStr
Description     : 按照指定分隔符拆分字符串
Calls           :
Called By       : 
Other           :
Modify Record   :
******************************************************************************************/ 
int SplitStrInt(const char *pszStr,const char cSplit,list<int>& ltStr)
{
	char *pch=NULL;
	char szBuf[1024*4]={0};
	char szTmp[512]={0};
	bool bIsFound = false;
	
	strncpy(szBuf, pszStr, sizeof(szBuf)-1);
	szBuf[sizeof(szBuf)-1] = 0;
	char *pBase = szBuf;
	
	while (1)
	{
		pch = strchr(pBase, cSplit);
		if ( NULL==pch)
		{
			break;
		}
		
		bIsFound = true;
		
		memset(szTmp, 0, sizeof(szTmp));
		strncpy(szTmp, pBase, pch-pBase);
		szTmp[pch-pBase] = '\0';
		ltStr.push_back(atoi(szTmp));
		//strcpy(szBuf, pch+1);  // è??é????…?-?é???????????????????–èˉ‘??ˉ?￠??????ˉè???????oé—?é￠?
		pBase = pch + 1;
	}
	
	if ((true==bIsFound)||strlen(pBase)>0)
	{
		ltStr.push_back(atoi(pBase));
	}

	return 0;
}

/************************************************************************/
// 订购时间、当前时间、偏移量、开始账期
 //-1表示获取账期失败
/************************************************************************/
int GetDynamicMonth(const char * startTime,const char * currentTime,int off_set,char *BillingCycleStart)
{
	char TempStr[32]={0};
	char TempStr2[32]={0};
    
	//start init
	int startyear=0,startmonth=0,startday=0;
	int currentyear=0,currentmonth=0,currentday=0;
	int iCycle=0;//整月
	int iCycle_int=0;
	int year=0, month=0, day=0;
	//year
	strncpy(TempStr,startTime,4);
	strncpy(TempStr2,currentTime,4);
	startyear=atoi(TempStr);
	currentyear=atoi(TempStr2);
	memset(TempStr,0x0,sizeof(TempStr));
	memset(TempStr2,0x0,sizeof(TempStr2));
    //month
	strncpy(TempStr,startTime+4,2);
	strncpy(TempStr2,currentTime+4,2);
	startmonth=atoi(TempStr);
	currentmonth=atoi(TempStr2);
	memset(TempStr,0x0,sizeof(TempStr));
	memset(TempStr2,0x0,sizeof(TempStr2));
	//day
	strncpy(TempStr,startTime+6,2);
	strncpy(TempStr2,currentTime+6,2);
	startday=atoi(TempStr);
	currentday=atoi(TempStr2);
	////end init 


	int lastday;
	month=currentmonth-startmonth;
	year=currentyear-startyear;
	month=year*12+month;//一共相隔多少个月

    if (off_set == 0)//配置0的情况下，偏移量修改为0
    {
        iCycle = 0;
    }
    else
    {
        iCycle=month/off_set;//
        iCycle_int=month%off_set;
    }
	
	if (iCycle==0)//本周期内的
	{
		strncpy(BillingCycleStart,startTime,8);
	}
	else if (iCycle>0)//不在本期内的//最后一个月比较
	{
		if ((currentday-startday)<0)//在上一个账期内5 7
		{
            lastday=getMaxDay(currentyear,currentmonth);//本账期月份的最后一天
            if (currentday==lastday)
            {
                month=(iCycle*off_set)%12;
                year=(iCycle*off_set)/12;
                year+=(startmonth+month)/12;
                month=(startmonth+month)%12;
               // day=lastday;//那么为下一个账期
            }
            else 
            {
                if (iCycle_int==0)
                {
                    iCycle--;
                }
                if (iCycle==0)//本周期内的
                {
                    strncpy(BillingCycleStart,startTime,8);
                    return 0;
                }
               
                month=(iCycle*off_set)%12;
                year=(iCycle*off_set)/12;
                year+=(startmonth+month)/12;
                month=(startmonth+month)%12;
            }
            if (month==0)
            {
                month=12;
                year--;
            }
			day=startday;	
			sprintf(BillingCycleStart,"%04d%02d%02d",startyear+year,month,day);
		}
		else //刚好新的账期
		{
			month=(iCycle*off_set)%12;
			year=(iCycle*off_set)/12;

			year+=(startmonth+month)/12;
			month=(startmonth+month)%12;
            if (month==0)
            {
                month=12;
                year--;
            }
			sprintf(BillingCycleStart,"%04d%02d%02d",startyear+year,month,startday);
		}
	}
	else
	{
		return -1;//失败
	}
	return 0;
}

/************************************************************************/
/* 获取动态天,定制时间，当前时间，偏移步长，开始账期
 -1返回错误 0正常
************************************************************************/
int GetDynamicDay(const char * startTime,const char * currentTime,int off_set,char *BillingCycleStart)
{
	char TempBuffer[64]={0};
	strncpy(TempBuffer,startTime,8);
	sprintf(TempBuffer,"%s000000",TempBuffer);
	unsigned long offsetsecond=off_set*3600*24;//秒
	
    
	time_t currentsecond=GetTimeFromLongStr(currentTime);//当前时间
	time_t startsecond =GetTimeFromLongStr(TempBuffer);//订购时间
	time_t diffsecond =currentsecond-startsecond; //差值

    unsigned long lnCycle= 0;
    unsigned long lnCycle_int=0;

	//diffsecond=diffsecond>0?diffsecond:0;
    if (offsetsecond == 0)
    {
    	  lnCycle = 0;
    }
    else
    {
        lnCycle=diffsecond/offsetsecond;
        lnCycle_int=diffsecond%offsetsecond;
    }
	unsigned long AddSecond=lnCycle*off_set*3600*24;

    selftimeAdd(TempBuffer,AddSecond);
	strncpy(BillingCycleStart,TempBuffer,8);
	return 0;
}

void StartDaemon()
{
#ifndef WIN32
    pid_t pid;
    pid=fork();
    if(0 > pid)
    {
        //printf("Create daemon fail!\n");
        exit(1);
    }
    else if(pid > 0)
    {
        exit(0); 
    }

    setsid();
    chdir("/");
    umask(0);
#endif

    return;
}




/******************************************************************************************
Author          : guoxd
Founction       :int MoveEmptyFile(const char *pszFileName)
Return          :int 
Parameter List  : 
  Param1: const char *pszFileName
Description     : 删除空文件
Calls           :
Called By       : 
Other           :
Modify Record   :
******************************************************************************************/ 
int MoveEmptyFile(const char *pszFileName)
{
	//测试文件是否存在
#ifdef WIN32
	if( access(pszFileName,0) < 0 )
	{
		return -1;
	}
#else
	if( access(pszFileName,F_OK) < 0 )
	{
		return -1;
	}
#endif

	struct stat buf;

	if( stat(pszFileName,&buf) < 0 )
	{
		return -1;
	}

	if( 0 == buf.st_size )
	{
		//删除空文件
		unlink(pszFileName);
	}	
}



/******************************************************************************************
Author          : guoxd
Founction       :int TruncateFile(long lnFileLen)
Return          :int 
Parameter List  : 
  Param2: const char *pszFileName
  Param1: long lnFileLen
Description     : 截断文件
Calls           :
Called By       : 
Other           :
Modify Record   :
******************************************************************************************/ 
int TruncateFile(const char *pszFileName,const long lnFileLen)
{
	ZASSERT(pszFileName);
	if( lnFileLen < 0 )
	{
		return -1;
	}

#ifdef WIN32
	if( access(pszFileName,0) < 0 )
	{
		return -1;
	}
#else
	if( access(pszFileName,F_OK) < 0 )
	{
		return -1;
	}
#endif	

	struct stat buf;

	if( stat(pszFileName,&buf) < 0 )
	{
		return -1;
	}

	if( buf.st_size < lnFileLen )
	{
		return -1;
	}

#ifndef WIN32
	if( truncate(pszFileName,lnFileLen) < 0 )
	{
		return -1;
	}
#endif

	return 0;
}



/******************************************************************************************
Author          : 
Founction       :selftimeAdd(char* begin_time,int seconds)
Return          :void 
Parameter List  : 
  Param2: char *begin_time
  Param1: int seconds
Description     : 时间函数,在begin_time的基础上增加seconds秒，当seconds为负时，减少相应的秒
Calls           :
Called By       : 
Other           :
Modify Record   :modify @2009-09-03
                 增加对seconds为负的支持，修改了TimeCheck()函数
******************************************************************************************/ 
void selftimeAdd(char* begin_time,int seconds)
{
  int year,mon,day,hh,mm,ss;
  char temp[5];
  char szOldTime[15];
  struct tm tm1,tm2;
  time_t ltime;
  temp[0]='\0';  

  strcpy(szOldTime,begin_time);
  strncpy(temp,begin_time,4);

  year=atol(temp);
  year=year-1900;
  memset(temp,'\0',5);
  strncpy(temp,begin_time+4,2);

  mon=atol(temp)-1;
  strncpy(temp,begin_time+6,2);

  day=atol(temp);
  strncpy(temp,begin_time+8,2);

  hh=atol(temp);
  strncpy(temp,begin_time+10,2);

  mm=atol(temp);
  strncpy(temp,begin_time+12,2);

  ss=atol(temp);

  memset(&tm1,0,sizeof(tm));

  tm1.tm_sec=ss;
  tm1.tm_min=mm;
  tm1.tm_hour=hh;
  tm1.tm_mday=day;
  tm1.tm_mon=mon;
  tm1.tm_year=year;

  ltime=mktime(&tm1);
  ltime+=seconds;
  //tm2=localtime(&ltime);  
  localtime_r(&ltime,&tm2);

  sprintf(begin_time,"%d%.2d%.2d%.2d%.2d%.2d",tm2.tm_year+1900,tm2.tm_mon+1,
            tm2.tm_mday,tm2.tm_hour,tm2.tm_min,tm2.tm_sec);

  //进行时间正确性校验,因为夏令时的关系，导致最后的时间可能会多一小时
  TimeCheck(szOldTime,begin_time,seconds); 

  return;
}



/******************************************************************************************
Author          : 
Founction       :ReadLock(int fd,int cmd,off_t offset,off_t len)
Return          :int 
Parameter List  : 
  Param1: int fd		文件描述符
  Param2: int cmd		F_GETTLK,F_SETLK(设置非阻塞锁),FSETLKW(设置阻塞锁)
  Param3: off_t offset	锁开始偏移量(绝对地址)
  Param4: off_t len		锁的长度
Description     : 设置读锁
Calls           :
Called By       : 
Other           :
Modify Record   :
******************************************************************************************/ 
int ReadLock(int fd,int cmd,off_t offset,off_t len)
{
#ifndef WIN32
	struct flock lock;	

	lock.l_type=F_RDLCK;
	lock.l_start=offset;
	lock.l_whence=SEEK_SET;
	lock.l_len=len;	

	return(fcntl(fd,cmd,&lock));
#endif

	return 0;
}




/******************************************************************************************
Author          : 
Founction       :ReadLock(int fd,int cmd,short whence,off_t offset,off_t len)
Return          :int 
Parameter List  : 
  Param1: int fd		文件描述符
  Param2: int cmd		F_GETTLK,F_SETLK(设置非阻塞锁),FSETLKW(设置阻塞锁)
  Param3: short whence  开始位置 SEEK_SET,SEEK_CUR,SEEK_END
  Param4: off_t offset	锁开始偏移量(相对地址)
  Param5: off_t len		锁的长度
Description     : 设置读锁
Calls           :
Called By       : 
Other           :
Modify Record   :
******************************************************************************************/ 
int ReadLock(int fd,int cmd,short whence,off_t offset,off_t len)
{
#ifndef WIN32
	struct flock lock;

	lock.l_type=F_RDLCK;
	lock.l_start=offset;
	lock.l_whence=whence;
	lock.l_len=len;	

	return(fcntl(fd,cmd,&lock));
#endif

	return 0;
}



/******************************************************************************************
Author          : 
Founction       :WriteLock(int fd,int cmd,off_t offset,off_t len)
Return          :int 
Parameter List  : 
  Param1: int fd		文件描述符
  Param2: int cmd		F_GETTLK,F_SETLK(设置非阻塞锁),FSETLKW(设置阻塞锁)
  Param3: off_t offset	锁开始偏移量(绝对地址)
  Param4: off_t len		锁的长度
Description     : 设置写锁
Calls           :
Called By       : 
Other           :
Modify Record   :
******************************************************************************************/ 

int WriteLock(int fd,int cmd,off_t offset,off_t len)
{
#ifndef WIN32
	struct flock lock;

	lock.l_type=F_WRLCK;
	lock.l_start=offset;
	lock.l_whence=SEEK_SET;
	lock.l_len=len;

	return(fcntl(fd,cmd,&lock));
#endif

	return 0;
}



/******************************************************************************************
Author          : 
Founction       :WriteLock(int fd,int cmd,short whence,off_t offset,off_t len)
Return          :int 
Parameter List  : 
  Param1: int fd		文件描述符
  Param2: int cmd		F_GETTLK,F_SETLK(设置非阻塞锁),FSETLKW(设置阻塞锁)
  Param3: short whence  开始位置 SEEK_SET,SEEK_CUR,SEEK_END
  Param4: off_t offset	锁开始偏移量(相对地址)
  Param5: off_t len		锁的长度
Description     : 设置写锁
Calls           :
Called By       : 
Other           :
Modify Record   :
******************************************************************************************/ 
int WriteLock(int fd,int cmd,short whence,off_t offset,off_t len)
{
#ifndef WIN32
	struct flock lock;

	lock.l_type=F_WRLCK;
	lock.l_start=offset;
	lock.l_whence=whence;
	lock.l_len=len;	

	return(fcntl(fd,cmd,&lock));
#endif

	return 0;
}




/******************************************************************************************
Author          : 
Founction       :FreeLock(int fd,int cmd,off_t offset,off_t len)
Return          :int 
Parameter List  : 
  Param1: int fd		文件描述符
  Param2: int cmd		F_GETTLK,F_SETLK(设置非阻塞锁),FSETLKW(设置阻塞锁)
  Param3: off_t offset	锁开始偏移量(绝对地址)
  Param4: off_t len		锁的长度
Description     : 释放锁
Calls           :
Called By       : 
Other           :
Modify Record   :
******************************************************************************************/ 
int FreeLock(int fd,int cmd,off_t offset,off_t len)
{
#ifndef WIN32
	struct flock lock;

	lock.l_type=F_UNLCK;
	lock.l_start=offset;
	lock.l_whence=SEEK_SET;
	lock.l_len=len;

	return(fcntl(fd,cmd,&lock));
#endif

	return 0;
}



/******************************************************************************************
Author          : 
Founction       :FreeLock(int fd,int cmd,short whence,off_t offset,off_t len)
Return          :int 
Parameter List  : 
  Param1: int fd		文件描述符
  Param2: int cmd		F_GETTLK,F_SETLK(设置非阻塞锁),FSETLKW(设置阻塞锁)
  Param3: short whence  开始位置 SEEK_SET,SEEK_CUR,SEEK_END
  Param4: off_t offset	锁开始偏移量(相对地址)
  Param5: off_t len		锁的长度
Description     : 释放锁
Calls           :
Called By       : 
Other           :
Modify Record   :
******************************************************************************************/ 
int FreeLock(int fd,int cmd,short whence,off_t offset,off_t len)
{
#ifndef WIN32
	struct flock lock;

	lock.l_type=F_UNLCK;
	lock.l_start=offset;
	lock.l_whence=whence;
	lock.l_len=len;

	return(fcntl(fd,cmd,&lock));
#endif

	return 0;
}



/******************************************************************************************
Author          : guoxd
Founction       :GetFileSize(char *pszFileName)
Return          :size_t 
Parameter List  : 
  Param1: char *pszFileName
Description     : 文件长度
Calls           :
Called By       : 
Other           :
Modify Record   :
******************************************************************************************/ 
size_t GetFileSize(const char *pszFileName)
{
	ZASSERT(pszFileName);

	struct stat buf;

	if(stat(pszFileName,&buf) < 0)
	{
		return -1;
	}

	return buf.st_size;
}



/******************************************************************************************
Author          : guoxd
Founction       :SumFile(const char *pszFileName,const char bak_type)
Return          :int 0:成功 1:失败
Parameter List  : 
  Param1: const char *pszFileName
  Param2: const char bak_type 'B':备份 'R':删除 'C':恢复
Description     : 对文件进行备份、删除和移动操作
Calls           :
Called By       : 
Other           :
Modify Record   :
******************************************************************************************/ 
int SumFile(const char *pszFileName,const char bak_type)
{
	ZASSERT(pszFileName);

	char szBakFileName[256];

	sprintf(szBakFileName,"%s.bk",pszFileName);

	if('B' != bak_type && 'R' != bak_type && 'C' != bak_type)
	{
		ZTRACE("备份类型(%c)不合法\n");
		return -1;
	}

	switch(bak_type)
	{
	case 'B':
		return BackupFile(pszFileName,szBakFileName);
	case 'R':
		return RemoveFile(szBakFileName);
	case 'C':
		return RestoreFile(pszFileName,szBakFileName);
	}

	return 0;
}



/******************************************************************************************
Author          : guoxd
Founction       :int BackupFile(const char *pszFileName,const char *pszBakFileName)
Return          :0:成功 1:失败
Parameter List  : 
  Param1: const char *pszFileName
  Param2: const char *pszBakFileName
Description     : 文件备份
Calls           :
Called By       : 
Other           :
Modify Record   :
******************************************************************************************/ 
int BackupFile(const char *pszFileName,const char *pszBakFileName)
{
	ZASSERT(pszFileName);
	ZASSERT(pszBakFileName);

	int fd_in,fd_out;
	char buffer[BUFFER];
	char *buffer_p;
	int bytes_read;
	int bytes_write;

	//打开原始文件
	fd_in = open(pszFileName,O_RDONLY);
	if(fd_in < 0)
	{
		ZTRACE("打开文件(%s)出错(%s)\n",pszFileName,strerror(errno));
		return -1;
	}

	//建立备份文件
	fd_out = open(pszBakFileName,O_RDWR|O_CREAT|O_TRUNC,S_IREAD|S_IWRITE);
	if(fd_out < 0)
	{
		close(fd_in);
		ZTRACE("建立文件(%s)出错(%s)\n",pszBakFileName,strerror(errno));
		return -1;
	}

	//EINTR=Interrupted system call
	while((bytes_read = read(fd_in,buffer,BUFFER)) != 0)
	{
		if(bytes_read < 0 /*&& EINTR == errno*/)
		{
			break;
		}

		buffer_p=buffer;

		while((bytes_write=write(fd_out,buffer_p,bytes_read)) != 0)
		{
			if(bytes_write < 0 /*&& EINTR == errno*/)
			{
				break;
			}
			else if(bytes_read == bytes_write)
			{
				break;
			}
			else
			{
				buffer_p+=bytes_write;
				bytes_read-=bytes_write;
			}
		}

		if(bytes_write < 0)
		{
			break;
		}
	}

	if(bytes_read < 0 || bytes_write < 0)
	{
		close(fd_in);
		close(fd_out);
		ZTRACE("备份文件(%s/%s)失败(%s)\n",pszFileName,pszBakFileName,strerror(errno));
		return -1;
	}

	close(fd_in);
	close(fd_out);

	return 0;
}



/******************************************************************************************
Author          : guoxd
Founction       :int RevoveFile(const char *pszFileName)
Return          :0:成功 1:失败
Parameter List  : 
  Param1: const char *pszFileName
Description     : 文件删除
Calls           :
Called By       : 
Other           :
Modify Record   :
******************************************************************************************/ 
int RemoveFile(const char *pszFileName)
{
	ZASSERT(pszFileName);

	if(unlink(pszFileName) < 0)
	{
		ZTRACE("删除文件()失败\n",pszFileName);
		return -1;
	}

	return 0;
}



/******************************************************************************************
Author          : guoxd
Founction       :int RestoreFile(const char *pszFileName,const char *pszBakFileName)
Return          :0:成功 1:失败
Parameter List  : 
  Param1: const char *pszFileName
  Param2: const char *pszBakFileName
Description     : 文件恢复
Calls           :
Called By       : 
Other           :
Modify Record   :
******************************************************************************************/ 
int RestoreFile(const char *pszFileName,const char *pszBakFileName)
{
	ZASSERT(pszFileName);
	ZASSERT(pszBakFileName);

	if(rename(pszBakFileName,pszFileName) < 0)
	{
		char buffer[256];
		sprintf(buffer, "恢复文件(%s/%s)失败\n",pszBakFileName,pszFileName);
		ZASSERT(buffer);
		return -1;
	}

	return 0;
}


/******************************************************************************************
Author          : guoxd
Founction       :SplitFileName(char *pszPath,char *pszDir,  char *pszFile)
Return          :无
Parameter List  : 
  Param1: char *pszPath 全路径名
  Param2: char *pszDir, 短路径名
  Param3: char *pszFile 文件名
Description     : 把文件名拆分为路径名和文件名
Calls           :
Called By       : 
Other           :
Modify Record   :
******************************************************************************************/ 
void SplitFileName(const char *pszPath,char *pszDir,  char *pszFile)
{
	char szNeatDir[MAX_PATH];		//短路径名
	char *pch;

	if(!pszPath || !pszDir || !pszFile)return;

	int nPathLen = strlen(pszPath);
	int nShortFileLen = 0;

	strcpy(szNeatDir,pszPath);

	pch = szNeatDir + nPathLen - 1;//定位至最后一个字符

	//pch直接操作了szNeatDir指向的字符串。
	while ((*pch != DIR_SPLITTER) && (pch >= szNeatDir))
	{
		*(pch--) = '\0';
		nShortFileLen++;
	}

	strncpy(pszFile,pszPath + nPathLen - nShortFileLen,nShortFileLen);
	*(pszFile + nShortFileLen) = '\0';
	strcpy(pszDir,szNeatDir);
}



/******************************************************************************************
Author          : guoxd
Founction       :int pcount(int argc,char ** argv)
Return          :无
Parameter List  : 
  Param1: int argc
  Param2: char **argv
Description     : 查看程序是否已运行
Calls           :
Called By       : 
Other           :
Modify Record   :
******************************************************************************************/ 
int pcount(char szProgName[])
{
    char argument[MAX_PATH];
    char commands[MAX_PATH];
    char filename[MAX_PATH];
    char dirname[MAX_PATH];

    memset(argument,0,sizeof(argument));
    memset(commands,0,sizeof(commands));
    memset(filename,0,sizeof(filename));
    memset(dirname,0,sizeof(dirname));

    SplitFileName(szProgName,dirname,filename);
    strcpy(argument,filename);
    sprintf(commands,"ps -ef |grep \"%s\" | grep -v sh |grep -v grep |grep -v %d |grep -v vi",argument,getpid());
	
    return(system(commands));
}




/**********************************************************************
函数名称：GetNextTag
函数功能：取得下一个Tag的相关信息，包括Tag本身占的字节数和Tag的值
输入参数：pbInBuffer――指向输入缓冲区的指针，
输出参数：wTag――Tag值, wTagLen――Tag本身占的字节数
返回值：0：成功   -1：失败
**********************************************************************/
int GetNextTag(BYTE* pbInBuffer, long& wTag, unsigned short& wTagLen)
{
    if (NULL == pbInBuffer)
    {
        return -1;
    }

    //获取原始话单当前字段的tag
    if(30 < (0x1F & pbInBuffer[0]))	//tag值大于30
    {
        if( pbInBuffer[1]>127 )
        {
        	wTag =pbInBuffer[0]*65536 + pbInBuffer[1]*256+ pbInBuffer[2];
        	wTagLen = 3;
        }
        else
        {
        	wTag =pbInBuffer[0]*256+ pbInBuffer[1];
        	wTagLen = 2;
        }	
    }
    else							//tag值小于等于30，用单字节编码
    {
        wTag = pbInBuffer[0];
        wTagLen = 1;
    }

    return 0;
}



/**********************************************************************
函数名称：GetTagLenInfo
函数功能：取得Length的相关信息，包括Length本身占的字节数和Length值
输入参数：pbInBuffer――指向输入缓冲区的指针，
输出参数：wLenLen――Length本身占的字节数, wConLen――Length的值
返回值：SUCC：成功   FAIL：失败
**********************************************************************/
int GetTagLenInfo(const BYTE* pbInBuffer, WORD& wLenLen, long& wConLen)
{
    if (pbInBuffer == NULL)
    {
        return -1;
    }

    wLenLen = 0;
    wConLen = 0;
    BYTE szTemp[5];  
	
    //得到首字节,判断是长长度或短长度
    memset(szTemp, 0, 5);
    memcpy(&szTemp, pbInBuffer, 5);

    if (szTemp[0] & 0x80)					//长长度编码
    {
        wLenLen = (szTemp[0] & 0x7F);		//得到后继长度字节个数

        if( wLenLen==1 )
        	wConLen = szTemp[1];
        else if ( wLenLen==2 )
        	wConLen = szTemp[1]*256 + szTemp[2];
        else if ( wLenLen==3 )
        	wConLen = szTemp[1]*65536 + szTemp[2]*256 + szTemp[3];	
        else if ( wLenLen==3 )
        	wConLen = szTemp[1]*16777216 + szTemp[2]*65536 + szTemp[3]*256 + szTemp[4];	
        	
        wLenLen++;
    }
    else									//短长度编码，单字节
    {
        wLenLen = 1;
        wConLen = (int)szTemp[0];
    }

    return 0;    
}


/******************************************************************************************
函数名    : BitToDecstring
描述      : 比特型转化为10进制的字符串,把一字节从nStartBitPos
			开始，长度为nBitLen的二进制转换为10进制的字符串。
输入参数  : 参数1: BYTE* pbInput	--输入二进制串的指针
			参数2: int nStartBitPos --字节中位的起始位置>=0 && <=7
			参数3: int nBitLen		--bit长度
			参数4: char* pszOutput	--输出字符串指针
			参数5: int nOutputLen	--指定输出长度
输出参数  : char *pszOutput
返回值    : SUCC——转换成功
			FAIL——转换失败
修改历史  : 
  1.日    期: 03-03-28
	作    者: 
	修改内容: 新生成函数
******************************************************************************************/
int BitToDecstring(BYTE *pbInput, 
					   int nStartBitPos, 
					   int nBitLen, 
					   char *pszOutput,  
					   int nOutputLen)
{
    assert(pbInput && pszOutput);
    assert(nStartBitPos >= 0 && nStartBitPos <= 7);
    assert(nStartBitPos + nBitLen <= 8);

    BYTE uTmp;
    uTmp = pbInput[0];
    memset(pszOutput, ' ', nOutputLen);    

    uTmp = uTmp << (8 - nStartBitPos - nBitLen);
    uTmp = uTmp >> (8 - nBitLen);    

    //把整数值转换成相应的字符串
    char sztmp[5];
    sprintf(sztmp, "%d", uTmp);    

    if((int) strlen(sztmp) > nOutputLen)
    {
        return -1;
    }    

    memcpy(pszOutput, sztmp, strlen(sztmp));
    return 0;
}


//时间校正，如果pszOldTime与pszNewTime的秒数差不是nDuration的值，则需要校正
//引起的原因是，夏令时的关系，需要给pszNewTime减1小时
int TimeCheck(const char *pszOldTime,char *pszNewTime,int nDuration)
{
	ZASSERT(pszOldTime);
	ZASSERT(pszNewTime);
    //modify by zengmf 2009-09-03，允许为负，时间自减
	//assert(nDuration >= 0);

    short mdays[2][12]= {
        { 31,28,31,30,31,30,31,31,30,31,30,31 },
        { 31,29,31,30,31,30,31,31,30,31,30,31 } 
	};

	time_t tmOld;	
	time_t tmNew;
	char szTmp[10];
	int nDay = 0;
	int nMonth;
	int nYear;
	int nHour;	
	char szCheckTime[15];
	int nMonArrayIndex = 0;
	int nTmp;

	tmOld = GetTimeFromLongStr(pszOldTime);
	tmNew = GetTimeFromLongStr(pszNewTime);
	
	if(tmNew - tmOld != nDuration)
	{
		//时间不对，进行校验
		memset(szTmp,0,sizeof(szTmp));

		strncpy(szTmp,pszNewTime,4);
		nYear = atoi(szTmp);

		memset(szTmp,0,sizeof(szTmp));
		strncpy(szTmp,pszNewTime+4,2);
		nMonth = atoi(szTmp);

		memset(szTmp,0,sizeof(szTmp));
		strncpy(szTmp,pszNewTime+6,2);
		nDay = atoi(szTmp);

		memset(szTmp,0,sizeof(szTmp));
		strncpy(szTmp,pszNewTime+8,2);
		nHour = atoi(szTmp);

		--nHour;
		if(nHour < 0)
		{
			nHour = 23;
			--nDay;
			if(nDay == 0)
			{
				nMonArrayIndex = (IsLeapYear(nYear) == 1)?1:0;
				nTmp = (nMonth == 1)?12:nMonth-1;
				nDay = mdays[nMonArrayIndex][nTmp-1];
				--nMonth;
				if(nMonth == 0)
				{
					nMonth = 12;
					--nYear;
				}
			}
		}		

		sprintf(szCheckTime,"%04d%02d%02d%02d",nYear,nMonth,nDay,nHour);
		strcat(szCheckTime,pszNewTime+10);
		strcpy(pszNewTime,szCheckTime); 
	}	

	return 0;
}



char *GetUtime(void)
{
#ifndef WIN32
        struct timeval  tv;
        static char             str[30];
        char                    *ptr;

        //if (gettimeofday(&tv, NULL) < 0)
               // printf("gettimeofday error");

        ptr = ctime(&tv.tv_sec);

        strcpy(str, &ptr[11]);
                /* Fri Sep 13 00:00:00 1986\n\0 */
                /* 0123456789012345678901234 5  */
		snprintf(str+8, sizeof(str)-8, ".%06ld", tv.tv_usec);

        return(str);
#else
        ACE_Time_Value  tv;
        static char             str[30];
        char                    *ptr;

        tv = ACE_OS::gettimeofday();
		
		long nSec = tv.sec();
		time_t tSec = (time_t)nSec;
        ptr = ctime(&tSec);
		nSec = (long)tSec;

        strcpy(str, &ptr[11]);
                /* Fri Sep 13 00:00:00 1986\n\0 */
                /* 0123456789012345678901234 5  */
		ACE_OS::snprintf(str+8, sizeof(str)-8, ".%06ld", nSec);

        return(str);
#endif
}

#ifndef WIN32
	void Pthread_mutex_lock(pthread_mutex_t *mptr)
	{
		int n;

		if ( (n = pthread_mutex_lock(mptr)) == 0)
			return;		

		//printf("pthread_mutex_lock error,return=%d \n",n);	
	}


	void Pthread_mutex_unlock(pthread_mutex_t *mptr)
	{
		int	n;

		if ( (n = pthread_mutex_unlock(mptr)) == 0)
			return;

		//printf("pthread_mutex_unlock error,return=%d\n",n);	
	}
#endif


/**************************************/


#ifndef WIN32
	struct timeval   tv_start, tv_stop;
#else
	ACE_Time_Value   tv_start, tv_stop;
#endif
	
void tv_sub(struct timeval *out, struct timeval *in)
{
    if ( (out->tv_usec -= in->tv_usec) < 0) 
	{/* out -= in */
            --out->tv_sec;
            out->tv_usec += 1000000;
    }

   out->tv_sec -= in->tv_sec;
}


int StartTime(void)
{
#ifndef WIN32
    return(gettimeofday(&tv_start, NULL));
#else
	tv_start = ACE_OS::gettimeofday();
	return 0;
#endif
}



double StopTime(void)
{
        double  clockus;
#ifndef WIN32
        if (gettimeofday(&tv_stop, NULL) == -1)
                return(0.0);

        tv_sub(&tv_stop, &tv_start);
        clockus = tv_stop.tv_sec * 1000000.0 + tv_stop.tv_usec;
#else
		return ACE_OS::gettimeofday().usec() - tv_start.usec();
#endif
        return(clockus);
}



time_t secondsBetween(const char* pszStartTime,const char* pszEndTime)
{
	return  GetTimeFromLongStr(pszEndTime) - GetTimeFromLongStr(pszStartTime);
}


char* nsprintf(char *pszString,const char* pszFormat,const long nValue,const int nLen)
{
	char szBuff[15];	

	sprintf(szBuff,pszFormat,nValue);
	strncpy(pszString,szBuff,nLen);	

	return pszString;
}


char* nssprintf(char *pszString,const char* pszFormat,const char* pszValue,const int nLen)
{
	char szBuff[35];

	sprintf(szBuff,pszFormat,pszValue);
	strncpy(pszString,szBuff,nLen);	

	return pszString;
}

bool CompareRegex(const char*a, const char*b)
{
	int z;
	regex_t reg;
	char ebuf[128];

	z = regcomp(&reg, b, REG_EXTENDED | REG_NEWLINE);
	if (z != 0)
	{
		regerror(z, &reg, ebuf, sizeof(ebuf));
		// printf("%s: pattern '%s' \n", ebuf, b);
		return false;
	}
	z = regexec(&reg, a, 0, NULL, 0);
	if (z == REG_NOMATCH)
	{
		regfree(&reg);
		return false;
	}
	else if (z != 0)
	{
		regerror(z, &reg, ebuf, sizeof(ebuf));
		// printf("%s: regcom('%s')\n", ebuf, a);
		regfree(&reg);
		return false;
	}

	regfree(&reg);

	return true;
}


/*数值型数据比较*/
bool  CompareValue(const char *v_a,const char *v_b,int v_Operators)
{
	switch (v_Operators)
	{
	case 10:	//=
		return (atol(v_a) == atol(v_b));
		break;
	case 20:	//!=
		return (atol(v_a) != atol(v_b));
		break;
	case 40:	//>
		return (atol(v_a) > atol(v_b));
		break;
	case 41:	//>=
		return (atol(v_a) >= atol(v_b));
		break;
	case 50:	//<
		return (atol(v_a) < atol(v_b));
		break;
	case 51:	//<=
		return (atol(v_a) <= atol(v_b));
		break;
    case 60: //当前变量值包含在值中， 直接调用CompareABPString函数
    case 61: //当前变量值不包含在值中
        return CompareString(v_a, v_b, v_Operators);
        break;
    case 64://正则表达式
        return CompareRegex(v_a, v_b);
    default:
        return false;
	}
}



char* zsprintf(const char* pszString,const char* pszFormat,const int nLen,char* pszOut)
{
	memset(pszOut,0,nLen+1);
	sprintf(pszOut,pszFormat,pszString);

	return pszOut;	
}


unsigned char getLow4b(unsigned char read_c)
{
        unsigned char _char;
        _char=read_c<<4 ;
        _char=_char>>4;
        if ( _char>=0 && _char<=9)
        {
                _char= _char+48;
        }
        else if (_char>=10 && _char<=16)
        {
                _char= _char+55;
        }

        return _char;
}



unsigned char getHigh4b(unsigned char read_c)
{
        unsigned char _char;
        _char=read_c>>4 ;

        if (_char>=0 && _char<=9)
        {
                _char=_char+48;
        }
        else if (_char>=10 && _char<=16)
        {
                _char=_char+55;
        }

        return _char;
}



void BcdToAsc(unsigned char *bcd_rec, unsigned char *asc_rec, int start_byte, int byte_num)
{
        int i=0,j=0,k=0;

        for (i=0;i<byte_num;i++)
        {
                *(asc_rec+2*i)= getHigh4b(*(bcd_rec+i+start_byte));
                *(asc_rec+2*i+1)=getLow4b(*(bcd_rec+i+start_byte));
        }

        *(asc_rec+2*byte_num)='\0';
}



unsigned char setLow4b(unsigned char read_c)
{
        unsigned char _char;
        
        if (read_c>='0' && read_c<='9')
        {
        	_char=read_c-48;
        }
        else if(read_c>='A' && read_c<='F')
        {
        	_char=read_c-55;
        }
        else if(read_c>='a' && read_c<='f')
        {
        	_char=read_c-87;
        }
        
        return _char;
}



unsigned char setHigh4b(unsigned char read_c)
{
        unsigned char _char;

        if (read_c>='0' && read_c<='9')
        {
        	_char=read_c-48;
        }
        else if(read_c>='A' && read_c<='F')
        {
        	_char=read_c-65;
        }
        else if(read_c>='a' && read_c<='f')
        {
        	_char=read_c-97;
        }

        _char=_char<<4;

        return _char;
}



int AscToBcd(unsigned char *asc_rec, unsigned char *bcd_rec, int start_byte, int byte_num)
{
        int i=0,j=0,k=0;

        if(byte_num%2 > 0)
        {
        	return -1;
        }

        for (i=0;i<byte_num;i++)
        {
                *(bcd_rec+i)= setHigh4b(*(asc_rec+2*i+start_byte));
                *(bcd_rec+i)= *(bcd_rec+i) + setLow4b(*(asc_rec+2*i+1+start_byte));
        }

        *(bcd_rec+byte_num/2)='\0';

        return 1;
}



/*16进制数据按字节输出*/
void output_16(void *ptr,int length)
{
    char *p = (char*)ptr;
    int i=0;
    for(i=0;i<length;i++) 
	{
       // printf("%02X ",p[i]);
    }

   // printf("\n");

    for(i=0;i<length;i++) 
	{
        //if(p[i]>=0x20 && p[i]<0x80) 
			//printf("%c  ",p[i]);
        //else 
			//printf("?? ");
    }

}



int zatoi(const char* pszValue,int nLen)
{
	char szBuff[20];

	memset(szBuff,0,20);
	strncpy(szBuff,pszValue,nLen);

	return atoi(szBuff);
}


long zatol(const char* pszValue,int nLen)
{
	char szBuff[20];	

	memset(szBuff,0,20);
	strncpy(szBuff,pszValue,nLen);	

	return atol(szBuff);	
}


/**
	解析[1-9]或[1,3,4]字符串
**/
int JudgeExpress(char cValue,const char* pszExpress)
{
	char szValue[10],szExpress[31],*p;
	list<int> ltValue;
	int i,nValue,nStartValue,nEndValue ;

	memset(szExpress,0,31);
	strncpy(szExpress,pszExpress,30);

	memset(szValue,0,10);
	szValue[0] = cValue;
	nValue = atoi(szValue);
	if((p = strchr(szExpress,'[')))
		*p = ' ';
	if((p = strchr(szExpress,']')))
		*p = 0;

	szValue[1] = 0;
	if((p = strchr(szExpress,'-')))
	{
		szValue[0] = *(p - 1);
		nStartValue = atoi(szValue);
		szValue[0] = *(p + 1);
		nEndValue = atoi(szValue);
		for(i = nStartValue;i <= nEndValue;i++)
			ltValue.push_back(i);
	}
	else
	{
		SplitStrInt(szExpress,',',ltValue);
	}	

	if(ltValue.end() != find(ltValue.begin(),ltValue.end(),nValue))
		return 0;	

	return -1;
}


void GetBeforeMonth(const char* Now, char* Before)
{
	char year[5], month[3];	

	strncpy(year, Now, 4);
	year[4] = 0;	

	strncpy(month, Now+4, 2);
	month[2] = 0;	

	int nYear = atoi(year);
	int nMonth = atoi(month);

	if(nMonth == 1)
	{
		sprintf(Before, "%0.4d12", nYear-1);
	}
	else
	{
		sprintf(Before, "%0.4d%0.2d", nYear, nMonth-1);	
	}	
}


void ChangeMonth(char* month, int offset)
{	
	month[6] = 0;	

	int nYear = (month[0]-'0')*1000 + (month[1]-'0')*100 + (month[2]-'0')*10 + month[3]-'0';
	int nMonth = (month[4]-'0')*10 + month[5]-'0';
	
	if(offset < 0)
	{
		offset = -offset;
		
		int nLeftYear = offset/12;
		int nLeftMonth = offset%12;
		
		nYear = nYear - nLeftYear;
		nMonth = nMonth - nLeftMonth;

		if(nMonth <= 0)
		{
			nMonth += 12;
			nYear--;	
		}		

		sprintf(month, "%4d%02d", nYear, nMonth);
	}
    else if(offset > 0)
    {
        int nLeftYear = offset/12;
        int nLeftMonth = offset%12;

        nYear = nYear + nLeftYear;
        nMonth = nMonth +nLeftMonth;

        if (nMonth > 12)
        {
            nMonth -=  12;
            ++nYear;
        }

        sprintf(month,"%4d%02d",nYear,nMonth);
    }
    return;
}



void ChangeDay(char* day, int offset)
{
	day[8] = 0;
	
	time_t t = GetTimeFromShortStr(day);
	t = t + 24*3600*offset;
	GetDateTime(day, t);
	day[8] = 0;
}

int CheckDay(char* szBeginDay,char* szEndDay)
{
    szBeginDay[8] = 0;
    szEndDay[8] = 0;

    time_t tBegin = GetTimeFromShortStr(szBeginDay);

    time_t tEnd = GetTimeFromShortStr(szEndDay);

    return abs((tEnd - tBegin)/(24*3600)) ;

}

char * strtoupper(const char *v_sSrc,char *v_sDes)
{
	size_t len = strlen(v_sSrc);
	char c = 'a'-'A';
	for(size_t i = 0;i <= len; ++i)
	{
		if(v_sSrc[i] >= 'a' && v_sSrc[i] <= 'z')
		{
			v_sDes[i] = v_sSrc[i] - c;
		}
		else
		{
			v_sDes[i] = v_sSrc[i];
		}
	}
	return v_sDes;
}

char * strtolower(const char *v_sSrc,char *v_sDes)
{
	size_t len = strlen(v_sSrc);
	char c = 'a'-'A';
	for(size_t i = 0;i <= len; ++i)
	{
		if(v_sSrc[i] >= 'A' && v_sSrc[i] <= 'Z')
		{
			v_sDes[i] = v_sSrc[i] + c;
		}
		else
		{
			v_sDes[i] = v_sSrc[i];
		}
	}
	return v_sDes;
}
string int_to_string(int nIN)   
{
    string strOut;
    char szOut[32] = {0};
	sprintf(szOut,"%d",nIN);
	strOut = szOut;
	return strOut;
}

string long_to_string(long lnIN)   
{
    string strOut;
    char szOut[32] = {0};
	sprintf(szOut,"%ld",lnIN);
	strOut = szOut;
	return strOut;
}

//操作符 in 判断
bool InCompare(const char* vi_pszLeft, const char* vi_pszRight)
{
	char  m_szTmpLeft[1024]={0};
	char  m_szTmpRight[1024]={0};
	if(strlen(vi_pszLeft)<=0)
	{
		if(strlen(vi_pszRight)<=0)
		{
			return true;
		}
		return false;
	}

	//首先判断分隔符类型
	char sp = ',';
	int c = 0;
	while('\0' != *(vi_pszRight+c) )
	{
		if(','==*(vi_pszRight+c))
		{
			sp = ',';
			break;
		}	
		else if('|'==*(vi_pszRight+c))
		{
			sp = '|';
			break;
		}
		else if(';'==*(vi_pszRight+c))
		{
			sp = ';';
			break;
		}
		else if('#'==*(vi_pszRight+c))
		{
			sp = '#';
			break;
		}
		else if('@'==*(vi_pszRight+c))
		{
			sp = '@';
			break;
		}		
		++c;
	}

	//构造 ",A," 格式
	if (vi_pszLeft[0] != sp)
	{
		sprintf(m_szTmpLeft,"%c%s",sp,vi_pszLeft);
	}
	else
	{
		strcpy(m_szTmpLeft,vi_pszLeft);
	}

	int nLen = strlen(m_szTmpLeft);
	if (m_szTmpLeft[nLen-1] != sp)
	{
		m_szTmpLeft[nLen] = sp;
		m_szTmpLeft[nLen+1] = '\0';
	}

	//构造 ",B1,B2,B3," 格式
	if (vi_pszRight[0] != sp)
	{
		sprintf(m_szTmpRight,"%c%s",sp,vi_pszRight);
	}
	else
	{
		strcpy(m_szTmpRight,vi_pszRight);
	}

	nLen = strlen(m_szTmpRight);
	if (m_szTmpRight[nLen-1] != sp)
	{
		m_szTmpRight[nLen] = sp;
		m_szTmpRight[nLen+1] = '\0';
	}

	//如果A在B中，判断
	char* pstr = strstr(m_szTmpRight, m_szTmpLeft);
	if (NULL != pstr)
	{
		return true; 
	}
	return false;
}

char * strreplace(const char *v_sSrc,char *v_sDes,char *v_sRep,size_t v_pos,size_t v_len)
{
	size_t len = strlen(v_sRep);
	memcpy(v_sDes,v_sSrc,v_pos);
	memcpy(v_sDes+v_pos,v_sRep,len);
	strcpy(v_sDes+v_pos+len,v_sSrc+v_pos+v_len);
	return v_sDes;
}
bool HeadStrjudge(const char * pHeadStr,const char * pOriStr)
{
    if (pHeadStr == NULL || pOriStr == NULL)
    {
        return false;
    }
    if (strlen(pHeadStr) > strlen(pOriStr))
    {
        return false;
    }
    for (int i= 0;i<strlen(pHeadStr);i++)
    {
        if (pHeadStr[i] != pOriStr[i])
        {
            return false;
        }
    }
    return true;
}
bool TailStrjudge(const char * pTailStr,const char * pOriStr)
{
    if (pTailStr == NULL || pOriStr == NULL)
    {
        return false;
    }
    if (strlen(pTailStr) > strlen(pOriStr))
    {
        return false;
    }
    int i=0,j=0;
    for (i= strlen(pOriStr)-1,j=strlen(pTailStr)-1; j>= 0; i--,j--)
    {
        if (pTailStr[j] != pOriStr[i])
        {
            return false;
        }
    }
    return true;
}

bool HeadTailStrListJudge(const char * pTailListStr,const char * pOriStr,const int nType)
{
    if (pTailListStr == NULL || pOriStr == NULL)
    {
        return false;
    }
    list<string> TempList;
    list<string>::iterator iter;

    SplitStr(pTailListStr,',',TempList);
    bool nRec = true;
    for (iter = TempList.begin();iter !=TempList.end(); ++iter)
    {
        nRec = false;
        if (nType == 1) //头
        {
            if (HeadStrjudge(iter->c_str(),pOriStr))
            {
                return true;
            }
        }
        else if (nType == 2)//尾
        {
            if (TailStrjudge(iter->c_str(),pOriStr))
            {
                return true;
            }

        }
    }
    return nRec;
}
//int SplitStr(const char *pszStr,const char cSplit,list<string>& ltStr)

bool CompareString(const char *v_a, const char *v_b,int v_Operator)
{
	char	tmp_b[1024];
	char    tmp_a[1024];
	char	szExpress[64];
	char *psr;
    bool bFind = false;
    char *pend;
	list<string> ltStr;
	list<string>::iterator ltStrIt;
    vector<string> vec_aStr;
    vector<string> vec_bStr;

	switch (v_Operator)
	{
		case 0:  	//nop 空操作符
			return true;
		case 10: //=
			return (strcmp(v_a,v_b)==0);
		case 20: //!=
		case 94:	
			return (strcmp(v_a,v_b)!=0);
		case 30: //like
			szExpress[0] = 0;
			if((psr = strchr((char *)v_b,'[')))		
			{
				strcpy(szExpress,psr );
		//		*psr = 0;
			}
			if(strncmp(v_a,v_b,strlen(v_b))==0)
			{
				tmp_a[0] = *(v_a + strlen(v_b));
				if((0 != tmp_a[0]) && (psr = strchr(szExpress,'[')))
				{
					//printf("va=[%s],value=[%c],express=[%s]\n",v_a,tmp_a[0],szExpress);
					if(PublicLib::JudgeExpress(tmp_a[0],szExpress) >= 0)
					{
						return true;
					}
				}else
					return true;
				
				return false;
			}
			else
				return false;
		case 31:	//不相似,not like
				if(!strncmp(v_a,v_b,strlen(v_b)))	//相似
			{
				return false;
			}
			else	//不相似
			{
				return true;
			}
        case 35: //like 类似包含在串中 如abc 35 bcabcd = true
            if(NULL == strstr(v_a, v_b))
			{
				return false;
			}
			else	//不相似
			{
				return true;
			}
		case 40: //>
			return strcmp(v_a,v_b) > 0;
//			return ( atol(v_a)>atol(v_b) );			
		case 41: //>=		
			return ( atol(v_a) >= atol(v_b) );
		case 50: //<		
			return strcmp(v_a,v_b) < 0;
//			return ( atol(v_a)<atol(v_b) );
		case 51: //<=		
			return ( atol(v_a) <= atol(v_b) );
		case 60: //当前变量值包含在值中
			if(v_b[strlen(v_b)-1]!=',')
				sprintf(tmp_b,"%s,\0",v_b);
			else
				strcpy(tmp_b,v_b);
				
			if( 0 == strlen(v_a) )
				return false;
			
			sprintf(tmp_a,"%s,",v_a);
				
				//printf("b = [%s],a = [%s]\n",tmp_b,tmp_a);
			psr=strstr(tmp_b,tmp_a);
			if(psr==NULL)
			{
				return false;
			}
			else
			{
				if(0 != (psr - tmp_b))
				{
					sprintf(tmp_a,",%s,",v_a);
					psr=strstr(tmp_b,tmp_a);
					if(NULL == psr)
					{
						return false;
					}
					else
					{
						return true;
					}
				}
				return true;
			}
			break;	
		case 61: //当前变量值不包含在值中
			if(v_b[strlen(v_b)-1]!=',')
				sprintf(tmp_b,"%s,\0",v_b);
			else
				strcpy(tmp_b,v_b);
				
			if( 0 == strlen(v_a) )
				return true;
			
			sprintf(tmp_a,"%s,",v_a);
				
			psr=strstr(tmp_b,tmp_a);
			if(psr==NULL)
				return true;
			else
            {
                if(0 != (psr - tmp_b))
                {
                    sprintf(tmp_a, ",%s,", v_a);
                    psr = strstr(tmp_b, tmp_a);
                    if(NULL == psr)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
                return false;
            }
			break;

		case 62:	//包含相似
			psr = (char*)v_b;
            while(psr != NULL)
            {
                //寻找下一个分隔符位置
                pend = strstr(psr, ",");
                if(pend != NULL)
                {
                    if (!strncmp(v_a, psr, pend - psr))	//相似
                    {
                        return true;
                    }
                    psr = pend + 1;
                }
                else //如果没有分隔符，比较一次就结束
                {
                    if (!strncmp(v_a, psr, strlen(psr)))	//相似
                    {
                        return true;
                    }
                    return false;
                }
            }
			return false;

		case 63:	//不包含相似
			psr = (char*)v_b;
            while(psr != NULL)
            {
                //寻找下一个分隔符位置
                pend = strstr(psr, ",");
                if(pend != NULL)
                {
                    if (!strncmp(v_a, psr, pend - psr))	//相似
                    {
                        return false;
                    }
                    psr = pend + 1;
                }
                else //如果没有分隔符，比较一次就结束
                {
                    if (!strncmp(v_a, psr, strlen(psr)))	//相似
                    {
                        return false;
                    }
                    return true;
                }
            }
			return true;
        case 64://正则表达式，ABP类型
            return CompareRegex(v_a, v_b);
        case 65://case 70://当前值包含右值
            if(v_a[strlen(v_a) - 1] != ',')
                sprintf(tmp_a, "%s,", v_a);
            else
                strcpy(tmp_a, v_a);

            if( 0 == strlen(v_b) )
                return false;

            sprintf(tmp_b, "%s,", v_b);

            psr = strstr(tmp_a, tmp_b);
            if(psr == NULL)
            {
                return false;
            }
            else
            {
                if(0 != (psr - tmp_a))
                {
                    sprintf(tmp_b, ",%s,", v_b);
                    psr = strstr(tmp_a, tmp_b);
                    if(NULL == psr)
                    {
                        return false;
                    }
                    else
                    {
                        return true;
                    }
                }
                return true;
            }
            break;
        case 66: // 存在交集，A、B都是以逗号分隔的字符串，例子: A={0,1,2}, B={2,3,4}, 则返回true
            if(0 == strlen(v_a) || 0 == strlen(v_b))
            {
                return false;
            }
            SplitStr(v_a, ',', vec_aStr);
            SplitStr(v_b, ',', vec_bStr);
            for(int i = 0; i < vec_aStr.size(); i++)
            {
                for(int j =0; j < vec_bStr.size(); j++)
                {
                    if( vec_aStr[i] == vec_bStr[j])
                    {
                        return true;
                    }
                }
            }
            return false;
            break;
        case 67: // 不存在交集，A、B都是以逗号分隔的字符串，例子: A={0,1,2}, B={3,4}, 则返回true
            if(0 == strlen(v_a) || 0 == strlen(v_b))
            {
                return true;
            }
            SplitStr(v_a, ',', vec_aStr);
            SplitStr(v_b, ',', vec_bStr);
            for(int i = 0; i < vec_aStr.size(); i++)
            {
                for(int j =0; j < vec_bStr.size(); j++)
                {
                    if( vec_aStr[i] == vec_bStr[j])
                    {
                        return false;
                    }
                }
            }
            return true;
            break;
        case 68:// A包含于B, A集合包含于B集合，例子：A[1,2],B[1,5,2,8]
            if(0 == strlen(v_a))
                return true;	
            SplitStr(v_a, ',', vec_aStr);
            SplitStr(v_b, ',', vec_bStr);
            for(int i = 0; i < vec_aStr.size(); i++)
            {
                bFind = false;
                for(int j =0; j < vec_bStr.size(); j++)
                {
                    if( vec_aStr[i] == vec_bStr[j])
                    {
                        bFind =true;
                        break;
                    }
                }
            }
            return bFind;
            break;
        case 69:// A包含B, A集合包含B集合，例子：A[1,5,2,8],B[1,2]
            if(0 == strlen(v_b))
                return true;	
            SplitStr(v_a, ',', vec_aStr);
            SplitStr(v_b, ',', vec_bStr);
            for(int i = 0; i < vec_bStr.size(); i++)
            {
                bFind = false;
                for(int j =0; j < vec_aStr.size(); j++)
                {
                    if( vec_bStr[i] == vec_aStr[j])
                    {
                        bFind =true;
                        break;
                    }
                }
            }
            return bFind;
            break;
        case 80:	//头部包含
            return HeadStrjudge(v_b,v_a);
			break;
        case 81:	//头部包含
            return HeadTailStrListJudge(v_b,v_a,1);
            break;
        case 82:	//尾部包含
            return TailStrjudge(v_b,v_a);
            break;
        case 83:	//尾部包含
            return HeadTailStrListJudge(v_b,v_a,2);
            break;

		case 91://时间非法
			if( !isTime(v_a) )
				tmp_a[0]='1';
			else
				tmp_a[0]='0';	
				
			if(	tmp_a[0]==v_b[0])
				return true;
			else
				return false;
			
			break;	
		case 92://非法数字
			if( !isStringCode(v_a,ALL_DIGITAL) )
				tmp_a[0]='1';
			else
				tmp_a[0]='0';	
				
			if(	tmp_a[0]==v_b[0])
				return true;
			else
				return false;
			
			break;	
			
		case 93://非法IP
		    if( !isIpAddress(v_a)) tmp_a[0]='1';
		    else tmp_a[0]='0';
		    if(tmp_a[0]==v_b[0]) return true;
		    else return false;
		case 99://add by qinlei,参数A为SDT时间，参数B为允许的时间差，系统时间减去开始时间，得到的时间差若大于参数B
            //则满足，2011/5/26
        {
            char szCurrDate[16] = {0};
            getCurrentDate(szCurrDate);
            long lInterval = secondsBetween(v_a, szCurrDate); //后者减前者，系统时间减取开始时间
            long lv_b = atol(v_b);
            if (lInterval >= lv_b)     //系统时间减去开始时间大于配置的时间差（单位为秒）
            {
                return true;
            }
            return false;
        }				
		default:
			return false;		
	}
}

bool CompareNew(const char *v_a,const char *v_b,int v_Operator,char v_type/*='C'*/)
{
    //字符型比较，默认比较类型
    if (v_type == 'C')
    {
        return CompareString(v_a, v_b, v_Operator);
    }
    else
    {
        return CompareValue(v_a, v_b, v_Operator);
    }
}

/*
 ** 功能描述: 对输入的原始串,用解密密钥进行解密;解密算法固定为DES算法
 ** 返回值: true: 解密成功; false: 解密失败
 ** 输入参数: 
      strSrcBuffer: 解密原始串,格式如:用户名/密文/数据库服务名
      strDecryptKey: 解密密钥
      nDecryptFlag: 0: 不参与解密; 1: 数据库连接密码部分解密; 2: 数据库连接用户名,密码解密; 
                           3: 整个配置项串解密; 暂时只实现为1的情况
  ** 输出参数: 
      strDecryptBUffer: 解密后的值,格式如:用户名/明文/数据库服务名
*/
bool Decrypt_Des(char* szDecryptBUffer, const char* szSrcBuffer, const char* szDecryptKey, int nDecryptFlag)
{
    //
    /*switch(nDecryptFlag)
    {
        case 1:
	     {
                //拆分原始串
                char szUser[64] = {0};
		  char szPassword[64] = {0};
		  char szServiceName[64] = {0};
		  SplitConnStr(szSrcBuffer, szUser, szPassword, szServiceName);

                //解密
                CEncryptCpt  CencryptCpt;

		  char szDecryptPassword[64] = {0};
                CencryptCpt.strDesDecrpt(szPassword, szDecryptKey, szDecryptPassword);

		  //printf("password new[%s]len[%d],old[%s]\n", szDecryptPassword,strlen(szDecryptPassword), szPassword);
		  
                //组装原始串
                sprintf(szDecryptBUffer, "%s/%s@%s", szUser, szDecryptPassword, szServiceName);
	     }
	  case 0:
	  case 2:
	  case 3:
	  default:
	      break;
    }*/
	
    return true;
}

void SplitConnStr(const char* pszConnStr, char*  pszUser, char* pszPassword, char* pszServiceName)
{
    char strConf[256]      = {0};
    strcpy(strConf,pszConnStr);

    int  i = 0;
    int nLength = sizeof(strConf);   
    for (i=0; i<nLength; i++)
    {
        if (strConf[i] == '/')
        {
            strcpy(strConf, strConf+i+1);
            pszUser[i] = '\0';
            break;
        }
        pszUser[i] = strConf[i];
    }

    for (i=0; i<nLength; i++)
    {
        if (strConf[i] == '@')
        {
            strcpy(strConf, strConf+i+1);
            pszPassword[i] = '\0';
            break;
        }
        pszPassword[i] = strConf[i];
    }
    strcpy(pszServiceName, strConf);
	
}

bool OCS_readConfig(string& strValue,const char* szpName,const char* szpSectionPath, int nDecryptFlag)
{
    //如果是从数据库读取配置文件,那么调用
    //DCParamCfg::OCS_readConfig
    //printf("read config state:%d\n", DCParamCfg::GetInstance()->GetReadConfigState());
    if (DCParamCfg::GetInstance()->GetReadConfigState() == 2)
    {
        return DCParamCfg::GetInstance()->OCS_readConfig(strValue, szpName, szpSectionPath);
    }

    char* szPFileName = getenv("OCSConfig");
    if(szPFileName==NULL || szpName == NULL || szpSectionPath == NULL)
    {
    	return false;
    }

    ConfigureFile* ptmpCfgFile = new ConfigureFile();
    ptmpCfgFile->initialization(szPFileName);

    if(strlen(szpSectionPath) >= (MAX_PATH-1))
    {
        delete ptmpCfgFile;
        ptmpCfgFile = NULL;
        return false;
    }
    	
    char szBuff[MAX_PATH]    = {0};
    sprintf(szBuff,"\\%s",szpSectionPath);

    string strSectionPath = szBuff;
    string strName        = szpName;

    bool bRet = ptmpCfgFile->getValue(strSectionPath,strName,strValue);
    
    if (bRet)
    {
        //如果路径名称中有'\'符号,统一转换成'/'
        char szNewSectionPath[128] = {0};
        DCParamCfg::GetInstance()->AlterPathFormat(const_cast<char*>(szpSectionPath), szNewSectionPath);

        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "Read %s/%s: [%s]",szNewSectionPath,szpName,strValue.c_str());

    }
    delete ptmpCfgFile;
    ptmpCfgFile = NULL;
    
    //在读取配置成功的前提下,如果输入参数nDecryptFlag要求解密
    //则调用解密函数对读取的串进行解密
    bool bResult = bRet;    //如果不需要解密,返回的是读取配置结果;需要解密,返回的是解密的结果
    if ((nDecryptFlag > 0) && bRet)
    {
        //读取配置文件中"system/DecryptKey"的解密密钥
        string strDecryptKey;
        bResult = OCS_readConfig(strDecryptKey, "DecryptKey", "system");
			
        //读取密钥成功,执行解密
        if (bResult)
        {
            char szDecryptBuffer[256] = {0};
			
	     //最终输出szDecryptBuffer,原始串strValue,加密密钥strDecryptKey
            bResult = Decrypt_Des(szDecryptBuffer, strValue.c_str(), strDecryptKey.c_str(), nDecryptFlag);
            strValue = szDecryptBuffer;    //输出解密后的串如果失败,则串为空,同时返回错误
        }
	 else
	 {
	     //读取解密密钥失败,按原始明文输出
	     //printf("read config system/DecryptKey failed.");
	     return bRet;
	 }
    }
	
    return bRet ;

}

bool OCS_readConfig(string& strValue,const char* szpName,const char* szpSubSectionPath,const char* szpSectionPath, int nDecryptFlag)
{
    //printf("read config state:%d", DCParamCfg::GetInstance()->GetReadConfigState());
	
    //如果是从数据库读取配置文件,那么调用
    //DCParamCfg::OCS_readConfig
    if (DCParamCfg::GetInstance()->GetReadConfigState() == 2)
    {
        return DCParamCfg::GetInstance()->OCS_readConfig(strValue, szpName, szpSubSectionPath, szpSectionPath);
    }

    char* szPFileName = getenv("OCSConfig");
    if(szPFileName==NULL || szpName == NULL || szpSubSectionPath == NULL || szpSectionPath == NULL)
    {
        return false;
    }
    
    ConfigureFile* ptmpCfgFile = new ConfigureFile();
    ptmpCfgFile->initialization(szPFileName);

    if((strlen(szpSectionPath) + strlen(szpSubSectionPath)) >= (MAX_PATH-2))
    {
        delete ptmpCfgFile;
        ptmpCfgFile = NULL;
        return false;
    }
        
    char szBuff[MAX_PATH] = {0};
    sprintf(szBuff,"\\%s\\%s",szpSectionPath,szpSubSectionPath);

    string strSectionPath = szBuff;
    string strName        = szpName;

    bool bRet = ptmpCfgFile->getValue(strSectionPath,strName,strValue);
    if (bRet)
    {
        //如果路径名称中有'\'符号,统一转换成'/'
        char szNewSectionPath[128] = {0};
        DCParamCfg::GetInstance()->AlterPathFormat(const_cast<char*>(szpSectionPath), szNewSectionPath);

        char szNewSubSectionPath[128] = {0};
        DCParamCfg::GetInstance()->AlterPathFormat(const_cast<char*>(szpSubSectionPath), szNewSubSectionPath);

		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "Read %s/%s/%s: [%s]",szNewSectionPath,szNewSubSectionPath,szpName,strValue.c_str());
    }

    delete ptmpCfgFile;
    ptmpCfgFile = NULL;
    
   
    return bRet;

}

void GetHostAddrIp(char *pszAddrIp)
{
	hostent *p;
    char szHostName[50];
    if(-1 == ACE_OS::hostname(szHostName,sizeof(szHostName)))
    {
        return;
    }
	
    if(NULL == (p = ACE_OS::gethostbyname(szHostName)))
    {
        return;
    }
	
    sockaddr_in addr_in ;
    addr_in.sin_addr =  *(in_addr *)p->h_addr_list[0];
	
    strcpy(pszAddrIp,inet_ntoa(addr_in.sin_addr));
}

long DC_HTON8Byte(long lOrg)
{
    int nTmp = 10;	
	
	if (((char*)&nTmp)[0] != 10)
	{
		return lOrg;
	}
	else
	{
		return DC_SWAP_DDWORD(lOrg);
	}
}

long DC_NTOH8Byte(long lOrg)
{
	int nTmp = 10;	
	
	if (((char*)&nTmp)[0] != 10)
	{
		return lOrg;
	}
	else
	{
		return DC_SWAP_DDWORD(lOrg);
	}
}

char * FindSubStr(char* pszOrg,char* szpTarget)
{
    if (pszOrg == NULL || szpTarget == NULL)
    {
        return NULL;
    }

    int nOrgLen    = strlen(pszOrg);
    int nTargetLen = strlen(szpTarget);

    for (int i = 0; i <= nOrgLen - nTargetLen; i++)
    {
        if (!strncmp(pszOrg+i,szpTarget,nTargetLen))
        {
            return pszOrg+i;
        }
    }

    return NULL;
}

void PrintHex(const char* pszDest,int nLen,char* pszResult)
{
    if (pszDest == NULL || pszResult == NULL)
    {
        return;
    }

    
    for (int i = 0; i < nLen; i++)
    {
        int nTmp = *(pszDest + i);
        sprintf(pszResult + strlen(pszResult),"%02X ",nTmp&0XFF);
    }
}

#ifdef WIN32
    __int64 GetInt64FromString(const char* pszString)
    {
        __int64 n64Result = 0;
        if (strlen(pszString) <= 19)
        {
            int i = 0;         
            while ((pszString[i] != 0) && (pszString[i] >= '0' && pszString[i] <= '9'))
            {
                n64Result = n64Result*10 + pszString[i] - '0';
				i++;
            }          
        }

        return n64Result;
    }
#endif

	void Reverse(char* pszData,int nLen)
	{
		if (nLen <= 1)
		{
			return ;
		}

		char* pszBuff = new char[nLen];
		memcpy(pszBuff,pszData,nLen);
		for (int i = 0; i < nLen; i++)
		{
			pszData[i] = pszBuff[nLen-1-i];
		}
		delete []pszBuff;
	}

    //add by zengmf 20080523
    //获取当前时间的微秒值
    long getTimeUsec()
    {
        ACE_Time_Value tv(ACE_OS::gettimeofday());
        return (tv.sec()*1000000+tv.usec());
    }

    //add by zhangpeng 2010-07-08
    //获取当前时间的秒数
    long GetSecTime()
    {
        time_t ltime;
        ltime = time(NULL);
        return ltime;
    }

    int SetSecTimeToDate(long lnSecTime, char* pszDate)
    {
        struct tm *local = NULL;

        local=localtime(&lnSecTime);
        if (local == NULL)
        {
	     return -1;
        }

        //格式化为:年月日时分秒
        sprintf(pszDate, "%0.4d%0.2d%0.2d%0.2d%0.2d%0.2d",
                  1900+local->tm_year, local->tm_mon+1, local->tm_mday,
                  local->tm_hour, local->tm_min, local->tm_sec); 
        return 0;
    }

    //add by zhangpeng 2010-07-08
    //把年月日时分秒的标准格式时间(如:20100707162459)转换成整型值
    long dateToSec(const char* pszDate)
    {
        char szTmp[5]={0};
        struct tm tm1;

        time_t ltime;

        strncpy(szTmp,pszDate,4);
        szTmp[4] = 0;
        tm1.tm_year=atoi(szTmp)-1900;

        memset(szTmp,0x00,sizeof(szTmp));

        strncpy(szTmp,pszDate+4,2);
        tm1.tm_mon=atoi(szTmp)-1;

        strncpy(szTmp,pszDate+6,2);
        tm1.tm_mday=atoi(szTmp);

        strncpy(szTmp,pszDate+8,2);
        tm1.tm_hour=atoi(szTmp);

        strncpy(szTmp,pszDate+10,2);
        tm1.tm_min=atoi(szTmp);

        strncpy(szTmp,pszDate+12,2);
        tm1.tm_sec=atoi(szTmp);

        ltime=mktime(&tm1);
	
        return ltime;
    }

    /****************************************************************************************
    *@input				pszTime:			当前日期
    *@input				pszBegin:			开始日期
    *@output			pszEnd:				结束日期
    *@return            如果pszTime>=pszBegin && pszTime<=pszEnd，返回true，否则返回false
    *@description       查询账本是否还有效
    *@frequency of call 
    ******************************************************************************************/
    bool CheckEffDate(const char* pszTime, const char* pszBegin, const char* pszEnd)
    {
            //DC_DEBUG((MY_DEBUG_03 ACE_TEXT("current time=%s  BeginEffDate=%s  EndEffDate=%s"), pszTime, pszBegin, pszEnd));
    	
    	if(	(strncmp(pszBegin, pszTime, strlen(pszBegin))<=0) && 
    		(strncmp(pszEnd, pszTime, strlen(pszEnd))>=0) )
    		return true;
    	else
    		return false;
    }
    
    //获取某年某月的最后一天
    int getMaxDay(const int nYear, const int nMonth)
    {
        if(nMonth <1 || nMonth > 12) return -1;
        if(nYear < 1000 || nYear > 9999 ) return -1;
        char maxday[12] = {31,28,31,30,31,30,31,31,30,31,30,31};
        if(nMonth != 2) 
        {
            return maxday[ nMonth -1 ];
        }
        else
        {
            return IsLeapYear(nYear) == 0 ? 29:28;
        }
    }

    //将日期转换为星期,注意星期天用0标识，需要和中国使用习惯区分开
    int dateToWeek(const char* pszDate,int& nWeek)
    {
        char szTmp[5]={0};
        struct tm tm1,*tm2;

        time_t ltime;

        strncpy(szTmp,pszDate,4);
        szTmp[4] = 0;
        tm1.tm_year=atoi(szTmp)-1900;

        memset(szTmp,0x00,sizeof(szTmp));

        strncpy(szTmp,pszDate+4,2);
        tm1.tm_mon=atoi(szTmp)-1;

        strncpy(szTmp,pszDate+6,2);
        tm1.tm_mday=atoi(szTmp);

        strncpy(szTmp,pszDate+8,2);
        tm1.tm_hour=atoi(szTmp);

        strncpy(szTmp,pszDate+10,2);
        tm1.tm_min=atoi(szTmp);

        strncpy(szTmp,pszDate+12,2);
        tm1.tm_sec=atoi(szTmp);

        ltime=mktime(&tm1);
        tm2=localtime(&ltime);

        nWeek=tm2->tm_wday;
        return nWeek;
    }

    //add by zhangpeng -at 2010-11-16-圆整方式
    long GetInt(const double &v_f, const char &v_Mode)
    {
        long lValues = v_f; 
        if ('1' == v_Mode) //向下圆整
        {
            return lValues;
        }   
        else if ('2' == v_Mode) // 向上圆整
        {
            if (0.000000 < (v_f - lValues))
                lValues++;
        }
        else if ('3' == v_Mode) // 四舍五入
        {
            if (0.5 <= (v_f - lValues))
                lValues++;
        }

        return lValues;
    }

	long Div(long f, long v, char mode)
	{
		long count = f / v;
		long tail = f - count * v;
		if(mode == '2' && tail > 0)		// 向上圆整
			count++;
		else if(mode == '3' && 2*tail >= v)	// 四舍五入	
			count++;
		return count;
	}

    string& TrimString( string& str, char sp/*=' '*/ )
    {
        int n = 0;
        while(str[n] == sp){ ++n; }
        str.erase(0, n);

        n = str.length();
        while( n > 0 && str[n-1] == sp )
        {
            --n;
        }
        if( n < str.length() )
        {
            str.erase(n);
        }
        return str;
    }

    int SplitParamList( const string& strValue, std::set<string>& setParams, char sep /*= ','*/ )
    {
        if( strValue.empty() )
        {
            return 0;
        }

        string vlist = strValue;
        vlist.append(1, sep);

        size_t p = 0, s;
        string item;
        while( string::npos != (s=vlist.find_first_of(sep, p)) )
        {
            if( PublicLib::TrimString(item.assign(vlist, p, s-p)).length() )
            {
                setParams.insert(item);
            }
            p = s+1;
        }
        return 0;
    }

    int SplitParamList( const string& strValue, vector<string>& vParams, char sep /*= ','*/ )
    {
        if( strValue.empty() )
        {
            return 0;
        }

        string vlist = strValue;
        vlist.append(1, sep);

        size_t p = 0, s;
        string item;
        while( string::npos != (s=vlist.find_first_of(sep, p)) )
        {
            // no empty validation
            PublicLib::TrimString(item.assign(vlist, p, s-p));
            vParams.push_back(item);
            p = s+1;
        }
        return 0;
    }

    int StringCompareEx( const char* src, const char* dst, int sep /*= '|'*/, int wc /*= '*'*/ )
    {
        for(; *src && *dst; ++src, ++dst)
        {
            if( *src == wc || *dst == wc )
            {
                /*
                *   Either src or dst contains a wildcard character at current
                * position, jump to next section
                */
                while( *src && *src != sep ){ ++src; }
                while( *dst && *dst != sep ){ ++dst; }
            }

            if( *src != *dst ){ break; }
        }

        if( (*src == wc && *(src+1) == 0) || (*dst == wc && *(dst+1) == 0) )
        {
            /*
            *   Either src or dst contains a wildcard character at the end of string
            *   Okay, they are considered equal
            */
            return 0;
        }

        /*
        *   returns the diff
        */
        return *src - *dst;
    }

    int StringToFraction( const char* a, int& num, int& den, int s /*= '/'*/ )
    {
        num = atoi(a);

        const char* p;
        for(p = a; *p && *p != s; ++p);

        den = (*p == s && *(++p)) ? atoi(p) : 1;
        return 0;
    }

	/*
	*	获取下一天的开始时间,当前日期为20111017223000，号，则下一天日期为20111018000000
	*/
	void  GetNextDay(const char * current_time,char * next_time)
	{
		char szCurrent_date[8] ={0};
		strncpy(szCurrent_date,current_time,8);

		ChangeDay(szCurrent_date,1);

		sprintf(next_time,"%s000000",szCurrent_date);

		return ;

	}

    
    //判断目录是否存在。不存在则创建，返回 0 创建成功
    int MkdirIfNotExist(char *sPath)
    {
        if (0 != ACE_OS::access(sPath,F_OK))
        {
            string strCmd = "mkdir -p ";
            strCmd = strCmd + sPath;
            if (0 != ACE_OS::system(strCmd.c_str()))
            {
                return -1;
            }
        }
    
        return 0;
    }
    
}


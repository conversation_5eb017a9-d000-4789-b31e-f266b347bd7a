/////////////////////////////////////////////////////////////////////////////
#ifndef BSK_HEAD_INCLUDED_BOSSDES_H_1000B
#define BSK_HEAD_INCLUDED_BOSSDES_H_1000B
// Beta1.00
/////////////////////////////////////////////////////////////////////////////
#include "CharPtr.h"
/////////////////////////////////////////////////////////////////////////////
// 密码的加密/解密
// 模型
// 一个 字符串 可以用两倍长的 H字符串 来表示
// 现在把 H字符串 加密后 再表示为 字符串
class CBossDes
{
public:
	CBossDes();
	~CBossDes();
	
	// Attributes
public:
	CCharPtr m_szData;
	bool Encrypt();
	bool Decrypt();
	// Operations
public:
	static bool Str2Hstr(const CCharPtr& szSrc, CCharPtr& szDst);
	static bool Hstr2Str(const CCharPtr& szSrc, CCharPtr& szDst);
	// Overrides
public:
	// Implementation
protected:
};

/////////////////////////////////////////////////////////////////////////////
#endif //BSK_HEAD_INCLUDED_BOSSDES_H_1000B

/*******************************************
*Copyrights   2007，深圳天源迪科计算机有限公司
*                   技术平台项目组
*All rights reserved.
*
*Filename：
*       DCPairManager.h
*Indentifier：
*
*Description：
*       序列值对超时处理线程
*Version：
*       V1.0
*Author:
*       YF.Du
*Finished：
*       2008年10月10日
*History:
*       2008/10/10  V1.0 文件创建
********************************************/
#ifndef _DC_UID_CACHE_MANAGER_H_
#define _DC_UID_CACHE_MANAGER_H_

#include <unistd.h>
#include <stdio.h>
#include <sys/time.h>
#include "TThread.h"
#include "DCPairList.h"
#include "DCRentData.h"
#include "DataDef.h"


//#include "ace/OS.h"
//#include "ace/Signal.h"
//#include "ace/Condition_T.h"

#include "DCLogMacro.h"
#include "DCDBManer.h" 


class DCUidCacheManager : public TThread
{
	public:

		/****************************************************************************************
		*@input
				pairList : 序列号值对队列
				timeOut : 超时时间(ms)

		*@output

		*@return

		*@description		构造函数
		******************************************************************************************/
		DCUidCacheManager();

		~DCUidCacheManager();

		int init(DCPairList *pairList);
        void SetLogBegin(bool bLogBegin,int writeLogCount);
		int UpdateInstDealed(STImmedUser &stImmedUser, int nState);

	private:
		
		int create_sql_info(const char*sfile);
			
		void routine();

	private:

		DCPairList		*m_pairList;		//序列号值对队列
		DCDBManer		*m_pDbm;
        volatile bool m_bLogBegin;
        volatile int m_writeLogCount;
};

#endif

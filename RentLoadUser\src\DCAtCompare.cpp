/***********************************************************************
* Module:  DCAtCompare.cpp
* Author:  gaos
* Modified: 2010年5月26日 14:44:18
* Purpose: Implementation of the class DCAtCompare
* Comment: 融合帐务条件比较
***********************************************************************/

#include "DCAtCompare.h"
//#include "common/DCAcctFunction.h"
#include "DCLogMacro.h"

using namespace std;
////////////////////////////////////////////////////////////////////////
// Name:       DCAtCompare::Compare(STAACondItem& vi_stCondItem)
// Purpose:    Implementation of DCAtCompare::Compare()
// Comment:    判断单个条件项
// Parameters:
// - vi_stCondItem
// Return:     bool
////////////////////////////////////////////////////////////////////////

bool DCAtCompare::Compare(STATCondItem& vi_stCondItem)
{

    m_pLeft     = vi_stCondItem.szLeft;
    m_pOperator = vi_stCondItem.szOperator;
    m_pRight    = vi_stCondItem.szRight;
	
	list<string> ltStr;
	list<string>::iterator ltStrIt;
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCAtCompare::Compare begin,m_pLeft[%s],m_pOperator[%s],m_pRight[%s]",m_pLeft,m_pOperator,m_pRight);
	if (IsNumber(m_pOperator))
	{
		m_nOperatorId = atoi(m_pOperator);
		switch(m_nOperatorId)
		{
		case 11: //==
			return 0 == strcmp(m_pLeft,m_pRight);
		case 12: //<>
			return 0 != strcmp(m_pLeft,m_pRight);
		case 13: //>
			return strcmp(m_pLeft,m_pRight) > 0;
		case 14: //<
			return strcmp(m_pLeft,m_pRight) < 0;
		case 15: //>=
			return strcmp(m_pLeft,m_pRight) >= 0;
		case 16: //<=
			return strcmp(m_pLeft,m_pRight) <= 0;
		case 18: //in
			return InCompare(m_pLeft,m_pRight);
		case 19: //not in
			return !InCompare(m_pLeft,m_pRight);
		case 41: //相似
			return 0 == strncmp(m_pLeft,m_pRight,strlen(m_pRight));
		case 42: //不相似
			return 0 != strncmp(m_pLeft,m_pRight,strlen(m_pRight));
		case 43: //包含相似
			PublicLib::SplitStr(m_pRight, ',', ltStr);	//拆分字段
			for(ltStrIt=ltStr.begin(); ltStrIt!=ltStr.end(); ++ltStrIt)	//比较每一字段
			{
				if((*ltStrIt)!="")
				{
					if (!strncmp(m_pLeft, (*ltStrIt).c_str(), (*ltStrIt).size()))	//相似
					{
						return true;
					}
				}
			}
			return false;
		case 44: //不包含相似
			PublicLib::SplitStr(m_pRight, ',', ltStr);	//拆分字段
			for(ltStrIt=ltStr.begin(); ltStrIt!=ltStr.end(); ++ltStrIt)	//比较每一字段
			{
				if((*ltStrIt)!="")
				{
					if (!strncmp(m_pLeft, (*ltStrIt).c_str(), (*ltStrIt).size()))	//相似
					{
						return false;
					}
				}
			}
			return true;
		}
	}
	else
	{
	    if (IsNumber(m_pLeft) && IsNumber(m_pRight) && (!IsAlpha(m_pOperator[0])))
	    {
	        return NumberCompare(atol(m_pLeft),m_pOperator,atol(m_pRight));
	    }

	    if (0 == strcmp(m_pOperator,"==") || 0 == strcmp(m_pOperator,"="))
	    {
	        return 0 == strcmp(m_pLeft,m_pRight);
	    }
	    else if (0 == strcmp(m_pOperator,"!=") || 0 == strcmp(m_pOperator,"<>"))
	    {
	        return 0 != strcmp(m_pLeft,m_pRight);
	    }
	    else if (0 == strcmp(m_pOperator,">"))
	    {
	        return strcmp(m_pLeft,m_pRight) > 0;
	    }
	    else if (0 == strcmp(m_pOperator,">="))
	    {
	        return strcmp(m_pLeft,m_pRight) >= 0;
	    }
	    else if (0 == strcmp(m_pOperator,"<"))
	    {
	        return strcmp(m_pLeft,m_pRight) < 0;
	    }
	    else if (0 == strcmp(m_pOperator,"<="))
	    {
	        return strcmp(m_pLeft,m_pRight) <= 0;
	    }
	    else if (0 == strcmp(m_pOperator,"LIKE"))
	    {
	        return 0 == strncmp(m_pLeft,m_pRight,strlen(m_pRight));
	    }
	    else if (0 == strcmp(m_pOperator,"NOTLIKE"))
	    {
	        return 0 != strncmp(m_pLeft,m_pRight,strlen(m_pRight));
	    }
	    else if (0 == strcmp(m_pOperator,"IN"))
	    {
	        return InCompare(m_pLeft,m_pRight);
	    }
	    else if (0 == strcmp(m_pOperator,"NOTIN"))
	    {
	        return !InCompare(m_pLeft,m_pRight);
	    }
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCAtCompare::Compare end failed,m_pLeft[%s],m_pOperator[%s],m_pRight[%s]",m_pLeft,m_pOperator,m_pRight);

	return false;
}

bool DCAtCompare::Compare(STATCondItem* vi_stCondItem)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCAtCompare::Compare begin");

	int i =0; //从第一组开始
	int tmp_groupid =vi_stCondItem[0].iGroupid;
	while(vi_stCondItem[i].inext_group_id !=0)
	{

		if(tmp_groupid !=vi_stCondItem[i].iGroupid )
		{
			return true;
		}

		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCAtCompare::Compare","group id :[%d],left value :[%s],operator :[%s] right value :[%s]",
			vi_stCondItem[i].iGroupid,vi_stCondItem[i].szLeft,vi_stCondItem[i].szOperator,vi_stCondItem[i].szRight);


		if(Compare(vi_stCondItem[i]) != true)
		{
			if(vi_stCondItem[i].inext_group_id == -1)
			{
				return false;
			}
			//查找下一组

			i = vi_stCondItem[i].inext_group_id;

			tmp_groupid = vi_stCondItem[i].iGroupid;
		}
		else
		{
			++i;
		}
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCAtCompare::Compare end");

	return true;
}

////////////////////////////////////////////////////////////////////////
// Name:       DCAtCompare::Compare(multimap<int,STAACondItem> & vi_mmapCondItems)
// Purpose:    Implementation of DCAtCompare::Compare()
// Comment:    判断分组条件项集。组间或；组内与
// Parameters:
// - vi_mmapCondItems
// Return:     bool
////////////////////////////////////////////////////////////////////////

bool DCAtCompare::Compare(multimap<int,STATCondItem> & vi_mmapCondItems)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCAtCompare::Compare begin,vi_mmapCondItems.size=[%d]",vi_mmapCondItems.size());

	if (vi_mmapCondItems.size() <=0 )
	{
		return true;
	}

	int iCurGroupID = -999;
	bool bCurGroupFlag = false;
	for (multimap<int,STATCondItem>::iterator itrCondItem=vi_mmapCondItems.begin();
		itrCondItem != vi_mmapCondItems.end();
		itrCondItem++)
	{
		//到达临界。第一条或下组第一条
		if (iCurGroupID != itrCondItem->first)
		{
			//判断当前组是否满足
			if (bCurGroupFlag)
			{
				return true;
			}

			//切换到下组
			iCurGroupID = itrCondItem->first;
			bCurGroupFlag = true;
		}

		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCAtCompare::Compare","group id :[%d],left value :[%s],operator :[%s] right value :[%s]",
			iCurGroupID,(itrCondItem->second).szLeft,(itrCondItem->second).szOperator,(itrCondItem->second).szRight);
		//DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","operator :[%s] right value :[%s]",(itrCondItem->second).szOperator,(itrCondItem->second).szRight));


		//组起始时状态为TRUE，若有任何一条FALSE，组为FALSE
		if (bCurGroupFlag)
		{
			bCurGroupFlag = Compare(itrCondItem->second);
		}
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCAtCompare::Compare end,vi_mmapCondItems.size=[%d]",vi_mmapCondItems.size());

	return bCurGroupFlag;
}

/***************************************************************************************************
*输入：     vi_iCount：个数
*输入：     vi_iGroup：按顺序保存group_id的数组
*输入：     vi_iCount：个数
*输出：     无
*返回：     满足返回true，不满足返回false
*调用：     需要完成条件集判断的各个程序
*描述：     根据传入的条件集完成判断，条件集以group_id为单位，分为不同的条件组，
同一条件组下条件项全满足则条件组满足条件
只要有一个条件组满足条件则条件集满足条件
***************************************************************************************************/      
bool DCAtCompare::Compare(const int vi_iCount, const int *vi_iGroup,  STATCondItem *vi_stCondition)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCAtCompare::Compare begin,vi_iCount[%d]",vi_iCount);

    if (vi_iCount <= 0)
    {
        return true;    
    }

    int iGroupId = -99;
    bool bGroup = false;

    for (int i = 0; i < vi_iCount; i++)
    {
        if (iGroupId != vi_iGroup[i])  //是第一条件组，或轮询到下一条件组
        {
            if (bGroup)     //一个条件组满足则认为条件集满足返回
            {
                return bGroup;    
            }

            bGroup = true;
            iGroupId = vi_iGroup[i];
        }

        if (bGroup)         //条件组下有一个条件项不满足则认为条件组不满足
        {             
            bGroup = Compare(vi_stCondition[i]);          
        }
    }
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCAtCompare::Compare end,vi_iCount[%d]",vi_iCount);

    return bGroup;
}


////////////////////////////////////////////////////////////////////////
// Name:       DCAtCompare::IsNumber(char * vi_sIn)
// Purpose:    Implementation of DCAtCompare::IsNumber()
// Parameters:
// - vi_sIn
// Return:     bool
////////////////////////////////////////////////////////////////////////

bool DCAtCompare::IsNumber(const char * vi_sIn)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCAtCompare::IsNumber begin,vi_sIn[%s]",vi_sIn);

	const char *pPos = vi_sIn;

	if ('0' == pPos[0])
	{
		return false;
	}

	for (int iLoop=0;iLoop<strlen(pPos);iLoop++)
	{
		if (pPos[iLoop] < '0' || pPos[iLoop] > '9')
		{
			return false;
		}
	}
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCAtCompare::IsNumber end,vi_sIn[%s]",vi_sIn);
	return true;
}

////////////////////////////////////////////////////////////////////////
// Name:       DCAtCompare::IsAlpha(char vi_chIn)
// Purpose:    Implementation of DCAtCompare::IsAlpha()
// Parameters:
// - vi_chIn
// Return:     bool
////////////////////////////////////////////////////////////////////////

bool DCAtCompare::IsAlpha(char vi_chIn)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCAtCompare::IsAlpha begin,vi_chIn[%c]",vi_chIn);

	if ((vi_chIn >= 'a' && vi_chIn <= 'z') || (vi_chIn >= 'A' && vi_chIn <= 'Z'))
	{
		return true;
	}
	else
	{
		return false;
	}
}

//为了提高效率，string改为char*
bool DCAtCompare::NumberCompare(long vi_lLeft, const char* vi_pszOperator, long vi_lRight)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCAtCompare::NumberCompare begin,vi_lLeft[%ld],vi_pszOperator[%s],vi_lRight[%ld]",vi_lLeft,vi_pszOperator,vi_lRight);

    if ( 0 == strcmp(vi_pszOperator,"==") || 0 == strcmp(vi_pszOperator,"=") )
    {
        return vi_lLeft == vi_lRight;
    }
    else if ( 0 == strcmp(vi_pszOperator,"!=") || 0 == strcmp(vi_pszOperator,"<>") )
    {
        return vi_lLeft != vi_lRight;
    }
    else if ( 0 == strcmp(vi_pszOperator,">") )
    {
        return vi_lLeft > vi_lRight;
    }
    else if ( 0 == strcmp(vi_pszOperator,">=") )
    {
        return vi_lLeft >= vi_lRight;
    }
    else if ( 0 == strcmp(vi_pszOperator,"<") )
    {
        return vi_lLeft < vi_lRight;
    }
    else if ( 0 == strcmp(vi_pszOperator,"<=") )
    {
        return vi_lLeft <= vi_lRight;
    }

    return false;
}

////////////////////////////////////////////////////////////////////////
// Name:       DCAtCompare::InCompare(string vi_strLeft, string vi_strRight)
// Purpose:    Implementation of DCAtCompare::InCompare()
// Parameters:
// - vi_chIn
// Return:     bool
////////////////////////////////////////////////////////////////////////
/*
bool DCAtCompare::InCompare(string &vi_strLeft, string &vi_strRight)
{
	list<string> ltStr;

	acctfunc::SplitStr(vi_strRight.c_str(),',',ltStr);
	for (list<string>::iterator itr=ltStr.begin();itr!=ltStr.end();itr++)
	{
		if (vi_strLeft == (*itr))
		{
			return true;
		}
	}

	return false;
}
*/

bool DCAtCompare::InCompare(const char* vi_pszLeft, const char* vi_pszRight)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCAtCompare::InCompare begin,vi_pszLeft[%s],vi_pszRight[%s]",vi_pszLeft,vi_pszRight);
	if(strlen(vi_pszLeft)<=0)
	{
		if(strlen(vi_pszRight)<=0)
		{
			return true;
		}
		return false;
	}

    //构造 ",A," 格式
    if (vi_pszLeft[0] != ',')
    {
        sprintf(m_szTmpLeft,",%s",vi_pszLeft);
    }
    else
    {
        strcpy(m_szTmpLeft,vi_pszLeft);
    }

    int nLen = strlen(m_szTmpLeft);
    if (m_szTmpLeft[nLen-1] != ',')
    {
        m_szTmpLeft[nLen] = ',';
        m_szTmpLeft[nLen+1] = '\0';
    }

    //构造 ",B1,B2,B3," 格式
    if (vi_pszRight[0] != ',')
    {
        sprintf(m_szTmpRight,",%s",vi_pszRight);
    }
    else
    {
        strcpy(m_szTmpRight,vi_pszRight);
    }

    nLen = strlen(m_szTmpRight);
    if (m_szTmpRight[nLen-1] != ',')
    {
        m_szTmpRight[nLen] = ',';
        m_szTmpRight[nLen+1] = '\0';
    }

    //如果A在B中，判断
    char* pstr = strstr(m_szTmpRight, m_szTmpLeft);
    if (NULL != pstr)
    {
        return true; 
    }
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCAtCompare::InCompare end,vi_pszLeft[%s],vi_pszRight[%s]",vi_pszLeft,vi_pszRight);

    return false;
}

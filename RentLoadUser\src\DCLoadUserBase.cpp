/****************************************************************************************
*Copyrights  2016，深圳天源迪科计算机有限公司
*						OCS项目组
*All rights reserved.
*
* Filename：	DCLoadUserBase.cpp		
* Indentifier：		
* Description：	用户加载基类
* Version：		V1.0
* Author:		zsh
* Finished：	2016年11月19日
* History:
******************************************************************************************/

#include "DCLoadUserBase.h" 

using namespace std;

//重连数据库
int DCLoadUserBase::ReConnect()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","DCLoadUserBase begin,m_nReconnectInterval[%d],m_nReconnectTimes[%d]",m_cfgPara.nReconnectInterval,m_cfgPara.nReconnectTimes);
	
	int nConnectCnt = 0;
	int nReconnectInterval = m_cfgPara.nReconnectInterval;
	while(nConnectCnt < m_cfgPara.nReconnectTimes)
	{
		nConnectCnt++;
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCLoadUserBase::ReConnect","sleep %d seconds",nReconnectInterval);
		sleep(nReconnectInterval);
		int nRet = p_dbm->CheckReset();	
		if(nRet < 0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,nRet,"DCLoadUserBase::ReConnect","ReConnect failed.");
			continue;
		}
		else
		{			
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCLoadUserBase::ReConnect","ReConnect success!");
			return 0;
		}
	}
	DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCLoadUserBase::ReConnect","Reconnect max times! Exit !");

	return -1;
}


//判断数据库错误是否需要重连
bool DCLoadUserBase::IfReConnect(int nErrorcode)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","DCLoadUserBase::IfReConnect begin,nErrorcode[%d]",nErrorcode);

	if(nErrorcode==ERR_DB_CONN_TIMEOUT || nErrorcode==ERR_DB_CONN_TERM ||
		nErrorcode==ERR_DB_NOT_CONNECTED || nErrorcode==ERR_DB_NOT_CONNECTED2 ||
		nErrorcode==ERR_DB_NOT_CONNECTED3 || nErrorcode==ERR_DB_NOT_CONNECTED4 ||
		nErrorcode==ERR_DB_SESSION_KILLED || nErrorcode==ERR_DB_NOT_LOGON ||
		nErrorcode==ERR_DB_SHUT_DOWN || nErrorcode == ERR_DB_KILL_SESSION ||
		nErrorcode==ERR_DB_DBLINK_DOWN || nErrorcode==ERR_DB_SYNTAX_ANALYSIS ||
		nErrorcode==ERR_DB_THE_SAME_DBLINK_COVER || nErrorcode==ERR_DB_DBLINK_NAME_ERROR || 
		nErrorcode==12571)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","DCLoadUserBase::IfReConnect end,need reconnect");
		return true;
	}
    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","DCLoadUserBase::IfReConnect end,not need reconnect");
	return false;
}

int DCLoadUserBase::setParams(const STInputPara inputPara)
{
	m_inputParam = inputPara;    
    map<int, string>::iterator iter = m_mapLatnList.find(inputPara.nLatnID);
	if(iter != m_mapLatnList.end())
	{
		m_inputParam.strLatn = iter->second;
        return 0;
	}
    return -1;
}

void DCLoadUserBase::Clear()
{
    if(m_pairManager)
    {
        m_pairManager->exit();
    	SAFE_DELETE_PTR(m_pairManager);
    }
    if(m_uidCacheManager)
    {
    	m_uidCacheManager->exit();
    	SAFE_DELETE_PTR(m_uidCacheManager);
    }
	SAFE_DELETE_PTR(m_pairList);
    SAFE_DELETE_PTR(m_pComboAdpt);
    
	m_mapLatnCfg.clear();
    m_mapLatnList.clear();

    if(m_bDcfServMode)
    {
    	map<string, DCCallZKServerMaker*>::iterator mapCallSer_itr = m_mappCallServer.begin();
    	while(mapCallSer_itr != m_mappCallServer.end())
    	{
    		SAFE_DELETE_PTR(mapCallSer_itr->second);
    		++mapCallSer_itr;
    	}
    	m_mappCallServer.clear();
    }
}

//初始化
int DCLoadUserBase::InitBase(const STInputPara inputPara,const STConfigPara cfgPara,const int nProcId)
{
	//初始化配置参数
	m_inputParam = inputPara;
	m_cfgPara = cfgPara;
	m_nProcId = nProcId;

    //初始化数据库链接
    char sfile[1024] = {0};

    const char *path = getenv("OCS_CONFIG");
    sprintf(sfile, "%s/RentLoad.sql.xml", path);
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "DCLoadUserBase::InitBase", "Get sql config file from path[%s/RentLoad.sql.xml]", path);

    p_dbm = new DCDBManer();
    if (!p_dbm)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, 1, "DCLoadUserBase::InitBase", "create DCDBManer failed");
        return -1;
    }
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "DCLoadUserBase::InitBase", "start to init DCDBManer for sql file [%s/RentLoad.sql.xml]", path);
    int nRet = p_dbm->Init(sfile);
    if(nRet < 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1,"DCLoadUserBase::InitBase", "init DCDBManer failed. ret[%d]",nRet);
		return -1;
	}
	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0, "DCLoadUserBase::InitBase","end to init DCDBManer");

    //初始化套餐匹配模块
	m_pComboAdpt = NULL;
	m_pComboAdpt = new DCComboAdaptPrdInst();
	nRet = m_pComboAdpt->init(p_dbm);
	if (nRet < 0)
	{
        DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"","DCLoadOldUser::InitSub, init DCComboAdaptPrdInst failed, error code[%d]", nRet);
		return -1;
	}
    m_pComboAdpt->setInputPara(m_inputParam,m_cfgPara);
   
    //子类初始化 
    nRet = InitSub();
    if (nRet)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, 1, "DCLoadUserBase::InitBase", "InitSub failed. ret[%d]", nRet);
        return -1;
    }

	return 0;
		
}

int DCLoadUserBase::InitBase(const map<string, STConfigPara> &mapLatnCfg,const map<int,string> &mapLatnList, const int nProcId, const bool bNeedCall)
{
	//初始化配置参数
	m_mapLatnCfg = mapLatnCfg;
    m_mapLatnList = mapLatnList;
	m_nProcId = nProcId;

    //初始化数据库链接
    char sfile[1024] = {0};

    const char *path = getenv("OCS_CONFIG");
    sprintf(sfile, "%s/RentLoad.sql.xml", path);
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "DCLoadUserBase::InitBase", "Get sql config file from path[%s/RentLoad.sql.xml]", path);

    p_dbm = new DCDBManer();
    if (!p_dbm)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, 1, "DCLoadUserBase::InitBase", "create DCDBManer failed");
        return -1;
    }
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "DCLoadUserBase::InitBase", "start to init DCDBManer for sql file [%s/RentLoad.sql.xml]", path);
    int nRet = p_dbm->Init(sfile);
    if(nRet < 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1,"DCLoadUserBase::InitBase", "init DCDBManer failed. ret[%d]",nRet);
		return -1;
	}
	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0, "DCLoadUserBase::InitBase","end to init DCDBManer");

    
    //初始化套餐匹配模块
    m_pComboAdpt = new DCComboAdaptPrdInst();
    nRet = m_pComboAdpt->init(p_dbm);
    if (nRet < 0)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"","DCLoadOldUser::InitSub, init DCComboAdaptPrdInst failed, error code[%d]", nRet);
        return -1;
    }

    map<string, STConfigPara>::iterator iter = m_mapLatnCfg.begin();
    if(bNeedCall)
    {
        //组件初始化
        m_pairList = new DCPairList();
    		
    	//启动消息超时处理线程 m_pairList nTimeOut
    	m_pairManager = new DCPairManager();
    	m_pairManager->init(m_pairList,(iter->second.nFlowTimeOut)*100);//超时时间 ,FlowTimeOut的单位为100ms
    	m_pairManager->start();

    	//启动消息返回后删除DCA缓存线程 m_pairList
    	m_uidCacheManager = new DCUidCacheManager();
    	m_uidCacheManager->init(m_pairList);
    	m_uidCacheManager->start();
    }
        
	for(iter = m_mapLatnCfg.begin(); iter != m_mapLatnCfg.end(); iter++)
	{
		m_cfgPara.ncycleSleepTime = iter->second.ncycleSleepTime;
		m_cfgPara.nReconnectInterval = iter->second.nReconnectInterval;
		m_cfgPara.nReconnectTimes = iter->second.nReconnectTimes;
		
		//初始化消息发送模块
		if(bNeedCall)
        {      
    		DCCallZKServerMaker *pCallServerBase = new DCCallZKServerMaker();
    		nRet = pCallServerBase->init(true,m_pairList,m_uidCacheManager,iter->second);
    		if (nRet < 0)
    		{
    			DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"","DCLoadOldUser::InitSub, init DCCallZKServerMaker failed, error code[%d]", nRet);
    			return -1;
    		}
    		m_mappCallServer.insert(pair<string, DCCallZKServerMaker* >(iter->first, pCallServerBase));
        }
    }
	return 0;
}

pthread_t DCLoadUserBase::GetThreadNo()
{
    return m_tno;
}

//线程处理函数
void DCLoadUserBase::routine()
{
	int nRet = 0;

    while (m_nRunState == 1)
    {
        sleep(m_cfgPara.ncycleSleepTime);
		//线程不退出
		ReConnect();
        if(m_bDcfServMode)
            processDcfs();
        else
		    process();
    }
}

//设置线程的运行控制状态
void DCLoadUserBase::SetRunState(int nState)
{
	m_nRunState = nState;
}

//获取线程的运行控制状态
int DCLoadUserBase::GetRunState()
{
	return m_nRunState;
}

//设置线程的 业务运行 控制状态
void DCLoadUserBase::SetTaskState(int nState)
{
	m_nTaskState = nState;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","SetTaskState[%d]",m_nTaskState);
}

//获取线程的 业务运行 控制状态
int DCLoadUserBase::GetTaskState()
{
	return m_nTaskState;
}


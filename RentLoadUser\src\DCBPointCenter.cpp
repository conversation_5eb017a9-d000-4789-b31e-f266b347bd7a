/*******************************************
*Copyrights  2008， 深圳天源迪科信息技术股份有限公司
*						在线计费项目组
*All rights reserved.
*
* Filename：	DCBPointCenter.cpp
* Indentifier：	
* Description：	埋点相关类
* Version：		V1.0
* Author:		GuanHui
* Finished：	2018年7月10日
* History:		
********************************************/
#include "DCBPointCenter.h"
#include "DCLogMacro.h"
#include "publiclib.h"

using namespace std;

DCBPointCenter* DCBPointCenter::m_pBPointCenter = NULL;

int DCBPointCenter::Release()
{
	map<STMonHead,DCMon*>::iterator iterMon;
	for(iterMon = m_mapBPHandler.begin(); iterMon != m_mapBPHandler.end(); iterMon++)
	{
		if(NULL != iterMon->second)
		{
			delete iterMon->second;
			iterMon->second = NULL;
			
		}
	}
    if (NULL != m_pBPointCenter)
    {
        delete m_pBPointCenter;
        m_pBPointCenter = NULL;
    }
    
    return 0;
}


DCBPointCenter::DCBPointCenter(void)
{
	memset(m_cIpAddr, 0x0, sizeof(m_cIpAddr));
	memset(m_cPid, 0x0, sizeof(m_cPid));
 	m_lPid = getpid();
	PublicLib::GetHostAddrIp(m_cIpAddr);
	m_nBPFlag = 0;//默认关
}

DCBPointCenter::~DCBPointCenter(void)
{
    Release();
}

//初始化参数
int DCBPointCenter::Init(STBPInput v_InParam)
{
	m_ndelayms = v_InParam.st_ndelayms;
	m_nportocol = v_InParam.st_nportocol;
	m_nBPFlag = v_InParam.st_nBPFlag;
	m_strAddr = v_InParam.st_strAddr;
	sprintf(m_cPid, "%ld",m_lPid);
	
    return 0;
}

DCMon* DCBPointCenter::GetBPHandle(const string sys, const string subsys, const string module)
{
	//开关关闭不进行统计
	if(0 == m_nBPFlag)
	{
		return NULL;
	}
	int nRet = 0;
	STMonHead hKey;
	hKey.system = sys;
	hKey.subsys = subsys;
	hKey.module = module;
	map<STMonHead,DCMon*>::iterator iterMon;
	iterMon = m_mapBPHandler.find(hKey);
	if(m_mapBPHandler.end() != iterMon)
	{
		return iterMon->second;
	}
	else
	{
		DCMon* tmphandler = new DCMon();
		if(NULL != tmphandler)
		{
			nRet = MonInit(tmphandler, sys, subsys, module);
			if(0 != nRet)
			{
				return NULL;
			}
			m_mapBPHandler.insert(make_pair(hKey,tmphandler));
			return tmphandler;
		}
	}
	return NULL;
}
int DCBPointCenter::MonInit(DCMon* phandler, const string sys, const string subsys, const string module)
{
	int nRet = 0;
	nRet = phandler->init(m_ndelayms, m_nportocol, m_cIpAddr);
	if(0 != nRet)
	{
		return nRet;
	}
	phandler->head(sys.c_str(), subsys.c_str(), module.c_str());
	return 0;
}
void DCBPointCenter::group_all_init(DCMon* phandler, const char* group_prefix,std::list<string> v_listLatn)
{
	if(NULL == phandler)
	{
		return ;
	}
	char group[50];
	for (std::list<string>::iterator iterLatnId = v_listLatn.begin(); iterLatnId != v_listLatn.end(); ++iterLatnId)
	{
		memset(group,0x0,sizeof(group));
		sprintf(group, "%s|%s", group_prefix,atoi((*iterLatnId).c_str()));
		phandler->group_set(group, "host", m_cIpAddr);
		phandler->group_set(group, "pid", m_cPid);
		phandler->group_set(group, "latnid", (*iterLatnId).c_str());
	}
}

void DCBPointCenter::group_init(DCMon* phandler, const char* group_prefix, const char* Latn, char* group)
{
	if(NULL == phandler)
	{
		return ;
	}
	sprintf(group, "%s|%s", group_prefix,Latn);
	phandler->group_set(group, "host", m_cIpAddr);
	phandler->group_set(group, "pid", m_cPid);
	phandler->group_set(group, "latnid", Latn);
}
void DCBPointCenter::group_init(DCMon* phandler, const char* group_prefix, const int Latn, char* group)
{
	if(NULL == phandler)
	{
		return ;
	}
	sprintf(group, "%s|%d", group_prefix,Latn);
	phandler->group_set(group, "host", m_cIpAddr);
	phandler->group_set(group, "pid", m_cPid);
	char tlatn[6] = {0};
	sprintf(tlatn,"%d",Latn);
	phandler->group_set(group, "latnid", tlatn);
}
void DCBPointCenter::GetFullGroup(const char* group_prefix, const int Latn, char* group)
{
	sprintf(group, "%s|%d", group_prefix,Latn);
}
void DCBPointCenter::GetFullGroup(const char* group_prefix, const char * Latn, char* group)
{
	sprintf(group, "%s|%s", group_prefix,Latn);
}

/***设置指标组头部信息***/
void DCBPointCenter::group_set(DCMon* phandler, const char* group, const char* name, const char* value)
{
	if(NULL == phandler)
	{
		return ;
	}
	phandler->group_set(group, name, value);	
}

/***设置单组指标增量值***/
void DCBPointCenter::cycle_inc(DCMon* phandler, const char* group, const char* k, const char* ki, long value)
{
	if(NULL == phandler)
	{
		return ;
	}
	phandler->cycle_inc(group, k, ki, value);
}

/***设置单组指标当前值***/
void DCBPointCenter::cycle_set(DCMon* phandler, const char* group, const char* k, const char* ki, long value)
{
	if(NULL == phandler)
	{
		return ;
	}
	phandler->cycle_set(group, k, ki, value);
}

/***设置单组指标整数状态值***/
void DCBPointCenter::state_set(DCMon* phandler, const char* group, const char* k, const char* ki, long value)
{
	if(NULL == phandler)
	{
		return ;
	}
	phandler->state_set(group, k, ki, value);
}

/***设置单组指标浮点状态值***/
void DCBPointCenter::state_set(DCMon* phandler, const char* group, const char* k, const char* ki, float value)
{
	if(NULL == phandler)
	{
		return ;
	}
	phandler->state_set(group, k, ki, value);
}

/***设置多组指标增量值***/
void DCBPointCenter::cycle_array_inc(DCMon* phandler, const char* group, int id, const char* k, const char* ki, long value)
{
	if(NULL == phandler)
	{
		return ;
	}
	phandler->cycle_array_inc(group, id, k, ki, value);
}

/***设置多组指标当前值***/
void DCBPointCenter::cycle_array_set(DCMon* phandler, const char* group, int id, const char* k, const char* ki, long value)
{
	if(NULL == phandler)
	{
		return ;
	}
	phandler->cycle_array_set(group, id, k, ki, value);
}

/***设置多组指标整数状态值***/
void DCBPointCenter::state_array_set(DCMon* phandler, const char* group, int id, const char* k, const char* ki, long value)
{
	if(NULL == phandler)
	{
		return ;
	}
	phandler->state_array_set(group, id, k, ki, value);
}

/***设置多组指标浮点状态值***/
void DCBPointCenter::state_array_set(DCMon* phandler, const char* group, int id, const char* k, const char* ki, float value)
{
	if(NULL == phandler)
	{
		return ;
	}
	phandler->state_array_set(group, id, k, ki, value);
}

/***设置多组指标增量值***/
void DCBPointCenter::cycle_array_inc(DCMon* phandler, const char* group, const char* id, const char* k, const char* ki, long value)
{
	if(NULL == phandler)
	{
		return ;
	}
	phandler->cycle_array_inc(group, id, k, ki, value);
}

/***设置多组指标当前值***/
void DCBPointCenter::cycle_array_set(DCMon* phandler, const char* group, const char* id, const char* k, const char* ki, long value)
{
	if(NULL == phandler)
	{
		return ;
	}
	phandler->cycle_array_set(group, id, k, ki, value);
}

/***设置多组指标整数状态值***/
void DCBPointCenter::state_array_set(DCMon* phandler, const char* group, const char* id, const char* k, const char* ki, long value)
{
	if(NULL == phandler)
	{
		return ;
	}
	phandler->state_array_set(group, id, k, ki, value);
}

/***设置多组指标浮点状态值***/
void DCBPointCenter::state_array_set(DCMon* phandler, const char* group, const char* id, const char* k, const char* ki, float value)
{
	if(NULL == phandler)
	{
		return ;
	}
	phandler->state_array_set(group, id, k, ki, value);
}



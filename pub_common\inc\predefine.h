/******************************************************************************************
Copyright           : 2004-5-16, Shenzhen Tianyuan DIC  Co.,Ltd
FileName            : PreDefine.h
Author              : guoxd
Version             : 
Date Of Creation    : 2004-5-16
Description         : 所有预定义信息描述
Others              : 
Function List       : 
    1.  ......
Modification History:
    1.Date  :
      Author  :
      Modification  :
******************************************************************************************/

#ifndef _PREDEFINE_H
#define _PREDEFINE_H



#include <stdio.h>
#include <assert.h>
#include <vector>
#include <list>
#include <string>


#ifdef WIN32
	#include <crtdbg.h>
#endif //WIN32


#if (defined _HP_ )||(defined WIN32)
    using namespace std;
	typedef vector<string> StringArray;
	typedef list<string>	 StringList;
	typedef vector<int>         INTArray;
	typedef vector<size_t>      SIZETArray;
	//
	typedef vector <string>::iterator StringArrayIter;
	typedef list <string>::iterator   StringListIter;
	typedef vector<int>::iterator          INTArrayIter;
	typedef vector<size_t>::iterator       SIZETArrayIter;
	typedef vector<time_t>		 TimeArray;
#else
	typedef std::vector<std::string> StringArray;
	typedef std::list<std::string>	 StringList;
	typedef std::vector<int>         INTArray;
	typedef std::vector<size_t>      SIZETArray;
	//
	typedef std::vector <std::string>::iterator StringArrayIter;
	typedef std::list <std::string>::iterator   StringListIter;
	typedef std::vector<int>::iterator          INTArrayIter;
	typedef std::vector<size_t>::iterator       SIZETArrayIter;
	typedef std::vector<time_t>		 TimeArray;
#endif


#ifdef DEBUG
    //在Windows下用Debug Out 窗口输出
    #if defined (WIN32) && defined (_DEBUG) && defined (_WIN32_)
	    #define ZTRACE           CDebugTraceMgr::GetDebugTraceMgr()->WinDebugOutputEx
	    #define ZASSERT(f)       _ASSERTE(f)
	//如果定义了调试，而且要求记录TRACE日志,用于不可以重现错误调试   
	#elif defined (TRACELOG)
        #define ZTRACE			CDebugTraceMgr::GetDebugTraceMgr()->DebugOutputEx
	    #define ZASSERT(f)      while(!(f)){\
	                                 CDebugTraceMgr::GetDebugTraceMgr()->DebugAssertEx(__FILE__,__LINE__);\
	                                 assert(false);\
	                                 break;\
	                                }
	//否则调试信息只在stdout输出
	#else
	    #define ZTRACE           printf
	    #define ZASSERT(f)       assert(f) 
	#endif

    #define ZVERIFY(f)	             ZASSERT(f)  
	#define ZTRACE0(sz)              ZTRACE(("%s"), _T(sz))
	#define ZTRACE1(sz, p1)          ZTRACE((sz), p1)
	#define ZTRACE2(sz, p1, p2)      ZTRACE((sz), p1, p2)
	#define ZTRACE3(sz, p1, p2, p3)  ZTRACE((sz), p1, p2, p3)
#else //DEBUG
    //注意区别ZASSERT和VERIFY的区别
	#define ZASSERT(f)				((void)0)
	#define ZVERIFY(f)               ((void)(f)) 
	#define ZTRACE					
	#define ZTRACE0(sz)
	#define ZTRACE1(sz, p1)
	#define ZTRACE2(sz, p1, p2)
	#define ZTRACE3(sz, p1, p2, p3)
#endif //DEBUG

//typedef unsigned long       DWORD;
#ifdef WIN32
	#ifndef BOOL
    typedef int                 BOOL;
	#endif
#endif

typedef unsigned char       BYTE;
typedef unsigned short      WORD;
typedef float               FLOAT;
typedef int                 INT;
typedef unsigned int        UINT;

#ifndef _max
	#define _max(a,b)            (((a) > (b)) ? (a) : (b))
#endif

#ifndef min
//	#define min(a,b)            (((a) < (b)) ? (a) : (b))
#endif

#define MAKEWORD(a, b)      ((WORD)(((BYTE)((DWORD_PTR)(a) & 0xff)) | ((WORD)((BYTE)((DWORD_PTR)(b) & 0xff))) << 8))
#define MAKELONG(a, b)      ((LONG)(((WORD)((DWORD_PTR)(a) & 0xffff)) | ((DWORD)((WORD)((DWORD_PTR)(b) & 0xffff))) << 16))
#define LOWORD(l)           ((WORD)((DWORD_PTR)(l) & 0xffff))
#define HIWORD(l)           ((WORD)((DWORD_PTR)(l) >> 16))
#define LOBYTE(w)           ((BYTE)((DWORD_PTR)(w) & 0xff))
#define HIBYTE(w)           ((BYTE)((DWORD_PTR)(w) >> 8))

#endif //_PREDEFINE_H




#include <utility>
#include <unistd.h>
#include <sys/types.h>
#include <time.h>
#include "DCParseXml.h"
#include "DCLoadOldCrossUser.h"
#include "DCPerfStatistic.h"

#define DIFFTIME_US(Tb, Ta) ((((Tb)>>24) - ((Ta)>>24))*1000000 + ((Tb)&0xFFFFFF) - ((Ta)&0xFFFFFF))

using namespace std;


DCLoadOldCrossUser::~DCLoadOldCrossUser()
{
}

int DCLoadOldCrossUser::InitSubLatnId()
{
	//获取本地网列表	
	m_ltLatn.clear();
    char szLatn[5] = {0};
    sprintf(szLatn,"%d",m_inputParam.nLatnID);
    m_ltLatn.push_back(szLatn);
    m_nLatnId = m_inputParam.nLatnID;
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "InitSubLatnId:%d",m_inputParam.nLatnID);

    map<string, STConfigPara>::iterator iterCfg;
	iterCfg = m_mapLatnCfg.find(m_inputParam.strLatn);
	if(iterCfg != m_mapLatnCfg.end())
	{
		m_cfgPara = iterCfg->second;
	}

	//初始化套餐匹配模块
	m_pComboAdpt->setInputPara(m_inputParam,m_cfgPara);

	if(m_cfgPara.nCrossAcctChgFlag)
	{
		m_bCrossAcctChgFlag = true;
	}
	else
	{
		m_bCrossAcctChgFlag = false;
	}

	DCBIZLOG(DCLOG_LEVEL_INFO, 0,"","InitSubLatnId success");
	DCDATLOG("RC00001:");
	return 0;
}

//初始化
int DCLoadOldCrossUser::InitSub()
{
	string latnId = m_cfgPara.latnList;	
	m_ltLatn.clear();
    PublicLib::SplitStr(latnId.c_str(),'|',m_ltLatn);

    if (m_ltLatn.empty())
    {		
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldCrossUser::InitSub","Configuration item of %s is null",m_inputParam.strLatn.c_str());
        return -1;
    }
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldCrossUser::InitSub", "%s is[%s] get size:%d",m_inputParam.strLatn.c_str(),latnId.c_str(),m_ltLatn.size());


	if(m_cfgPara.nCrossAcctChgFlag)
	{
		m_bCrossAcctChgFlag = true;
	}
	else
	{
		m_bCrossAcctChgFlag = false;
	}
	
	DCBIZLOG(DCLOG_LEVEL_INFO, 0,"","DCLoadOldCrossUser::InitSub success");
	DCDATLOG("RC00001:");
	return 0;
}

//统一分发模式处理函数
int DCLoadOldCrossUser::processDcfs()
{
    if(1 == m_nTaskState)
    {
        int nRet = 0;   
        long lnQueryAcctId=-99,lnQueryOfrInstId=-99,lnPkid=0;
        
        DCDateTime cur_time;
        char szCurTime[16]={0};

        nRet = InitSubLatnId();
        if(nRet < 0)
        {
            DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "processDcfs","InitSubLatnId fail");
            SetTaskState(0);
            return -1;
        }

        if (ReConnect()) //重连失败
        {
            DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "ReConnect failed,exit");
            SetTaskState(0);
            return -1;
        }

        // 后付费跨账户更新流程 (SQL任务)
        if(PROC_TYPE_POST_CROSS_UPDATE == m_inputParam.nProType)//跨账户分组进程
        {
            //acct_id, ofr_inst_id
            if(3 == m_inputParam.vecSQLFields.size())
            {
                lnPkid = atol(m_inputParam.vecSQLFields[0].c_str());
                lnQueryAcctId = atol(m_inputParam.vecSQLFields[1].c_str());
                lnQueryOfrInstId = atol(m_inputParam.vecSQLFields[2].c_str());

                //任务对应表数据预占
                nRet = UpdateDealState(lnPkid, m_inputParam.nLatnID);
                if(nRet < 0)
                {
                    //任务处理失败
                    DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "processDcfs", "UpdateDealState failed,exit");
                    SetTaskState(0);
                    return -1;
                }
            }
            else
            {
                DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "task_params size[%d] error!", m_inputParam.vecSQLFields.size());
                SetTaskState(0);
                return -1;
            }
            
            nRet = m_pComboAdpt->SaveSpecialCrossAcct(lnQueryAcctId,lnQueryOfrInstId,m_inputParam.nLatnID,m_bCrossAcctChgFlag);
            if(nRet < 0)
            {
                DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "processDcfs", "SaveSpecialCrossAcct failed,exit");
                SetTaskState(0);
                return -1;
            }

            DCDATLOG(); //主动触发日志输出
            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"processDcfs", "Cycle scan tb_bil_cross_account_mid end one times.");

            SetTaskState(0);
            return 0;
        }

        char szCurrDate[9]={0};
        strncpy(szCurrDate,cur_time.ToString(string("YYYYMMDDHHNNSS")).c_str(),8);
        int nCurrDate = atoi(szCurrDate);
        //如果日期或本地网变更,则重新刷新
        int nBillingCycleID = (int)(nCurrDate/100);
        if (m_nBillingCycleId != nCurrDate || m_nLatnId!=DCDataCenter::instance()->m_nLatnId)//刷新时间到天 
        {
            m_nBillingCycleId = nCurrDate;
            DCDataCenter::instance()->RefreshBillingCycle(nBillingCycleID,m_nLatnId);
            if(!((m_pComboAdpt->m_pEventType)->reloadCondition()))
            {
                DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"processDcfs","DCRDEventType reloadCondition error\n");
                SetTaskState(0);
                return 0;
            }
        }   
            
        if( m_pComboAdpt->LoadSpecialAcct(m_nLatnId)<0)
        {
            DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"processDcfs","LoadSpecialAcct failed, LatnId[%d].",m_nLatnId);
            SetTaskState(0);
            return -1;
        }
 
        //填写开始处理时间  
        SetLastDealTimeDcfServ(cur_time, m_inputParam.nProType, m_nProcId, m_nLatnId, true);
        
        nRet = CheckCrossAcctOfrInst(m_nLatnId);
        if(nRet<0)
        {   
            if(ReConnect()) //重连失败
            {
                DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "processDcfs",   "ReConnect failed,exit");   
                SetTaskState(0);
                return -1;
            }   
            else  //重连成功,需要重新加载用户
            {                       
                DCBIZLOG(DCLOG_LEVEL_INFO, 0,"processDcfs", "ReConnect success,CheckCrossAcctOfrInst once again");
                DCDATLOG("RC00002:");
                nRet = CheckCrossAcctOfrInst(m_nLatnId);                
                if(nRet<0)
                {                           
                    DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"processDcfs", "CheckCrossAcctOfrInst failed again");
                    SetTaskState(0);
                    return -1;
                }
            }
        }

        memset(szCurTime,0x00,sizeof(szCurTime));
        PublicLib::GetTime(szCurTime, YYYYMMDDHHMMSS);
        cur_time.FromString(szCurTime,"YYYYMMDDHHNNSS");
        SetLastDealTimeDcfServ(cur_time, m_inputParam.nProType, m_nProcId, m_nLatnId, false);

        DCBIZLOG(DCLOG_LEVEL_INFO, 0,"processDcfs","Check cross ofrinst finished,sleep 10s and then deal next flew");
        DCDATLOG("RC00005:");
        ACE_Time_Value aSleepS(10);
        ACE_OS::sleep(aSleepS); 
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"processDcfs", "DCLoadOldCrossUser::process end");

        DCDATLOG(); //主动触发日志输出
        SetTaskState(0);
    }//if(1 == m_nTaskState)
    return 0;
}

//处理函数
int DCLoadOldCrossUser::process()
{
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "DCLoadOldCrossUser::process begin. procid:%d",m_nProcId);

    int nRet = 0;   

    map<long,STEventPro> MapEvtType ;
    DCDateTime last_time,cur_time;
    char szCurTime[16]={0};

    //刷新数据
    initCycEvtPro(MapEvtType,m_nProcId,m_inputParam.nProType);
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldCrossUser::process","MapEvtType size is [%d]",MapEvtType.size());

    for (std::list<string>::iterator iterLatnId = m_ltLatn.begin(); iterLatnId != m_ltLatn.end(); ++iterLatnId)
    { 
        m_nLatnId =  atoi((*iterLatnId).c_str());

        if(PROC_TYPE_POST_CROSS_UPDATE == m_inputParam.nProType)//跨账户分组进程
        {
            nRet = UpdateSpecialCrossAcct(m_nLatnId);
            if(nRet<0)
            {   
                if(ReConnect()) //重连失败
                {
                    DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldCrossUser::process",   "ReConnect failed,exit");   
                    return -1;
                }   
                else  //重连成功,需要重新加载用户
                {                       
                    DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCLoadOldCrossUser::process", "ReConnect success,UpdateSpecialCrossAcct once again");
                    nRet = UpdateSpecialCrossAcct(m_nLatnId);               
                    if(nRet<0)
                    {                           
                        DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"DCLoadOldCrossUser::process", "UpdateSpecialCrossAcct failed again");
                        return -1;
                    }
                }
            }
            DCDATLOG(); //主动触发日志输出
            continue;
        }
        
        map<long,STEventPro>::iterator iter = MapEvtType.begin();
        for(;iter!=MapEvtType.end();iter++)
        {
            if(m_nLatnId!=(iter->second).nLatnId)
            {
                DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldCrossUser::process","continue this latn_id! [input latn_id=%d] [config latn_id=%d]",m_nLatnId,(iter->second).nLatnId);
                continue;
            }
            last_time.FromString((iter->second).szLastDealTime,"YYYYMMDDHHNNSS");
            memset(szCurTime,0x00,sizeof(szCurTime));
            PublicLib::GetTime(szCurTime, YYYYMMDDHHMMSS);
            cur_time.FromString(szCurTime,"YYYYMMDDHHNNSS");
            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldCrossUser::process","load old crossuser! [CycleEventId=%ld] [LastDealTime=%s] [CurTime=%s] [SetHour=%d] [LatnId=%d]",
                (iter->second).lnCycleEventId,(iter->second).szLastDealTime,szCurTime,(iter->second).nSetHour,(iter->second).nLatnId);
            
            if(IsNeedDeal(last_time,cur_time,(iter->second).nSetHour))
            {
                char szCurrDate[9]={0};
                strncpy(szCurrDate,cur_time.ToString(string("YYYYMMDDHHNNSS")).c_str(),8);
                int nCurrDate = atoi(szCurrDate);
                //如果日期或本地网变更,则重新刷新
                int nBillingCycleID = (int)(nCurrDate/100);
                if (m_nBillingCycleId != nCurrDate || m_nLatnId!=DCDataCenter::instance()->m_nLatnId)//刷新时间到天 
                {
                    m_nBillingCycleId = nCurrDate;
                    DCDataCenter::instance()->RefreshBillingCycle(nBillingCycleID,m_nLatnId);
                    if(!((m_pComboAdpt->m_pEventType)->reloadCondition()))
                    {
                        DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCLoadOldCrossUser::process","DCRDEventType reloadCondition error\n");
                        return 0;
                    }
                }   
                
                if( m_pComboAdpt->LoadSpecialAcct(m_nLatnId)<0)
                {
                    DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldCrossUser::process","LoadSpecialAcct failed, LatnId[%d].",m_nLatnId);
                    return -1;
                }
                
                if(PROC_TYPE_POST_CORSS_JUDGE == m_inputParam.nProType)//定时提取套餐实例，判断是否为跨账户
                {
                    //填写开始处理时间  
                    SetLastDealTime(cur_time,iter->second,true);
                    
                    nRet = CheckCrossAcctOfrInst(m_nLatnId);
                    if(nRet<0)
                    {   
                        if(ReConnect()) //重连失败
                        {
                            DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldCrossUser::process",   "ReConnect failed,exit");   
                            return -1;
                        }   
                        else  //重连成功,需要重新加载用户
                        {                       
                            DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCLoadOldCrossUser::process", "ReConnect success,CheckCrossAcctOfrInst once again");
                            DCDATLOG("RC00002:");
                            nRet = CheckCrossAcctOfrInst(m_nLatnId);                
                            if(nRet<0)
                            {                           
                                DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"DCLoadOldCrossUser::process", "CheckCrossAcctOfrInst failed again");
                                return -1;
                            }

                        }
                        
                    }
                }
                //填写处理完成时间  
                memset(szCurTime,0x00,sizeof(szCurTime));
                PublicLib::GetTime(szCurTime, YYYYMMDDHHMMSS);
                cur_time.FromString(szCurTime,"YYYYMMDDHHNNSS");
                SetLastDealTime(cur_time,iter->second,false);

            }
            else
            {
                DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCLoadOldCrossUser::process", "IsNeedDeal return false!");
                DCDATLOG("RC00004:");
                continue;
            }
            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldCrossUser::process","Deal one event time end!***********************************************");
        }
    }

    if(PROC_TYPE_POST_CROSS_UPDATE == m_inputParam.nProType)//跨账户分组进程
    {
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldCrossUser::process", "Cycle scan tb_bil_cross_account_mid end one times,sleep 3s.");

        ACE_Time_Value aSleepS(3);
        ACE_OS::sleep(aSleepS); 
        return 0;
    }
 
    DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCLoadOldCrossUser::process","Send cross user finished,sleep 10s and then deal next flew");
    DCDATLOG("RC00005:");
    ACE_Time_Value aSleepS(10);
    ACE_OS::sleep(aSleepS); 
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldCrossUser::process", "DCLoadOldCrossUser::process end");

    DCDATLOG(); //主动触发日志输出
    return 0;
}


//获取定时任务配置表信息
int DCLoadOldCrossUser::initCycEvtPro(map<long,STEventPro> &MapEvtType,int nProcId,int nProcType)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldCrossUser::initCycEvtPro begin,nProcId[%d] nProcType[%d]",nProcId,nProcType);

    long lnCycleEventId=0;
    char buf[32];
	string sbuf;

    try
    {
        UDBSQL* pQuery = p_dbm->GetSQL("QueryCycleEvent");			
		if(pQuery==NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldCrossUser::initCycEvtPro","GetSQL [QueryCycleEvent] failed");
			return -1;
		} 
        pQuery->UnBindParam();
        pQuery->BindParam(1,nProcId);
        pQuery->BindParam(2,nProcType);
		pQuery->GetSqlString(sbuf);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldCrossUser::initCycEvtPro","Do SQL:Begin to do QueryCycleEvent,sql :[%s]",sbuf.c_str());	
        
        pQuery->Execute();

		STEventPro tmpPro;
        while(pQuery->Next())
        {	
			pQuery->GetValue(1,buf);
            lnCycleEventId  = atol(buf);
			tmpPro.lnCycleEventId = lnCycleEventId;			
			pQuery->GetValue(2,buf);
            tmpPro.nSetHour = atoi(buf);
            pQuery->GetValue(3,buf);
            tmpPro.nLatnId = atoi(buf);
			pQuery->GetValue(4,buf);
            strcpy(tmpPro.szLastDealTime , buf);
			pQuery->GetValue(5,buf);
            strcpy(tmpPro.szLastStartTime , buf);
			
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldCrossUser::initCycEvtPro","Get CycEvtPro: CycleEventId=[%ld] SetHour=[%d] LatnId=[%d] LastDealTime=[%s] LastStartTime=[%s]",
				lnCycleEventId,tmpPro.nSetHour,tmpPro.nLatnId,tmpPro.szLastDealTime,tmpPro.szLastStartTime );
			MapEvtType.insert(make_pair(lnCycleEventId,tmpPro));
        }
		pQuery->Close();
		
    }
    catch (UDBException& e)
    {
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldCrossUser::initCycEvtPro","InitCycEvtPro throw out abnormity! sql[%s] %s",sbuf.c_str(),e.ToString());
        return -1;	
    }
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldCrossUser::initCycEvtPro end");
    return 0;
}


//判断是否到定时任务处理时间
bool DCLoadOldCrossUser::IsNeedDeal(DCDateTime dtLast,DCDateTime dtcur,const int nSetHour)
{
	if(dtLast > dtcur)
		return false;

	char sztmp[10]={0};
	char szSetTime[16]={0};
	sprintf(sztmp, "%s", (dtcur.ToString(string("YYYYMMDDHHNNSS"))).c_str());
	sztmp[8]=0;
	sprintf(szSetTime,"%s%02d0000",sztmp,nSetHour);
	DCDateTime dtnext;
	dtnext.FromString(szSetTime,string("YYYYMMDDHHNNSS"));

	//跨账户更新流程无需校验上次开始时间
	if(dtLast < dtnext && (dtcur > dtnext || dtcur == dtnext))
		return true;
	
	return false;
}

int DCLoadOldCrossUser::SetLastDealTime(const DCDateTime cur_time,const STEventPro stEvn,bool isStart)
{
	
    long lnCycleEventId=stEvn.lnCycleEventId;	
	char szLastTime[16]={0};
	sprintf(szLastTime, "%s", (cur_time.ToString(string("YYYYMMDDHHNNSS"))).c_str());
	szLastTime[14]=0;	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"SetLastDealTime","szLastTime[%s] lnCycleEventId[%ld] isStart[%d]",szLastTime,lnCycleEventId,isStart);
	string sbuf,sqlname;
    try
    {
    	if(isStart)
			sqlname = "UpdateCycleEventStart";
		else
			sqlname = "UpdateCycleEvent";
        UDBSQL* pUpdate = p_dbm->GetSQL(sqlname.c_str());			
		if(pUpdate==NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "SetLastDealTime","GetSQL [%s] failed",sqlname.c_str());
			return -1;
		} 
        pUpdate->UnBindParam();
        pUpdate->BindParam(1,szLastTime);
        pUpdate->BindParam(2,lnCycleEventId);
		pUpdate->GetSqlString(sbuf);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"SetLastDealTime","Do SQL:Begin to do %s,sql :[%s]",sqlname.c_str(),sbuf.c_str());	
        
        pUpdate->Execute();
		pUpdate->Connection()->Commit();
		pUpdate->Close();
    }
    catch (UDBException& e)
    {
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"SetLastDealTime","sql[%s] %s",sbuf.c_str(),e.ToString());
        return -1;	
    }
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","SetLastDealTime end");
    return 0;
}

int DCLoadOldCrossUser::SetLastDealTimeDcfServ(const DCDateTime cur_time,const int nProType, const int nProcId, const int nLatnId, bool isStart)
{
	char szLastTime[16]={0};
	sprintf(szLastTime, "%s", (cur_time.ToString(string("YYYYMMDDHHNNSS"))).c_str());
	szLastTime[14]=0;	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"SetLastDealTimeDcfServ","szLastTime[%s]isStart[%d]nProType[%d]nProcId[%d]nLatnId[%d]",szLastTime,isStart,nProType,nProcId,nLatnId);
	string sbuf,sqlname;
    try
    {
    	if(isStart)
			sqlname = "UpdateCycleEventStartDcfServ";
		else
			sqlname = "UpdateCycleEventDcfServ";
        UDBSQL* pUpdate = p_dbm->GetSQL(sqlname.c_str());			
		if(pUpdate==NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "SetLastDealTimeDcfServ","GetSQL [%s] failed",sqlname.c_str());
			return -1;
		} 
        pUpdate->UnBindParam();
        pUpdate->BindParam(1,szLastTime);	//PRESENT_START_TIME
        pUpdate->BindParam(2,nProType);	//PROC_TYPE
		pUpdate->BindParam(3,nProcId);	//PROC_ID
		pUpdate->BindParam(4,nLatnId);	//LATN_ID
		pUpdate->GetSqlString(sbuf);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"SetLastDealTimeDcfServ","Do SQL:Begin to do %s,sql :[%s]",sqlname.c_str(),sbuf.c_str());	
        
        pUpdate->Execute();
		pUpdate->Connection()->Commit();
		pUpdate->Close();
    }
    catch (UDBException& e)
    {
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"SetLastDealTimeDcfServ","sql[%s] %s",sbuf.c_str(),e.ToString());
        return -1;	
    }
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"SetLastDealTimeDcfServ","end");
    return 0;
}


//提取套餐实例和明细,并根据明细判断是否有跨账户套餐,如果是跨账户,则插入tb_bil_cross_account_mid_表,跨账户更新进程会处理这张表
int DCLoadOldCrossUser::CheckCrossAcctOfrInst(int nLatnId)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldCrossUser::CheckCrossAcctOfrInst begin ");

	long lnOfrInstId=-99;
	set<long> setOfrInstId;
	char szEffDate[20] = {0};
	char szExpDate[20] = {0};
	long lnSpecialId=0;
	int nAcctType = 0;
	string strsql;
	int nRet=0;
	char szSqlName[64]={0};
	set<long> setAcctId;
	
	char *sCycleBeginTime = DCDataCenter::instance()->sCycleBeginTime;
	char *sCycleEndTime = DCDataCenter::instance()->sCycleEndTime;

	struct timeval tm;
	gettimeofday(&tm, NULL);
	unsigned long lnbegin = (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);
	unsigned long lnend = 0, lnUseTime = 0;
	int nTps=0;
	long lnCounts=0;

	try
	{
		sprintf(szSqlName , "GetAllOfrInst");
		UDBSQL*pQuery = p_dbm->GetSQL(szSqlName);
		if(pQuery == NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCLoadOldCrossUser::CheckCrossAcctOfrInst","not find sql[%s]",szSqlName);
			return -1;
		}
	
		pQuery->UnBindParam();
		pQuery->BindParam(1,nLatnId);
		pQuery->BindParam(2,m_inputParam.nProcNum);
		pQuery->BindParam(3,m_nProcId);
		pQuery->GetSqlString(strsql);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldCrossUser::CheckCrossAcctOfrInst","nProcNum[%d],m_nProcId[%d],Do SQL: begin to do [GetAllOfrInst],sql :[%s] ",m_inputParam.nProcNum,m_nProcId,strsql.c_str());

	    pQuery->Execute();
	    while(pQuery->Next())
	    {
	    	lnCounts++;
	    	pQuery->GetValue(2, szEffDate);
            pQuery->GetValue(3, szExpDate);
	    	//过滤失效记录
			if ((strcmp(szEffDate,"") != 0) && (strcmp(szExpDate,"") != 0))
			{
				if (0 == strncmp(szEffDate,szExpDate,8))
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldCrossUser::CheckCrossAcctOfrInst","OfrInstId Filter by EffDate[%s]=ExpDate[%s].",szEffDate,szExpDate);
					continue;
				}
				if (strncmp(sCycleBeginTime,szExpDate,8) > 0 || strncmp(sCycleEndTime,szEffDate,8) < 0)
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldCrossUser::CheckCrossAcctOfrInst","OfrInstId Filter by EffDate[%s] or ExpDate[%s] out of this month[%d].",szEffDate,szExpDate,DCDataCenter::instance()->iBillingCycleId);
					continue;
				}
			}
			
	    	pQuery->GetValue(1, lnOfrInstId);
	    	//一个实例只取一次
			if(setOfrInstId.find(lnOfrInstId)!=setOfrInstId.end())
			{
				continue;
			}
			setOfrInstId.insert(lnOfrInstId);
			m_pComboAdpt->m_setOfrInstId.insert(lnOfrInstId);
			DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCLoadOldCrossUser::CheckCrossAcctOfrInst","Get OfrInstId[%ld] to check",lnOfrInstId);
			DCDATLOG("RC00009:%ld",lnOfrInstId);
			
			//获取套餐明细
			nRet = m_pComboAdpt->GetOfrInstDetailInfo(lnOfrInstId, nLatnId);			
			if ( nRet < 0 )
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"DCLoadOldCrossUser::CheckCrossAcctOfrInst","GetOfrInstDetailInfo Find detail info failed,lnOfrInstId[%ld].",lnOfrInstId);
				return -1;
			}

			//判断是否有跨账户套餐
			setAcctId.clear();
			nAcctType = m_pComboAdpt->CheckSpecialCrossAcct(nLatnId, lnOfrInstId,false,lnSpecialId,setAcctId);
			if(nAcctType <0)
			{
				if(nAcctType==-2)//套餐实例没有找到归属账户
				{
					m_pComboAdpt->releaseUserMap(true);
					continue;
				}
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldCrossUser::CheckCrossAcctOfrInst","CheckSpecialAcct failed,OfrInstId[%ld].",lnOfrInstId);
				return -1;
			}
			m_pComboAdpt->releaseUserMap(true);
			
			if(lnCounts%1000==0)//每一万用户输出一次tps
			{
				gettimeofday(&tm, NULL);//性能日志
				lnend = (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);//性能日志
				lnUseTime = DIFFTIME_US(lnend, lnbegin);
				nTps=0;
				if(lnUseTime>0 && lnCounts>0)
					nTps =(int)((lnCounts*1000000.0)/lnUseTime);
				DCBIZLOG(DCLOG_LEVEL_INFO, 0,"TPS CheckCrossAcctOfrInst","LoadUseTime[%ldus] CheckOfrInstCounts[%ld][tps=%d] LatnId[%d]",lnUseTime,lnCounts,nTps,nLatnId);
				DCDATLOG("RC00010:%d!%ld!%ld!%d",nLatnId,lnUseTime,lnCounts,nTps);

                std::string strStat;
            	p_dbm->get_statistic()->to_string(strStat, true);	 //过滤未使用的
        		DCPERFLOG((int)lnUseTime,"LatnId[%d] LoadUseTime[%ldus] CheckOfrInstCounts[%ld][tps=%d] sqlperf:%s",nLatnId,lnUseTime,lnCounts,nTps,strStat.c_str());
            }
			if(lnCounts%30==0)
				DCDATLOG(); //主动触发日志输出
		}
		pQuery->Close();
		
	} 
	catch(std::exception& e)
	{		
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldCrossUser::CheckCrossAcctOfrInst","error info: [%s] sql[%s]",e.what(),strsql.c_str());
		return -1;
	}

	gettimeofday(&tm, NULL);//性能日志
	lnend = (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);//性能日志
	lnUseTime = DIFFTIME_US(lnend, lnbegin);
	nTps=0;
	if(lnUseTime>0 && lnCounts>0)
		nTps =(int)((lnCounts*1000000.0)/lnUseTime);
	std::string strStat;
	p_dbm->get_statistic()->to_string(strStat, true);	 //过滤未使用的
	DCBIZLOG(DCLOG_LEVEL_INFO, 0,"PERF RentLoad CheckCrossAcctOfrInst","LoadUseTime[%ldus] CheckOfrInstCounts[%ld][tps=%d] LatnId[%d] sqlperf:%s",lnUseTime,lnCounts,nTps,nLatnId,strStat.c_str());
	DCDATLOG("RC00011:%d!%ld!%ld!%d!%s",nLatnId,lnUseTime,lnCounts,nTps,strStat.c_str());
	DCPERFLOG((int)lnUseTime,"LatnId[%d] LoadUseTime[%ldus] CheckOfrInstCounts[%ld][tps=%d] sqlperf:%s",nLatnId,lnUseTime,lnCounts,nTps,strStat.c_str());
            
	//重置统计信息
	p_dbm->get_statistic()->reset();

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldCrossUser::CheckCrossAcctOfrInst end ");
	return 0;
}

int DCLoadOldCrossUser::UpdateDealState(const long lnPkid, const int nLatnId)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","UpdateDealState begin,lnPkid[%d]",lnPkid);

	// <sql name="UpdateDealStateMid" bind="22" sub="553,554,555,556,558,562,563,564,566">
	// 		update tb_bil_cross_account_mid_[@] 
	// 		SET deal_state=11 //业务预占
	// 		where pkid=? 
	// 		AND deal_state=21 //任务预占
	// </sql>

	int nAffect = 0;
    char sqlname[50] = {0};
    string sbuf;

	try
	{
		sprintf(sqlname, "UpdateDealStateMid|%d", nLatnId);
		UDBSQL* pUpdate = p_dbm->GetSQL(sqlname);
		if(pUpdate==NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "UpdateDealState","GetSQL [%s] failed",sqlname);
			return -1;
		} 
		pUpdate->UnBindParam();
		pUpdate->BindParam(1,lnPkid);

		pUpdate->GetSqlString(sbuf);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"UpdateDealState","Do SQL:Begin to do %s,sql :[%s]",sqlname,sbuf.c_str());
		
		pUpdate->Execute();
		nAffect = pUpdate->GetRowCount();
		pUpdate->Connection()->Commit();
		pUpdate->Close();
	}
	catch (UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"UpdateDealState","InitCycEvtPro throw out abnormity! sql[%s] %s",sbuf.c_str(),e.ToString());
		return -1;
	}

	if(0 == nAffect)
	{
		//未能取到对应任务的正确的表数据
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"UpdateDealState","GetRowCount[%d]Get task error!", nAffect);
		return -1;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","UpdateDealState end");
	return 0;
}

//从跨账户中间表取出跨账户信息，进行分组并合并到跨账户表（特殊账户分流表）//跨账户更新进程调用
int DCLoadOldCrossUser::UpdateSpecialCrossAcct(int nLatnId)
{
	string strsql;
    char szSqlName[64]={0};
	long lnQueryAcctId=-99,lnQueryOfrInstId=-99;
	try
	{
		sprintf(szSqlName,"GetAllCrossMiddle|%d",nLatnId);
		UDBSQL*pQuery = p_dbm->GetSQL(szSqlName);
		if(pQuery == NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCLoadOldCrossUser::UpdateSpecialCrossAcct","not find sql[%s]",szSqlName);
			return -1;
		}

		pQuery->GetSqlString(strsql);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldCrossUser::UpdateSpecialCrossAcct","Do SQL: begin to do [GetAllCrossMiddle],sql :[%s] ",strsql.c_str());
		
		pQuery->UnBindParam();
	    pQuery->Execute();
	    while(pQuery->Next())
	    {	
			pQuery->GetValue(1, lnQueryAcctId);
            pQuery->GetValue(2, lnQueryOfrInstId);

			m_pComboAdpt->SaveSpecialCrossAcct(lnQueryAcctId,lnQueryOfrInstId,nLatnId,m_bCrossAcctChgFlag);
	    }
		pQuery->Close();
	} 
	catch(std::exception& e)
	{		
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldCrossUser::UpdateSpecialCrossAcct","UpdateSpecialCrossAcct failed: [%s] sql[%s]",e.what(),strsql.c_str());
	}
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldCrossUser::UpdateSpecialCrossAcct end");
	return 0;
}



/*******************************************
 *Copyrights   2007，深圳天源迪科计算机有限公司
 *                   技术平台项目组
 *All rights reserved.
 *
 *Filename：
 *       DCPairManager.cpp
 *Indentifier：
 *
 *Description：
 *       序列值对超时处理线程
 *Version：
 *       V1.0
 *Author:
 *       YF.Du
 *Finished：
 *       2008年10月10日
 *History:
 *       2008/10/10  V1.0 文件创建
 ********************************************/
#include <stdlib.h>
#include "DCUidCacheManager.h"
#include "publiclib.h"

using namespace std;

DCUidCacheManager::DCUidCacheManager()
{
	m_pairList = NULL;
	m_pDbm = NULL;
    m_bLogBegin = true;
    m_writeLogCount = 0;
}

DCUidCacheManager::~DCUidCacheManager()
{
}

int DCUidCacheManager::init(DCPairList *pairList)
{
	m_pairList = pairList;

	char buf[512]={0};
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCUidCacheManager::init","Get config file from path:env[OCS_CONFIG].");
	const char* path = getenv("OCS_CONFIG");
	sprintf(buf, "%s/RentLoad.sql.xml", path);
	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0,"DCUidCacheManager::init", "start create_sql_info, path[%s]", buf);
	if(create_sql_info(buf) < 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCUidCacheManager::init", "create_sql_info failed, path[%s]", buf);
	    return -1;
	}
	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0,"DCUidCacheManager::init", "create_sql_info success");	
}


int DCUidCacheManager::create_sql_info(const char*sfile)
{
    m_pDbm = new DCDBManer();
    if(!m_pDbm)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 1,"DCUidCacheManager::create_sql_info", "create DCDBManer failed");
		return -1;
	}
	DCBIZLOG(DCLOG_LEVEL_DVIEW, 0,"DCUidCacheManager::create_sql_info", "start to init DCDBManer for sql file [%s]", sfile);
    int nRet = m_pDbm->Init(sfile);
    if(nRet < 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,1,"DCUidCacheManager::create_sql_info",  "init DCDBManer failed,ret[%d]",nRet);
		return nRet;
	}
	DCBIZLOG(DCLOG_LEVEL_DVIEW,0,"DCUidCacheManager::create_sql_info",  "end to init DCDBManer");
	return 0;
}


void DCUidCacheManager::routine()
{
	string struid, strsql;
	bool isWrong=false;
	int nConnectCnt = 0;
	char szSqlName[64]={0};
	SMsgPair msgpair; //回调消息
	bool bFirst = true;
    
    //日志表缓存数据
    int nBillingCycleId = 0;//YYYYMMDD
    int nwriteLogCount = 0;
    int nPrdInstNums = 0;
    int nSubInstNums = 0;
    int nOfrInstNums = 0;
    char szCurTime[16] = {0};
	while (1)
	{
		sleep(1);
		bFirst = true;
		while (m_pairList->UidQueOut(msgpair))
		{
			if (bFirst) // 首次重连数据库
			{
				m_pDbm->CheckReset();
				bFirst = false;
			}
			struid = msgpair.uuid;
			//更新日志,仅更新msgtype=0全量处理的
			if(msgpair.nMsgType==MSGTYPE_OLD && (msgpair.nPrdInstNum!=0 || msgpair.nSubInstNum!=0 || msgpair.nOfrInstNum!=0))
			{
                if (msgpair.nLogBillingCycleId != nBillingCycleId)
                {
                    nwriteLogCount = 0;
                    nPrdInstNums = 0;
                    nSubInstNums = 0;
                    nOfrInstNums = 0;
                    nBillingCycleId = msgpair.nLogBillingCycleId;
                }
			    nPrdInstNums += msgpair.nPrdInstNum;
                nSubInstNums += msgpair.nSubInstNum;
                nOfrInstNums += msgpair.nOfrInstNum;
                if(++nwriteLogCount % 50 == 0 || (!m_bLogBegin && nwriteLogCount>=m_writeLogCount))
                {
    				try
    				{
    					sprintf(szSqlName,"UpdateRentSendLog|%d",msgpair.nLatnId);
    					UDBSQL*pExec = m_pDbm->GetSQL(szSqlName);
    					if(pExec == NULL)
    					{
    						DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCUidCacheManager::routine","not find sql[%s]",szSqlName);
    						break ;
    					}                    
                        PublicLib::GetTime(szCurTime, YYYYMMDDHHMMSS);
    					pExec->UnBindParam();
    					pExec->BindParam(1,nPrdInstNums);
    					pExec->BindParam(2,nSubInstNums);
    					pExec->BindParam(3,nOfrInstNums);
    					pExec->BindParam(4,szCurTime);
    					pExec->BindParam(5,msgpair.lnPidId);
    					pExec->BindParam(6,msgpair.nLogBillingCycleId);

    					pExec->GetSqlString(strsql);
    					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCUidCacheManager::routine","Do SQL: begin to do [UpdateRentSendLog],sql :[%s] ",strsql.c_str());

    				    pExec->Execute();
    				    if(pExec->GetRowCount()>0)
    				    {	
    						pExec->Connection()->Commit();
    				        //break ;
    				    }else
    				    {
    						pExec->Connection()->Rollback();
    				    }
    					pExec->Close();
    				} 
    				catch(std::exception& e)
    				{		
    					DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCUidCacheManager::routine","UpdateRentSendLog failed: [%s] sql[%s]",e.what(),strsql.c_str());
    					isWrong = true;
    					break;
    				}
                }
			}

			//更新账户变更表
			if((MSGTYPE_TRANSFER == msgpair.nMsgType) && msgpair.vecTransferId.size()>0)
			{
				try
				{
					long lnTransferId=0;
					for(int index=0;index<msgpair.vecTransferId.size();index++)
					{
						lnTransferId = msgpair.vecTransferId[index];
						sprintf(szSqlName,"UpdateTransferPrdAcct|%d",msgpair.nLatnId);
						UDBSQL*pExec = m_pDbm->GetSQL(szSqlName);
						if(pExec == NULL)
						{
							DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCUidCacheManager::routine","not find sql[%s]",szSqlName);
							break ;
						}
						pExec->UnBindParam();
						pExec->BindParam(1,1);
						pExec->BindParam(2,lnTransferId);

						pExec->GetSqlString(strsql);
						DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCUidCacheManager::routine","lnTransferId[%ld] Do SQL: begin to do [UpdateTransferPrdAcct],sql :[%s] ",lnTransferId,strsql.c_str());

					    pExec->Execute();
					    if(pExec->GetRowCount()>0)
					    {	
							pExec->Connection()->Commit();
					        //break ;
					    }else
					    {
							pExec->Connection()->Rollback();
					    }
						pExec->Close();
					}
				} 
				catch(std::exception& e)
				{		
					DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCUidCacheManager::routine","UpdateTransferPrdAcct failed: [%s] sql[%s]",e.what(),strsql.c_str());
					isWrong = true;
					break;
				}
			}

			//更新立即出账表			
			if((MSGTYPE_IMMED == msgpair.nMsgType) && msgpair.vecTransferId.size()>0)
			{
				for(int index=0;index<msgpair.vecTransferId.size();index++)
				{		
					int nState = IMMED_STATE_INIT;
					STImmedUser stImmedUser;
					stImmedUser.latn_id = msgpair.nLatnId;
					stImmedUser.acct_id = msgpair.lnSpecialId;
					stImmedUser.itemoutimmed_id = msgpair.vecTransferId[index];
					if (msgpair.nCallBackRetCode != 0)
					{
						nState = IMMED_STATE_FAILED;
					}
					else
					{
						nState = IMMED_STATE_SUCCESS;
					}
					UpdateInstDealed(stImmedUser,nState);
				}
			}			
			
			//先查DCA缓存
			try
			{
				UDBSQL*pQuery = m_pDbm->GetSQL("QueryCacheBill");
				if(pQuery == NULL)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCUidCacheManager::routine","not find sql[QueryCacheBill]");
					break ;
				}
				pQuery->UnBindParam();
				pQuery->BindParam(1,struid.c_str());
				pQuery->GetSqlString(strsql);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCUidCacheManager::routine","struid[%s],Do SQL: begin to do [QueryCacheBill],sql :[%s] ",struid.c_str(),strsql.c_str());

			    pQuery->Execute();
				if(!pQuery->Next())
				{
					pQuery->Close();
					continue;
				}
				pQuery->Close();
			} 
			catch(std::exception& e)
			{		
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCPairList::out","QueryCacheBill failed: [%s] sql[%s]",e.what(),strsql.c_str());
				isWrong = true;
				break;
			}
			//删除DCA缓存
			try
			{
				UDBSQL*pExec = m_pDbm->GetSQL("DeleteCacheBill");
				if(pExec == NULL)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCUidCacheManager::routine","not find sql[DeleteCacheBill]");
					return ;
				}
				pExec->UnBindParam();
				pExec->BindParam(1,struid.c_str());
				pExec->GetSqlString(strsql);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCUidCacheManager::routine","struid[%s],Do SQL: begin to do [DeleteCacheBill],sql :[%s] ",struid.c_str(),strsql.c_str());

			    pExec->Execute();
				pExec->Connection()->Commit();
				pExec->Close();
			} 
			catch(std::exception& e)
			{		
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCUidCacheManager::routine","DeleteCacheBill failed: [%s] sql[%s]",e.what(),strsql.c_str());
				isWrong = true;
				break;
			}
			//清除dca传递消息
			if(msgpair.nBillingFlag == 4)
			{
				try
				{
					UDBSQL *pExec = m_pDbm->GetSQL("DeleteBigAcctMsg");
					if(NULL == pExec)
					{
				        DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Not find SQL name:[DeleteBigAcctMsg]");
						return;
					}
					pExec->UnBindParam();
					pExec->BindParam(1, msgpair.lnSpecialId);
					pExec->BindParam(2, msgpair.nMessagesource);
					pExec->BindParam(3, msgpair.nBatchNo);
					pExec->GetSqlString(strsql);
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCUidCacheManager::routine","lnSpecialId[%ld] nMessagesource[%d] nBatchNo[%d],Do SQL: begin to do [DeleteBigAcctMsg],sql :[%s] ",msgpair.lnSpecialId,msgpair.nMessagesource,msgpair.nBatchNo,strsql.c_str());

				    pExec->Execute();
					pExec->Connection()->Commit();
					pExec->Close();
				}
				catch(UDBException& e)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCUidCacheManager::routine","DeleteBigAcctMsg failed: [%s] sql[%s]",e.what(),strsql.c_str());
					isWrong = true;
					break;
				}
			}
		}
		if(isWrong)
		{
			nConnectCnt=0;
			while(nConnectCnt++ < 3)
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCUidCacheManager::routine","sleep 5 seconds");
				sleep(5);
				int nRet = m_pDbm->CheckReset();	
				if(nRet < 0)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR,nRet,"DCUidCacheManager::routine","ReConnect failed.");
					continue;
				}
				else
				{			
					DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCUidCacheManager::routine","ReConnect success!");
					isWrong = false;
					break ;
				}
			}
		}
	}
}


int DCUidCacheManager::UpdateInstDealed(STImmedUser &stImmedUser, int nState)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","UpdateInstDealed begin,stImmedUser[%s],nState[%d]",stImmedUser.dump_to_string().c_str(),nState);
	int nCount = 0,nRet = 0;
	string strSqlText = "";
	char sqlname[64] = {0};
	sprintf(sqlname,"UpdateImmedState|%d",stImmedUser.latn_id);
	UDBSQL *pml = m_pDbm->GetSQL(sqlname);
	if (pml == NULL)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Not find Sql [%s]", sqlname);
		return FAIL_NOT_FIND_SQLNAME;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Get Sql [%s] OK", sqlname);
	while(nCount < NUMBER_OF_CYCLES)
	{
		try
		{			
	        pml->UnBindParam();
			pml->BindParam(1,nState);
		    pml->BindParam(2,stImmedUser.itemoutimmed_id);
		    pml->GetSqlString(strSqlText);            
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "[%s][%s]", sqlname,strSqlText.c_str());
	        pml->Execute();
	        int nAffectRows = pml->GetRowCount();
	        if(nAffectRows > 0)
	        {
	            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "update success! affected row count[%d]", nAffectRows);
	        }
	        else
	        {
	            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "do not update any record, affected row count[%d]", nAffectRows);
	        }
	        pml->Connection()->Commit();		
	        pml->Close();
		}
		catch (UDBException &e)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "[%s][%s]", sqlname,e.ToString());
			try
			{
				pml->Connection()->Rollback();
			}
			catch(...){}
			m_pDbm->CheckReset();
			nCount++;
			nRet = FAIL_UPDATE_ITEM_OUT_IMMED_USER;
			continue;
		}
		nRet = 0;
		break;
	}
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","UpdateInstDealed end,stImmedUser[%s],nState[%d]",stImmedUser.dump_to_string().c_str(),nState);
	return nRet;
}



void DCUidCacheManager::SetLogBegin(bool bLogBegin,int writeLogCount)
{
    m_bLogBegin = bLogBegin;
    m_writeLogCount = writeLogCount;
}


   /*-------------------------------------------------------------------
  ---                                                             ---
  ---            <PERSON>zhen Tianyuan DIC Comp. Ltd.                 ---
  ---            Programmed by <PERSON>                     ---
  ---            Refreshed time: 1999-09-22                       ---                               
  ---            Communication telephone: +81-0755-3329693        ---
  ---            e-mail: <EMAIL>                          ---
  ---  
  -------------------------------------------------------------------*/
/* utilLocal.h included all functions used ususlly                
   getCurrentDate(...) return current day in format: "yyyy-mm-dd:hh:mm:ss"
   isWeekDay(...) return whick day of a week: 0-Sunday,1-Monday,...,6-Saterday 
   isStringCode(..) judges whether the input parameter is code comprised by '0'-'9','A'-'Z' and 'a'-'z'
   if true return 1;
   else return 0;    */ 
#ifndef __UTILLOCAL_H__
#define __UTILLOCAL_H__

#include <stdio.h>
#include <string.h>
#include "publiclib.h"



#ifdef __cplusplus
extern "C" {
#endif


#define ALL_DIGITAL 1
#define ALL_LETTER 2
#define SQLNOTFOUND 1403

static char _weekday[][4]={"Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"};
static char _month[][4]={"Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"};

extern void getHHMMSS(const char *time_string,int *hour,int *min,int *sec,char flag);
extern void getCurrentDate(char* v_date);
extern void getWeekDay(int *v_week);
extern void dateToWeek(const char* v_date,int *v_week);
extern int isStringCode(const char* v_code,int v_flag);
extern int authenticate(const char* session_name);
extern void timeAdd(const char* begin_time,int seconds,char* end_time);
extern void selftimeAdd(char* begin_time,int seconds);
extern int secondsBetween(const char* begin_time,const char* end_time);
extern int timeCount(const char * begin_time,int *duration1,const char* offset,int *duration2);
extern int monthBetween(int offset1,int duration1,const char* offset2,int duration2,const char* begin_time,int call_duration);
extern int seasonBetween(int offset1,int duration1,const char* offset2,int duration2,const char* begin_time,int call_duration);
extern int yearBetween(int offset1,int duration1,const char* offset2,int duration2,const char* begin_time,int call_duration);
extern int halfyearBetween(int offset1,int duration1,const char* offset2,int duration2,const char* begin_time,int call_duration);
extern int timeBetween(const char* unit,int offset1,int duration1,const char* offset2,int duration2,const char* begin_time,int call_duration);
extern void methodCalculate(const char* method,int quantity,int in_value,int* out_value);

/* convert the seconds since 1970 to the datetime 'yyyymmddhh24miss */
extern void toDateTime(time_t seconds,char * dateTime,short typeHint);

/*extern void getFileName(char *path,char* file_name);*/
extern void stringTrim(char *str);

extern void stringLTrim(char *str);
extern void stringRTrim(char *str);
extern void stringAllTrim(char *str);
extern int monthRent(const char *time_unit,int count,const char* start_date,const char* end_date);

extern void getLBSConfig(char* result,const char* flag, int* status);
extern void getDCMConfig(char* result,const char* flag, int* status);

/*extern void lastDayOfMonth(int &month,int &day);*/
extern void getFileName(char *path,char *file_name);
extern int isTime(const char*time);

extern int writeHead(FILE *fp);
extern int writeHeader(int fid);
extern int writeCall(FILE *fp,short cur_month);
extern int writeCall2(FILE *fp,short cur_month);
extern int writeCharge(FILE *fp,short cur_month);
extern int writeChargebk(FILE *fp,short cur_month);
extern int writeMeterbk(FILE *fp,short cur_month);
extern int writeMeterbk_hf(FILE *fp,short cur_month);
extern int writeCallbk(FILE *fp,short cur_month);
extern int writeSettbk(FILE *fp,short cur_month);
extern int writeHeadMT(FILE *fp,short cur_month);
extern int writeHeadAMS(FILE *fp);
extern int writeHeadUC(FILE *fp,short cur_month);
extern int writeHeadST(FILE *fp,short cur_month);
extern int findPidByName(const char *);
extern void formatDate(char *str);


//通过程序名称查进程数
extern int findProcessByName(const char * name);


/****************************************************************************************
*@intout		v_ListNumber：数组指针
*@intout		v_ListLen：数组长度
*@input			v_FindNumber: 需要找的数    
*@return		:(1)<0 没有找到 （3）>=0 所在数据下标的位置
*@description   :用二分发查找
******************************************************************************************/
extern int lFindWithHalf(const long *v_ListNumber,const long &v_ListLen,const long &v_FindNumber);
extern int iFindWithHalf(const int *v_ListNumber,const int &v_ListLen,const int &v_FindNumber);

/****************************************************************************************
*@intout			_UserId：用户名,格式iba/iba@ora817
*@intout			_ControlFile:控制文件名（含路径）
*@return			true:导入成功
*@description   	sqlldr导入数据
******************************************************************************************/
extern bool ExecSqlload(const char *_UserId,const char *_ControlFile);
extern bool ExecSqlloadTrue(const char *_UserId,const char *_ControlFile);

extern long Power(const int &v_a,const int &v_b);
extern bool CompareChar(const char *v_a,const char *v_b,const int &v_Operators);

//尾数处理方式
extern long GetInt(const double &v_f, const char &v_Mode);

//执行任务
extern int StartProg(const char *v_Command);

extern void nstrcpy(char *v_TrgStr,const char *v_OriStr,const int &v_Len);

//对象比较
extern bool CompareValues(const char *v_a,const char *v_b,const char v_VarType,const int &v_Operators);


//判断字符串是否合法IP地址
extern bool isIpAddress(const char *v_str);

/**----------------------------------------------------------------   
	 	* @param 		char *str :目标串 
		* @return       char *:结果   <br>
		* @description	功能描述: 将字符串全部转换为大写字符 <br>  
------------------------------------------------------------------*/
char *strupr(char *str);

#ifdef __cplusplus
}
#endif


#endif




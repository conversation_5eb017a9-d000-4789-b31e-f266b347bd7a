#include <utility>
#include <unistd.h>
#include <sys/types.h>
#include <time.h>
#include "DCParseXml.h"
#include "DCLoadOldUser.h"
#include "DCPerfStatistic.h"

#define DIFFTIME_US(Tb, Ta) ((((Tb)>>24) - ((Ta)>>24))*1000000 + ((Tb)&0xFFFFFF) - ((Ta)&0xFFFFFF))

using namespace std;
using namespace ocs;


DCLoadOldUser::~DCLoadOldUser()
{
	if(!m_bDcfServMode)
	    SAFE_DELETE_PTR(m_pCallServer);
}

int DCLoadOldUser::InitSubLatnId()
{
	//获取本地网列表
	m_ltLatn.clear();
    char szLatn[5] = {0};
    sprintf(szLatn,"%d",m_inputParam.nLatnID);
    m_ltLatn.push_back(szLatn);
    m_nLatnId = m_inputParam.nLatnID;
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "InitSubLatnId:%d",m_inputParam.nLatnID);

	map<string, STConfigPara>::iterator iterCfg;
	iterCfg = m_mapLatnCfg.find(m_inputParam.strLatn);
	if(iterCfg != m_mapLatnCfg.end())
	{
		m_cfgPara = iterCfg->second;
	}

	//初始化套餐匹配模块
	m_pComboAdpt->setInputPara(m_inputParam,m_cfgPara);

	//初始化套餐匹配模块
	map<string, DCCallZKServerMaker*>::iterator iter1;
	iter1 = m_mappCallServer.find(m_inputParam.strLatn);
    if(iter1 != m_mappCallServer.end())
	{
		m_pCallServer = iter1->second;
	}

	//对应于大屏后台程序的处理方式
	if(m_ptrBPMon)
	    m_ptrBPMon = DCKpiSender::instance()->GetKPIHandle("BILLING", "REC", "RENDLOAD_B");

	DCBIZLOG(DCLOG_LEVEL_INFO, 0,"","InitSubLatnId success");
	DCDATLOG("RB00001:");
	return 0;
}

//初始化
int DCLoadOldUser::InitSub()
{
	//获取本地网列表
	string latnId = m_cfgPara.latnList;	
	m_ltLatn.clear();
    PublicLib::SplitStr(latnId.c_str(),'|',m_ltLatn);

    if (m_ltLatn.empty())
    {		
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::InitSub","Configuration item of %s is null",m_inputParam.strLatn.c_str());
        return -1;
    }
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldUser::InitSub", "%s is[%s] get size:%d",m_inputParam.strLatn.c_str(),latnId.c_str(),m_ltLatn.size());


	//初始化埋点
	m_ptrBPMon = DCKpiSender::instance()->GetKPIHandle("BILLING", "REC", "RENDLOAD_B");//对应于大屏后台程序的处理方式
	if (m_ptrBPMon)
	{
		DCKpiSender::instance()->group_all_init(m_ptrBPMon, "TraceKpi", m_ltLatn);
	}
	else
	{		
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","monitor init fail. error code:%d, error info:%s",DCKpiSender::instance()->ErrorCode(),DCKpiSender::instance()->ErrorInfo()); 	
		return -1;
	}
	
	//初始化套餐匹配模块
	m_pCallServer = NULL;
	m_pCallServer = new DCCallZKServerMaker();
	int nRet = m_pCallServer->init(m_cfgPara);
	if (nRet < 0)
	{
        DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"","DCLoadOldUser::InitSub, init DCCallZKServerMaker failed, error code[%d]", nRet);
		return -1;
	}
	DCBIZLOG(DCLOG_LEVEL_INFO, 0,"","DCLoadOldUser::InitSub success");
	DCDATLOG("RB00001:");
	return nRet;
}

//统一分发模式处理函数
int DCLoadOldUser::processDcfs()
{
    if(1 == m_nTaskState)
    {
        int nRet = 0;
        //取配置
        nRet = InitSubLatnId();
        if(nRet < 0)
        {
            DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "processDcfs","InitSub fail");
            SetTaskState(0);
            return -1;
        }

        if (ReConnect()) //重连失败
        {
            DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "ReConnect failed,exit");
            SetTaskState(0);
            return -1;
        }

        //处理新装用户和变更
        if(PROC_TYPE_INST_CHANGE == m_inputParam.nProType || 
            PROC_TYPE_TRANSFER_PRD == m_inputParam.nProType)
        {
            nRet = DealNewUserDcfServ();
            if(nRet <0)
            {
                DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "processDcfs","DealNewUserDcfServ failed");
                SetTaskState(0);
                return -1;
            }
            m_inputParam.nProType = PROC_TYPE_INST_CHANGE;
        }

        if(PROC_TYPE_FREZEN_USER == m_inputParam.nProType)  //预付费
        {
            //处理复机用户
            m_inputParam.nProType = PROC_TYPE_PRE_NORMAL;
            nRet = DealFrezenUserDcfServ();
            if(nRet <0)
            {
                DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "processDcfs","DealFrezenUserDcfServ failed");
                SetTaskState(0);
                return -1;
            }
            m_inputParam.nProType = PROC_TYPE_FREZEN_USER;
        }
        
        //处理老用户(预付+后付)
        if(PROC_TYPE_POST_NORAML == m_inputParam.nProType ||
            PROC_TYPE_PRE_NORMAL == m_inputParam.nProType)
        {
            nRet = DealOldUserDcfServ();
            if(nRet <0)
            {   
                DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "processDcfs","DealOldUserDcfServ failed");  
                SetTaskState(0);
                return -1;
            }
        }

        if(PROC_TYPE_TRIAL_USER == m_inputParam.nProType)
        {
            m_inputParam.nProType = PROC_TYPE_PRE_NORMAL;//预付费
            //处理试算用户
            nRet = DealTrialUserDcfServ();
            if(nRet <0)
            {   
                DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "processDcfs","DealTrialUserDcfServ failed");    
                SetTaskState(0);
                return -1;
            }
            m_inputParam.nProType = PROC_TYPE_TRIAL_USER;
        }
        
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "processDcfs end");
        
        DCDATLOG(); //主动触发日志输出
        SetTaskState(0);
    }//if(1 == m_nTaskState)
    return 0;
}

//处理函数
int DCLoadOldUser::process()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "DCLoadOldUser::process begin. procid:%d",m_nProcId);

	int nRet = 0;	

	//新装流程开始前变更消息控制队列大小
	RentUserInfo stRentInfo;
	stRentInfo.stAcctHead.user_mode = USER_NEW;
	nRet = m_pCallServer->process(&stRentInfo,MSGCTRL_MODE_LOADNEWBEG,p_dbm);
	if(nRet < 0)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::process","call CallZKServerMaker process fail");
    } 
				
	//处理新装用户
	nRet = DealNewUser();		
	if(nRet <0)
	{   
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::process","DealNewUser failed");	
		return -1;			
	}  
	if(PROC_TYPE_PRE_NORMAL == m_inputParam.nProType)  //预付费
	{
        //处理复机用户
	    nRet = DealFrezenUser();		
	    if(nRet <0)
	    {   
		    DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::process","DealFrezenUser failed");	
		    return -1;			
	    }
	}
	
	//处理老用户
	nRet = DealOldUser();		
	if(nRet <0)
	{   
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::process","DealOldUser failed");	
		return -1;			
	}

	if(PROC_TYPE_PRE_NORMAL == m_inputParam.nProType)  //预付费
	{
		//处理试算用户
		nRet = DealTrialUser();
		if(nRet <0)
		{   
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::process","DealTrialUser failed");	
			return -1;			
		}
	}
	
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "DCLoadOldUser::process end");
	
	DCDATLOG(); //主动触发日志输出		
	return 0;
}

//获取定时任务配置表信息
int DCLoadOldUser::initCycEvtPro(map<long,STEventPro> &MapEvtType,int nProcId,int nProcType)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::initCycEvtPro begin,nProcId[%d] nProcType[%d]",nProcId,nProcType);
    long lnCycleEventId=0;
    char buf[32];
	string sbuf;

    try
    {
        UDBSQL* pQuery = p_dbm->GetSQL("QueryCycleEvent");			
		if(pQuery==NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::initCycEvtPro","GetSQL [QueryCycleEvent] failed");
			return -1;
		} 
        pQuery->UnBindParam();
        pQuery->BindParam(1,nProcId);
        pQuery->BindParam(2,nProcType);
		pQuery->GetSqlString(sbuf);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldUser::initCycEvtPro","Do SQL:Begin to do QueryCycleEvent,sql :[%s]",sbuf.c_str());	
        
        pQuery->Execute();

		STEventPro tmpPro;
        while(pQuery->Next())
        {	
			pQuery->GetValue(1,buf);
            lnCycleEventId  = atol(buf);
			tmpPro.lnCycleEventId = lnCycleEventId;			
			pQuery->GetValue(2,buf);
            tmpPro.nSetHour = atoi(buf);
            pQuery->GetValue(3,buf);
            tmpPro.nLatnId = atoi(buf);
			pQuery->GetValue(4,buf);
            strcpy(tmpPro.szLastDealTime , buf);
			pQuery->GetValue(5,buf);
            strcpy(tmpPro.szLastStartTime , buf);
			
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldUser::initCycEvtPro","Get CycEvtPro: CycleEventId=[%ld] SetHour=[%d] LatnId=[%d] LastDealTime=[%s] LastStartTime=[%s]",
				lnCycleEventId,tmpPro.nSetHour,tmpPro.nLatnId,tmpPro.szLastDealTime ,tmpPro.szLastStartTime );
			MapEvtType.insert(make_pair(lnCycleEventId,tmpPro));
        }
		pQuery->Close();
		
    }
    catch (UDBException& e)
    {
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldUser::initCycEvtPro","InitCycEvtPro throw out abnormity! sql[%s] %s",sbuf.c_str(),e.ToString());
        return -1;	
    }
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::initCycEvtPro end");
    return 0;
}

//处理复机用户
int DCLoadOldUser::DealFrezenUser()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::DealFrezenUser begin");

	int nRet = 0;

	std::list<string>::iterator it = m_ltLatn.begin();
	for(; it != m_ltLatn.end(); it++)
	{
        m_nLatnId = atoi((*it).c_str());
		//如果账期或本地网变更,则重新刷新账期
		char szTime[16]={0};
		PublicLib::GetTime(szTime, YYYYMMDD);
		int nBillingCycleID = atoi(szTime);
		if (m_nBillingCycleId != nBillingCycleID || m_nLatnId!=DCDataCenter::instance()->m_nLatnId)
		{
			m_nBillingCycleId = nBillingCycleID;
			DCDataCenter::instance()->RefreshBillingCycle((int)(nBillingCycleID/100),m_nLatnId);
			if(!((m_pComboAdpt->m_pEventType)->reloadCondition()))
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCLoadOldUser::DealFrezenUser","DCRDEventType reloadCondition error\n");
				return -1;
			}
		}	

		nRet = LoadFrozenUser(m_nLatnId);
		if(nRet<0)
		{	
			if(ReConnect()) //重连失败
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "",	"ReConnect failed,exit");	
				return -1;
			}	
			else  //重连成功,需要重新加载用户
			{						
				DCBIZLOG(DCLOG_LEVEL_INFO, 0,"", "ReConnect success,load frozen user once again");
				DCDATLOG("RB00002:");
				nRet = LoadFrozenUser(m_nLatnId);				
				if(nRet<0)
				{							
					DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"", "load frozen user failed again");
					return -1;
				}
			}
		}		
				
	}

	DCBIZLOG(DCLOG_LEVEL_INFO, 0,"","Send Frozen user finished,sleep %d s and then deal old user",m_cfgPara.nFrozenUserSleepTime);
	DCDATLOG("RA00002:");
	ACE_Time_Value aSleepS(m_cfgPara.nFrozenUserSleepTime);
	ACE_OS::sleep(aSleepS); 
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::DealFrezenUser end");
    return 0;
}

//处理复机用户
int DCLoadOldUser::DealFrezenUserDcfServ()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::DealFrezenUser begin");

	int nRet = 0;
    
	//如果账期或本地网变更,则重新刷新账期
	char szTime[16]={0};
	PublicLib::GetTime(szTime, YYYYMMDD);
	int nBillingCycleID = atoi(szTime);
	if (m_nBillingCycleId != nBillingCycleID || m_nLatnId!=DCDataCenter::instance()->m_nLatnId)
	{
		m_nBillingCycleId = nBillingCycleID;
		DCDataCenter::instance()->RefreshBillingCycle((int)(nBillingCycleID/100),m_nLatnId);
		if(!((m_pComboAdpt->m_pEventType)->reloadCondition()))
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCLoadOldUser::DealFrezenUser","DCRDEventType reloadCondition error\n");
			return -1;
		}
	}

	nRet = LoadFrozenUserDcfServ(m_nLatnId, m_inputParam.vecSQLFields);
	if(nRet<0)
	{	
		if(ReConnect()) //重连失败
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "",	"ReConnect failed,exit");	
			return -1;
		}
		else  //重连成功,需要重新加载用户
		{
			DCBIZLOG(DCLOG_LEVEL_INFO, 0,"", "ReConnect success,load frozen user once again");
			DCDATLOG("RB00002:");
			nRet = LoadFrozenUserDcfServ(m_nLatnId, m_inputParam.vecSQLFields);
			if(nRet<0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"", "load frozen user failed again");
				return -1;
			}
		}
	}

	DCBIZLOG(DCLOG_LEVEL_INFO, 0,"","Send Frozen user finished,sleep %d s and then deal old user",m_cfgPara.nFrozenUserSleepTime);
	DCDATLOG("RA00002:");
	ACE_Time_Value aSleepS(m_cfgPara.nFrozenUserSleepTime);
	ACE_OS::sleep(aSleepS); 
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::DealFrezenUser end");
	return 0;
}

//处理老用户
int DCLoadOldUser::DealOldUser()
{
	map<long,STEventPro> MapEvtType ;
	DCDateTime last_time,cur_time,last_start_time;
	char szCurTime[16]={0};
	vector<long> vecInvaildRec;
	int nRet = 0;
	ACE_Time_Value aSleepS(m_cfgPara.nOldUserSleepTime);
	
	//定时任务数据刷新数据
	initCycEvtPro(MapEvtType,m_nProcId,m_inputParam.nProType);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldUser::DealOldUser","MapEvtType size is [%d]",MapEvtType.size());
	
	for (std::list<string>::iterator iterLatnId = m_ltLatn.begin(); iterLatnId != m_ltLatn.end(); ++iterLatnId)
	{ 
		m_nLatnId =  atoi((*iterLatnId).c_str());
			
		map<long,STEventPro>::iterator iter = MapEvtType.begin();
		for(;iter!=MapEvtType.end();iter++)
		{
			if(m_nLatnId!=(iter->second).nLatnId)
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldUser::DealOldUser","continue this latn_id! [input latn_id=%d] [config latn_id=%d]",m_nLatnId,(iter->second).nLatnId);
				continue;
			}
			last_time.FromString((iter->second).szLastDealTime,"YYYYMMDDHHNNSS");
			last_start_time.FromString((iter->second).szLastStartTime,"YYYYMMDDHHNNSS");
			memset(szCurTime,0x00,sizeof(szCurTime));
			PublicLib::GetTime(szCurTime, YYYYMMDDHHMMSS);
			cur_time.FromString(szCurTime,"YYYYMMDDHHNNSS");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldUser::DealOldUser","load old user! [CycleEventId=%ld] [LastDealTime=%s] [CurTime=%s] [SetHour=%d] [LatnId=%d]",
				(iter->second).lnCycleEventId,(iter->second).szLastDealTime,szCurTime,(iter->second).nSetHour,(iter->second).nLatnId);
				
			if(IsNeedDeal(last_time,cur_time,(iter->second).nSetHour,last_start_time))
			{
				char szCurrDate[9]={0};
				strncpy(szCurrDate,cur_time.ToString(string("YYYYMMDDHHNNSS")).c_str(),8);
				int nCurrDate = atoi(szCurrDate);
				//如果日期或本地网变更,则重新刷新账期
				int nBillingCycleID = (int)(nCurrDate/100);
				if (m_nBillingCycleId != nCurrDate || m_nLatnId!=DCDataCenter::instance()->m_nLatnId)//刷新时间到天 
				{
				    if (m_nBillingCycleId != nCurrDate)
                    {
                        m_writeLogCount = 0;
                        m_nPrdInstNum = 0;
                        m_nSubInstNum = 0;
                        m_nOfrInstNum = 0;
                    }
					m_nBillingCycleId = nCurrDate;
					DCDataCenter::instance()->RefreshBillingCycle(nBillingCycleID,m_nLatnId);
					if(!((m_pComboAdpt->m_pEventType)->reloadCondition()))
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCLoadOldUser::DealOldUser","DCRDEventType reloadCondition error\n");
						return -1;
					}
				}	

				//更新跨账户表和过户表信息
				if( m_pComboAdpt->LoadSpecialAcct(m_nLatnId)<0)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldUser::DealOldUser","LoadSpecialAcct failed, LatnId[%d].",m_nLatnId);
					return -1;
				}
				
				if( m_pComboAdpt->LoadTransferPrdAcct(m_nLatnId,vecInvaildRec)<0)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldUser::DealOldUser","LoadTransferPrdAcct failed, LatnId[%d].",m_nLatnId);
					return -1;
				}
			
				//填写开始处理时间	
				SetLastDealTime(cur_time,iter->second,true);
                m_pComboAdpt->releaseUserMap(true);
				//每次老用户处理前先清理队列
				RentUserInfo stRentInfo;
				nRet = m_pCallServer->process(&stRentInfo,MSGCTRL_MODE_LOADOLDBEG,p_dbm);
				if(nRet < 0 )
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::DealOldUser","call DCCallZKServerMaker::process fail");
				} 

                p_dbm->CheckReset(); // 重连mysql，西藏查资料与定价不同的mysql，这里主动重连一下
                //处理老用户--非跨账户
				nRet = loadOldUser(cur_time,m_nLatnId,false);//增加试算标识
				if(nRet<0)
				{	
					if(ReConnect()) //重连失败
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::DealOldUser",	"ReConnect failed,exit");	
						return -1;
					}	
					else  //重连成功,需要重新加载用户
					{						
						DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCLoadOldUser::DealOldUser", "ReConnect success,load old user once again");
						DCDATLOG("RB00002:");
						nRet = loadOldUser(cur_time,m_nLatnId,false);//增加试算标识				
						if(nRet<0)
						{							
							DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"DCLoadOldUser::DealOldUser", "load old user failed again");
							return -1;
						}
					}
				}	

				DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCLoadOldUser::DealOldUser","Send normal old user finished,sleep 10s and then deal cross old user");
				DCDATLOG("RB00004:");
				ACE_OS::sleep(aSleepS); 
				
				//处理老用户--跨账户
				nRet = loadOldCrossUser(cur_time,m_nLatnId,false);
				if(nRet<0)
				{   
			        if(ReConnect()) //重连失败
			        {
				        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldCrossUser::DealOldUser",	"ReConnect failed,exit");	
				        return -1;
			        }	
					else  //重连成功,需要重新加载用户
					{						
					    DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCLoadOldCrossUser::DealOldUser", "ReConnect success,load old crossuser once again");
						DCDATLOG("RC00003:");
						nRet = loadOldCrossUser(cur_time,m_nLatnId,false);				
						if(nRet<0)
						{							
					        DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"DCLoadOldCrossUser::DealOldUser", "load old crossuser failed again");
						    return -1;
						}
					}					
				}
                
				WriteRentLog(m_nLatnId,self(),0,0,0,true);
                stRentInfo.nGroupNum = m_writeLogCount;
                m_pCallServer->process(&stRentInfo, MSGCTRL_MODE_LOADOLDEND,p_dbm);

				DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCLoadOldUser::DealOldUser","Send cross user finished,sleep 10s and then deal next flow");
				DCDATLOG("RC00005:");
				ACE_OS::sleep(aSleepS);
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCLoadOldUser::DealOldUser", "IsNeedDeal return false!");
				DCDATLOG("RB00003:");
				continue;
			}
			//填写处理完成时间	
			memset(szCurTime,0x00,sizeof(szCurTime));
			PublicLib::GetTime(szCurTime, YYYYMMDDHHMMSS);
			cur_time.FromString(szCurTime,"YYYYMMDDHHNNSS");
			nRet = SetLastDealTime(cur_time,iter->second,false);
			if (nRet < 0)
			{
				if (0 > ReConnect()) //重连失败
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::DealOldUser", "ReConnect failed,exit");
					return -1;
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_INFO, 0, "DCLoadOldUser::DealOldUser", "ReConnect success, set deal time");
					if (SetLastDealTime(cur_time, iter->second, false) < 0)
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "DCLoadOldUser::DealOldUser", "ReConnect success,update last deal time failed once again");
					}
				}
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldUser::DealOldUser","Deal one event time end!***********************************************");
		}
	}
    
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldUser::DealOldUser","end! ");
    return 0;
}

//处理老用户
int DCLoadOldUser::DealOldUserDcfServ()
{
	DCDateTime cur_time;
	char szCurTime[16]={0};
	vector<long> vecInvaildRec;
	int nRet = 0;
	ACE_Time_Value aSleepS(m_cfgPara.nOldUserSleepTime);

	char szCurrDate[9]={0};
	PublicLib::GetTime(szCurrDate, YYYYMMDD);
	int nBillingCycleID = atoi(szCurrDate);
	//如果日期或本地网变更,则重新刷新账期
	if (m_nBillingCycleId != nBillingCycleID || m_nLatnId!=DCDataCenter::instance()->m_nLatnId)//刷新时间到天 
	{
	    if (m_nBillingCycleId != nBillingCycleID)
        {
            m_writeLogCount = 0;
            m_nPrdInstNum = 0;
            m_nSubInstNum = 0;
            m_nOfrInstNum = 0;
        }
		m_nBillingCycleId = nBillingCycleID;
		DCDataCenter::instance()->RefreshBillingCycle((int)(nBillingCycleID/100),m_nLatnId);
		if(!((m_pComboAdpt->m_pEventType)->reloadCondition()))
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCLoadOldUser::DealOldUser","DCRDEventType reloadCondition error\n");
			return -1;
		}
	}	

	//更新跨账户表和过户表信息
	if( m_pComboAdpt->LoadSpecialAcct(m_nLatnId)<0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldUser::DealOldUser","LoadSpecialAcct failed, LatnId[%d].",m_nLatnId);
		return -1;
	}
	
	if( m_pComboAdpt->LoadTransferPrdAcct(m_nLatnId,vecInvaildRec)<0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldUser::DealOldUser","LoadTransferPrdAcct failed, LatnId[%d].",m_nLatnId);
		return -1;
	}
	
	//填写开始处理时间	
	SetLastDealTimeDcfServ(cur_time, m_inputParam.nProType, m_nProcId, m_nLatnId, true);
	m_pComboAdpt->releaseUserMap(true);
	//每次老用户处理前先清理队列
	RentUserInfo stRentInfo;
	nRet = m_pCallServer->process(&stRentInfo,MSGCTRL_MODE_LOADOLDBEG,p_dbm);
	if(nRet < 0 )
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::DealOldUser","call DCCallZKServerMaker::process fail");
	} 

	p_dbm->CheckReset(); // 重连mysql，西藏查资料与定价不同的mysql，这里主动重连一下
	//处理老用户--非跨账户
	nRet = loadOldUser(cur_time,m_nLatnId,false);//增加试算标识
	if(nRet<0)
	{	
		if(ReConnect()) //重连失败
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::DealOldUser",	"ReConnect failed,exit");	
			return -1;
		}	
		else  //重连成功,需要重新加载用户
		{						
			DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCLoadOldUser::DealOldUser", "ReConnect success,load old user once again");
			DCDATLOG("RB00002:");
            nRet = loadOldUser(cur_time,m_nLatnId,false);//增加试算标识				
			if(nRet<0)
			{							
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"DCLoadOldUser::DealOldUser", "load old user failed again");
				return -1;
			}
		}
	}	

	DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCLoadOldUser::DealOldUser","Send normal old user finished,sleep 10s and then deal cross old user");
	DCDATLOG("RB00004:");
	ACE_OS::sleep(aSleepS); 

    //处理老用户--跨账户
	nRet = loadOldCrossUser(cur_time,m_nLatnId,false);
	if(nRet<0)
	{   
		if(ReConnect()) //重连失败
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldCrossUser::DealOldUser",	"ReConnect failed,exit");	
			return -1;
		}	
		else  //重连成功,需要重新加载用户
		{						
			DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCLoadOldCrossUser::DealOldUser", "ReConnect success,load old crossuser once again");
			DCDATLOG("RC00003:");
            nRet = loadOldCrossUser(cur_time,m_nLatnId,false);				
			if(nRet<0)
			{							
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"DCLoadOldCrossUser::DealOldUser", "load old crossuser failed again");
				return -1;
			}
		}					
	}
	
	WriteRentLog(m_nLatnId,self(),0,0,0,true);
    stRentInfo.nGroupNum = m_writeLogCount;
    m_pCallServer->process(&stRentInfo, MSGCTRL_MODE_LOADOLDEND,p_dbm);

    DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCLoadOldUser::DealOldUser","Send cross user finished,sleep 10s and then deal next flow");
	DCDATLOG("RC00005:");
	ACE_OS::sleep(aSleepS);

	//填写处理完成时间	
	memset(szCurTime,0x00,sizeof(szCurTime));
	PublicLib::GetTime(szCurTime, YYYYMMDDHHMMSS);
	cur_time.FromString(szCurTime,"YYYYMMDDHHNNSS");
	nRet = SetLastDealTimeDcfServ(cur_time, m_inputParam.nProType, m_nProcId, m_nLatnId, false);
	if (nRet < 0)
	{
		if (0 > ReConnect()) //重连失败
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::DealOldUser", "ReConnect failed,exit");
			return -1;
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_INFO, 0, "DCLoadOldUser::DealOldUser", "ReConnect success, set deal time");
			if (SetLastDealTimeDcfServ(cur_time, m_inputParam.nProType, m_nProcId, m_nLatnId, false) < 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "DCLoadOldUser::DealOldUser", "ReConnect success,update last deal time failed once again");
			}
		}
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldUser::DealOldUser","Deal one event time end!***********************************************");

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldUser::DealOldUser","end! ");
    return 0;
}

//新增处理试算用户
int DCLoadOldUser::DealTrialUser()
{

	map<long,STEventPro> MapEvtType ;
	DCDateTime last_time,cur_time,last_start_time;
	char szCurTime[16]={0};
	vector<long> vecInvaildRec;
	int nRet = 0;
	ACE_Time_Value aSleepS(m_cfgPara.nOldUserSleepTime);

	//定时任务数据刷新数据
	initCycEvtPro(MapEvtType,m_nProcId,10);//试算中proc_type=10
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldUser::DealTrialUser","MapEvtType size is [%d]",MapEvtType.size());

	for (std::list<string>::iterator iterLatnId = m_ltLatn.begin(); iterLatnId != m_ltLatn.end(); ++iterLatnId)
	{ 
		m_nLatnId =  atoi((*iterLatnId).c_str());
			
		map<long,STEventPro>::iterator iter = MapEvtType.begin();
		for(;iter!=MapEvtType.end();iter++)
		{
			if(m_nLatnId!=(iter->second).nLatnId)
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldUser::DealTrialUser","continue this latn_id! [input latn_id=%d] [config latn_id=%d]",m_nLatnId,(iter->second).nLatnId);
				continue;
			}
			last_time.FromString((iter->second).szLastDealTime,"YYYYMMDDHHNNSS");
			last_start_time.FromString((iter->second).szLastStartTime,"YYYYMMDDHHNNSS");
			memset(szCurTime,0x00,sizeof(szCurTime));
			PublicLib::GetTime(szCurTime, YYYYMMDDHHMMSS);
			cur_time.FromString(szCurTime,"YYYYMMDDHHNNSS");
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldUser::DealTrialUser","load old user! [CycleEventId=%ld] [LastDealTime=%s] [CurTime=%s] [SetHour=%d] [LatnId=%d]",
				(iter->second).lnCycleEventId,(iter->second).szLastDealTime,szCurTime,(iter->second).nSetHour,(iter->second).nLatnId);

			//ssps_trial每月试算一次	
			if((m_cfgPara.nTrialDay == cur_time.GetDay()) && IsNeedDeal(last_time,cur_time,(iter->second).nSetHour,last_start_time))//sspa_trial，判断是否到了每月的试算日期
			{
			//if(IsNeedDeal(last_time,cur_time,(iter->second).nSetHour,last_start_time))//ssps_trial，不确定还需不需要这个条件，应该是需要
			//ssps_trial，应该需要把账期改为下个月
			
			
				char szCurrDate[7]={0};
				strncpy(szCurrDate,cur_time.ToString(string("YYYYMMDDHHNNSS")).c_str(),6);
				PublicLib::ChangeMonth(szCurrDate, 1);
				
				//如果日期或本地网变更,则重新刷新账期				
				int nBillingCycleID = atoi(szCurrDate);
				int nCurrDate = nBillingCycleID*100+m_cfgPara.nTrialDay;
				if (m_nBillingCycleId != nCurrDate || m_nLatnId!=DCDataCenter::instance()->m_nLatnId)//刷新时间到天 
				{
					m_nBillingCycleId = nCurrDate;
					DCDataCenter::instance()->RefreshBillingCycle(nBillingCycleID,m_nLatnId);
					if(!((m_pComboAdpt->m_pEventType)->reloadCondition()))
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCLoadOldUser::DealTrialUser","DCRDEventType reloadCondition error\n");
						return -1;
					}
				}	

				//更新跨账户表和过户表信息
				if( m_pComboAdpt->LoadSpecialAcct(m_nLatnId)<0)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldUser::DealTrialUser","LoadSpecialAcct failed, LatnId[%d].",m_nLatnId);
					return -1;
				}
				
			
				//填写开始处理时间	
				SetLastDealTime(cur_time,iter->second,true);
                m_pComboAdpt->releaseUserMap(true);
				//每次老用户处理前先清理队列
				//RentUserInfo stRentInfo;
				//nRet = m_pCallServer->process(&stRentInfo,MSGCTRL_MODE_LOADOLDBEG,p_dbm);
				//if(nRet < 0 )
				//{
				//	DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::DealTrialUser","call DCCallZKServerMaker::process fail");
				//} 

                p_dbm->CheckReset(); // 重连mysql，西藏查资料与定价不同的mysql，这里主动重连一下
				//处理老用户--非跨账户
				nRet = loadOldUser(cur_time,m_nLatnId,true);//ssps_trial
				if(nRet<0)
				{	
					if(ReConnect()) //重连失败
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::DealTrialUser",	"ReConnect failed,exit");	
						return -1;
					}	
					else  //重连成功,需要重新加载用户
					{						
						DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCLoadOldUser::DealTrialUser", "ReConnect success,load old user once again");
						DCDATLOG("RB00002:");
						nRet = loadOldUser(cur_time,m_nLatnId,true);				
						if(nRet<0)
						{							
							DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"DCLoadOldUser::DealTrialUser", "load old user failed again");
							return -1;
						}
					}
				}	

				DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCLoadOldUser::DealTrialUser","Send normal old user finished,sleep 10s and then deal cross old user");
				DCDATLOG("RB00004:");
				ACE_OS::sleep(aSleepS); 
				
				//处理老用户--跨账户
				nRet = loadOldCrossUser(cur_time,m_nLatnId,true);
				if(nRet<0)
				{   
			        if(ReConnect()) //重连失败
			        {
				        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldCrossUser::DealTrialUser",	"ReConnect failed,exit");	
				        return -1;
			        }	
					else  //重连成功,需要重新加载用户
					{						
					    DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCLoadOldCrossUser::DealTrialUser", "ReConnect success,load old crossuser once again");
						DCDATLOG("RC00003:");
						nRet = loadOldCrossUser(cur_time,m_nLatnId,true);				
						if(nRet<0)
						{							
					        DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"DCLoadOldCrossUser::DealTrialUser", "load old crossuser failed again");
						    return -1;
						}
					}					
				}
				
				DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCLoadOldUser::DealTrialUser","Send cross user finished,sleep 10s and then deal next flow");
				DCDATLOG("RC00005:");
				ACE_OS::sleep(aSleepS);
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCLoadOldUser::DealTrialUser", "IsNeedDeal return false!");
				DCDATLOG("RB00003:");
				continue;
			}
			//填写处理完成时间	
			memset(szCurTime,0x00,sizeof(szCurTime));
			PublicLib::GetTime(szCurTime, YYYYMMDDHHMMSS);
			cur_time.FromString(szCurTime,"YYYYMMDDHHNNSS");
			nRet = SetLastDealTime(cur_time,iter->second,false);
			if (nRet < 0)
			{
				if (0 > ReConnect()) //重连失败
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::DealTrialUser", "ReConnect failed,exit");
					return -1;
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_INFO, 0, "DCLoadOldUser::DealTrialUser", "ReConnect success, set deal time");
					if (SetLastDealTime(cur_time, iter->second, false) < 0)
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "DCLoadOldUser::DealTrialUser", "ReConnect success,update last deal time failed once again");
					}
				}
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldUser::DealTrialUser","Deal one event time end!***********************************************");
		}
	}
    
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldUser::DealTrialUser","end! ");
    return 0;

}

//新增处理试算用户
int DCLoadOldUser::DealTrialUserDcfServ()
{
	DCDateTime cur_time;
	char szCurTime[16]={0};
	int nRet = 0;
	ACE_Time_Value aSleepS(m_cfgPara.nOldUserSleepTime);

	for (std::list<string>::iterator iterLatnId = m_ltLatn.begin(); iterLatnId != m_ltLatn.end(); ++iterLatnId)
	{ 
		m_nLatnId =  atoi((*iterLatnId).c_str());

		char szCurrDate[7]={0};
		strncpy(szCurrDate,cur_time.ToString(string("YYYYMMDDHHNNSS")).c_str(),6);
		PublicLib::ChangeMonth(szCurrDate, 1);
		
		//如果日期或本地网变更,则重新刷新账期				
		int nBillingCycleID = atoi(szCurrDate);
		int nCurrDate = nBillingCycleID*100+m_cfgPara.nTrialDay;
		if (m_nBillingCycleId != nCurrDate || m_nLatnId!=DCDataCenter::instance()->m_nLatnId)//刷新时间到天 
		{
			m_nBillingCycleId = nCurrDate;
			DCDataCenter::instance()->RefreshBillingCycle(nBillingCycleID,m_nLatnId);			
			if(!((m_pComboAdpt->m_pEventType)->reloadCondition()))
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCLoadOldUser::DealTrialUser","DCRDEventType reloadCondition error\n");
				return -1;
			}
		}	

		//更新跨账户表和过户表信息
		if( m_pComboAdpt->LoadSpecialAcct(m_nLatnId)<0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldUser::DealTrialUser","LoadSpecialAcct failed, LatnId[%d].",m_nLatnId);
			return -1;
		}
		
	
		//填写开始处理时间	
		SetLastDealTimeDcfServ(cur_time, m_inputParam.nProType, m_nProcId, m_nLatnId, true);
		m_pComboAdpt->releaseUserMap(true);
		//每次老用户处理前先清理队列
		//RentUserInfo stRentInfo;
		//nRet = m_pCallServer->process(&stRentInfo,MSGCTRL_MODE_LOADOLDBEG,p_dbm);
		//if(nRet < 0 )
		//{
		//	DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::DealTrialUser","call DCCallZKServerMaker::process fail");
		//} 

		p_dbm->CheckReset(); // 重连mysql，西藏查资料与定价不同的mysql，这里主动重连一下
		//处理老用户--非跨账户
		nRet = loadOldUser(cur_time,m_nLatnId,true);//ssps_trial
		if(nRet<0)
		{	
			if(ReConnect()) //重连失败
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::DealTrialUser",	"ReConnect failed,exit");	
				return -1;
			}	
			else  //重连成功,需要重新加载用户
			{						
				DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCLoadOldUser::DealTrialUser", "ReConnect success,load old user once again");
				DCDATLOG("RB00002:");
				nRet = loadOldUser(cur_time,m_nLatnId,true);				
				if(nRet<0)
				{							
					DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"DCLoadOldUser::DealTrialUser", "load old user failed again");
					return -1;
				}
			}
		}	

		DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCLoadOldUser::DealTrialUser","Send normal old user finished,sleep 10s and then deal cross old user");
		DCDATLOG("RB00004:");
		ACE_OS::sleep(aSleepS); 
		
		//处理老用户--跨账户
		nRet = loadOldCrossUser(cur_time,m_nLatnId,true);
		if(nRet<0)
		{   
			if(ReConnect()) //重连失败
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldCrossUser::DealTrialUser",	"ReConnect failed,exit");	
				return -1;
			}	
			else  //重连成功,需要重新加载用户
			{						
				DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCLoadOldCrossUser::DealTrialUser", "ReConnect success,load old crossuser once again");
				DCDATLOG("RC00003:");
				nRet = loadOldCrossUser(cur_time,m_nLatnId,true);				
				if(nRet<0)
				{							
					DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"DCLoadOldCrossUser::DealTrialUser", "load old crossuser failed again");
					return -1;
				}
			}					
		}
		
		DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCLoadOldUser::DealTrialUser","Send cross user finished,sleep 10s and then deal next flow");
		DCDATLOG("RC00005:");
		ACE_OS::sleep(aSleepS);

		//填写处理完成时间	
		memset(szCurTime,0x00,sizeof(szCurTime));
		PublicLib::GetTime(szCurTime, YYYYMMDDHHMMSS);
		cur_time.FromString(szCurTime,"YYYYMMDDHHNNSS");
		nRet = SetLastDealTimeDcfServ(cur_time, m_inputParam.nProType, m_nProcId, m_nLatnId, false);
		if (nRet < 0)
		{
			if (0 > ReConnect()) //重连失败
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::DealTrialUser", "ReConnect failed,exit");
				return -1;
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_INFO, 0, "DCLoadOldUser::DealTrialUser", "ReConnect success, set deal time");
				if (SetLastDealTimeDcfServ(cur_time, m_inputParam.nProType, m_nProcId, m_nLatnId, false) < 0)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "DCLoadOldUser::DealTrialUser", "ReConnect success,update last deal time failed once again");
				}
			}
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldUser::DealTrialUser","Deal one event time end!***********************************************");
	}
    
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldUser::DealTrialUser","end! ");
    return 0;
}

int DCLoadOldUser::UpdateDealStateFrozen(const long lFrozenId)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","UpdateDealStateFrozen begin,lFrozenId[%d]",lFrozenId);

	// <sql name="UpdateDealStateFrozen" bind="2">
	// 		UPDATE tb_prd_frozen_user
	// 		SET acct_deal_state=11
	// 		WHERE Frozen_id=?
	// 		AND acct_deal_state=21
	// </sql>

	string sbuf,sqlname;
	int nAffect = 0;

	try
	{
		sqlname = "UpdateDealStateFrozen";
		UDBSQL* pUpdate = p_dbm->GetSQL(sqlname.c_str());
		if(pUpdate==NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "UpdateDealState","GetSQL [%s] failed",sqlname.c_str());
			return -1;
		} 
		pUpdate->UnBindParam();
		pUpdate->BindParam(1,lFrozenId);

		pUpdate->GetSqlString(sbuf);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"UpdateDealState","Do SQL:Begin to do %s,sql :[%s]",sqlname.c_str(),sbuf.c_str());
		
		pUpdate->Execute();
		nAffect = pUpdate->GetRowCount();
		pUpdate->Connection()->Commit();
		pUpdate->Close();
	}
	catch (UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"UpdateDealState","InitCycEvtPro throw out abnormity! sql[%s] %s",sbuf.c_str(),e.ToString());
		return -1;
	}

	if(0 == nAffect)
	{
		//未能取到对应任务的正确的表数据
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"UpdateDealState","GetRowCount[%d]Get task error!", nAffect);
		return -1;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","UpdateDealState end");
	return 0;
}

//提取复机用户（非跨账户）资料
int DCLoadOldUser::LoadFrozenUser(int nLatnId)
{	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "DCLoadOldUser::LoadFrozenUser begin,LatnId[%d] nProcNum[%d] m_nProcId[%d]",nLatnId,m_inputParam.nProcNum,m_nProcId);
 
	long lnCounts=0,lnAcctCounts=0,lnSendMsgCount=0;
	long lntmp=0,lnSendMsgCountTmp=0;

	struct timeval tm;
	gettimeofday(&tm, NULL);
	unsigned long lnbegin = (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);
	unsigned long lnbeginTmp = lnbegin;
	unsigned long lnend = 0, lnUseTime = 0;
	int nTps1=0,nTps2=0,nTps3=0;
	int nState = 0;

	char monGroup[50] = {0};
	
	if (m_cfgPara.nOpenKpi)
	{		
		DCKpiSender::instance()->GetFullGroup("TraceKpi", nLatnId, monGroup);
	}

	char buf[32];
	char szEffDate[16] = {0};	
	char szExpDate[16] = {0};	
	long lnLastPrdInstId=-99;
	long lnLastAcctId=-99;
	int nCurAcctPrdCount = 0;
	long lnCurPrdInstId=-99,lnCurAcctId=-99;
	T_UserInfo tLastUserInfo;  //上一个复机用户信息
	T_UserInfo tCurUserInfo;  //当前复机用户信息

    ocs::StRentMainInfo tPrdInst;
	T_FrozenInfo tFrozenInfo;
	int nRet = 0;

	try
	{
		char *sCycleBeginTime = DCDataCenter::instance()->sCycleBeginTime;
		char *sCycleEndTime = DCDataCenter::instance()->sCycleEndTime;
		
		UDBSQL* pQuery = p_dbm->GetSQL("Query555");
		if(pQuery==NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::LoadFrozenUser","GetSQL Query555 failed");
			return -1;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","Get sql Query555 OK");

		UDBSQL* pQueryUserInfo = p_dbm->GetSQL("data_serv");
		if(pQueryUserInfo==NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::LoadFrozenUser","GetSQL data_serv failed");
			return -1;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","Get sql data_serv OK");
		pQuery->UnBindParam();
		pQuery->BindParam(1,nLatnId);
		pQuery->BindParam(2,m_inputParam.nProcNum);
		pQuery->BindParam(3,m_nProcId);
		string sbuf;
		pQuery->GetSqlString(sbuf);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldUser::LoadFrozenUser","Do SQL: begin to do Query555,sql :[%s] ",sbuf.c_str());

		pQuery->Execute();
		while(pQuery->Next())
		{
			//切换一个用户清空一次m_vecOfrInstTmp，因为已经存入m_stAcctBodyReq.VOfrInst
			m_pComboAdpt->releaseUserMap(false);		
			lnCounts++;

			pQuery->GetValue(1,tFrozenInfo.lFrozenId);
			pQuery->GetValue(2,tFrozenInfo.lPrdInstId);
			tFrozenInfo.lLatnId = nLatnId;			
			pQuery->GetValue(3,tFrozenInfo.cStateTime);
			pQuery->GetValue(4,tFrozenInfo.nObversionMode);				

			pQueryUserInfo->UnBindParam();
			pQueryUserInfo->BindParam(1,tFrozenInfo.lPrdInstId);
			pQueryUserInfo->BindParam(2,tFrozenInfo.lLatnId);
			pQueryUserInfo->Execute();
			if(pQueryUserInfo->Next())
			{
				pQueryUserInfo->GetValue(1,tPrdInst.lnAcctId);
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","Can not find frozen user info,lFrozenId[%ld],lLatnId[%ld]",tFrozenInfo.lFrozenId,tFrozenInfo.lLatnId);
                pQueryUserInfo->Close();
			   	continue;
			}		
			if(lnLastAcctId==-99)
			{
				lnLastAcctId = tPrdInst.lnAcctId;	//第一个账户
				
			    tLastUserInfo.nUserMode = USER_STOP;
			    tLastUserInfo.nObversionMode = tFrozenInfo.nObversionMode;
			    char szTriggerCycle[9] = {0};
			    strncpy(szTriggerCycle,tFrozenInfo.cStateTime,8);
			    szTriggerCycle[8]='\0';
			    tLastUserInfo.nTriggerCycle = atoi(szTriggerCycle);
			}
						
			//ACCTID切换后发送前一个账户消息
			if(lnLastAcctId != tPrdInst.lnAcctId)
			{
				DCBIZLOG(DCLOG_LEVEL_INFO,0,"","Change AcctId. LastAcctId[%ld] LastAcctCount[%d], CurAcctId[%ld].",lnLastAcctId,nCurAcctPrdCount,tPrdInst.lnAcctId);
				DCDATLOG("RB00005:%ld!%d!%ld",lnLastAcctId,nCurAcctPrdCount,tPrdInst.lnAcctId);
				lnAcctCounts++;
				// 查找套餐实例信息和明细,组装消息并发送
				if( AcctChangeDeal(lnLastAcctId, nLatnId, lnAcctCounts, nCurAcctPrdCount,lnLastPrdInstId,lnSendMsgCount,tLastUserInfo)<0)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"","AcctChangeDeal failed,AcctId[%ld].",lnLastAcctId);

					//更新复机表状态
					nState = 2;
			        nRet = UpdateFrozenDealed(tFrozenInfo.lFrozenId,nState,nLatnId);
			        if (nRet < 0)
			        {
		                if(ReConnect()) //重连失败
				        {
					        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::loadFrozenUser",	"ReConnect failed,exit");	
					        return -1;
				        }
				        else
				        {
					        DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"DCLoadOldUser::loadFrozenUser", "ReConnect success,UpdateFrozenDealed Frozen_id[%ld] once again",tFrozenInfo.lFrozenId);
					        if(UpdateFrozenDealed(tFrozenInfo.lFrozenId,nState,nLatnId)<0)
					        {
						        return -1;
					        }
				        }		
			        }
					continue;
				}
				
				lnLastAcctId = tPrdInst.lnAcctId;	//切换账号
				nCurAcctPrdCount = 0;
				
				tLastUserInfo.nUserMode = USER_STOP;
			    tLastUserInfo.nObversionMode = tFrozenInfo.nObversionMode;
			    char szTriggerCycle[9] = {0};
			    strncpy(szTriggerCycle,tFrozenInfo.cStateTime,8);
			    szTriggerCycle[8]='\0';
			    tLastUserInfo.nTriggerCycle = atoi(szTriggerCycle);				

				DCDATLOG(); //主动触发日志输出

			}
			nCurAcctPrdCount++;
			
			pQueryUserInfo->GetValue(7,szEffDate);
			pQueryUserInfo->GetValue(8,szExpDate);			
			tPrdInst.lnProdInstId = tFrozenInfo.lPrdInstId;
			lnCurPrdInstId = tPrdInst.lnProdInstId;
			lnCurAcctId = tPrdInst.lnAcctId;

			tCurUserInfo.nUserMode = USER_STOP;
			tCurUserInfo.nObversionMode = tFrozenInfo.nObversionMode;
			char szTriggerCycle[9] = {0};
			strncpy(szTriggerCycle,tFrozenInfo.cStateTime,8);
			szTriggerCycle[8]='\0';
			tCurUserInfo.nTriggerCycle = atoi(szTriggerCycle);		
				
			/*无需校验生失效时间//过滤失效记录
			if ((strcmp(szEffDate,"") != 0) && (strcmp(szExpDate,"") != 0))
			{
				if (0 == strncmp(szEffDate,szExpDate,8))
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","MainPrd Filter by EffDate[%]=ExpDate[%s],PrdInstId[%ld] AcctId[%ld]",szEffDate,szExpDate,lnCurPrdInstId,lnCurAcctId);
					SAFE_DELETE_PTR(tPrdInst);
					continue;
				}
				if (strncmp(sCycleBeginTime,szExpDate,8) > 0 || strncmp(sCycleEndTime,szEffDate,8) < 0)
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","MainPrd Filter by EffDate[%s] or ExpDate[%s] out of this month[%d],PrdInstId[%ld] AcctId[%ld]",szEffDate,szExpDate,m_nBillingCycleId,lnCurPrdInstId,lnCurAcctId);
					SAFE_DELETE_PTR(tPrdInst);
					continue;
				}
			}*/
			pQueryUserInfo->GetValue(3,buf);
			tPrdInst.szAccNum = buf;
			pQueryUserInfo->GetValue(5,buf);
			tPrdInst.szUserTypeId = buf;				
			pQueryUserInfo->GetValue(6,buf);
			tPrdInst.lnOfferId = atol(buf);
			pQueryUserInfo->GetValue(10,buf);                 
			tPrdInst.lnCustId = atol(buf);
			pQueryUserInfo->GetValue(11,buf);
			tPrdInst.szStatusCd = buf;
			pQueryUserInfo->GetValue(12,buf);
			tPrdInst.szCreateDate = buf;
			pQueryUserInfo->GetValue(13,buf);
			tPrdInst.szFirstFinishDate = buf;
			pQueryUserInfo->GetValue(14,buf);
			tPrdInst.szInstallDate = buf;
			
			pQueryUserInfo->GetValue(18,buf);
			tPrdInst.lnProdId = atol(buf);	
			pQueryUserInfo->GetValue(19,buf);			
			tPrdInst.szAreaCode = buf;	
			pQueryUserInfo->Close();
			
			tPrdInst.szEffDate = szEffDate;
			tPrdInst.szExpDate = szExpDate;	
			tPrdInst.lnOfrInstId = -2;
			tPrdInst.lnGroupUserGroupId = tPrdInst.lnProdInstId;

			string strChargetatusCd;
			nRet = m_pComboAdpt->QueryChargeStatusCd(tPrdInst.szStatusCd,strChargetatusCd);
			if (nRet < 0)
			{				
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","Curr PrdInstId[%ld] AcctId[%ld] QueryChargeStatusCd filed",tPrdInst.lnProdInstId,tPrdInst.lnAcctId);
				return -1;
			}
			tPrdInst.szStatusCd = strChargetatusCd;

			if(m_pComboAdpt->ComboAdapt(tPrdInst, m_nLatnId)<0)
            {
			    DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","Curr PrdInstId[%ld] AcctId[%ld] ComboAdapt filed",lnCurPrdInstId,lnCurAcctId);
				m_pComboAdpt->releaseUserMap(true); //清空数据	
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"","loadFrozenUser fail end,LatnId[%d]",nLatnId);
				nState = 2;
            }
			else
			{
				nState = 1;
			}
			
			lnLastPrdInstId = lnCurPrdInstId;	
			//更新复机表状态
			nRet = UpdateFrozenDealed(tFrozenInfo.lFrozenId,nState,nLatnId);
			if (nRet < 0)
			{
		        if(ReConnect()) //重连失败
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::loadFrozenUser",	"ReConnect failed,exit");	
					return -1;
				}
				else
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"DCLoadOldUser::loadFrozenUser", "ReConnect success,UpdateFrozenDealed Frozen_id[%ld] once again",tFrozenInfo.lFrozenId);
					if(UpdateFrozenDealed(tFrozenInfo.lFrozenId,nState,nLatnId)<0)
					{
						return -1;
					}
				}		
			}

			if(lnCounts%1000==0)//每一千用户输出一次tps
			{
				gettimeofday(&tm, NULL);//性能日志
				lnend = (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);//性能日志
				lnUseTime = DIFFTIME_US(lnend, lnbeginTmp);
				lnbeginTmp = lnend;
				nTps3=0;
				lntmp = lnSendMsgCount-lnSendMsgCountTmp;
				if(lnUseTime>0 && lntmp>0)
				{
					nTps3 =(int)((lntmp*1000000.0)/lnUseTime);
				}
				lnSendMsgCountTmp = lnSendMsgCount;
				DCBIZLOG(DCLOG_LEVEL_INFO, 0,"TPS loadFrozenUser","LatnId[%d] curUseTime[%ldus] curSendMsgCounts[%ld][tps=%d] LoadAcctCounts[%ld] LoadPrdCounts[%ld]",nLatnId,lnUseTime,lntmp,nTps3,lnAcctCounts,lnCounts);
				DCDATLOG("RB00006:%d!%ld!%ld!%d!%ld!%ld",nLatnId,lnUseTime,lntmp,nTps3,lnAcctCounts,lnCounts);

                std::string strStat;
            	p_dbm->get_statistic()->to_string(strStat, true);	 //过滤未使用的
        		DCPERFLOG((int)lnUseTime,"LatnId[%d] curUseTime[%ldus] curSendMsgCounts[%ld][tps=%d] LoadAcctCounts[%ld] LoadPrdCounts[%ld] sqlperf:%s",nLatnId,lnUseTime,lntmp,nTps3,lnAcctCounts,lnCounts,strStat.c_str());
            }

			DCDATLOG(); //主动触发日志输出
		}	
		pQuery->Close();		
		
		//发出最后一条
		if(nCurAcctPrdCount>0)
		{
			lnAcctCounts++;			
			if( AcctChangeDeal(lnCurAcctId, nLatnId, lnAcctCounts, nCurAcctPrdCount,lnCurPrdInstId,lnSendMsgCount,tCurUserInfo)<0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldUser::loadOldUser","AcctChangeDeal failed,AcctId[%ld].",lnCurAcctId);
				return -1;
			}
		
		}
		if(lnAcctCounts>0)
    	{
    		RentUserInfo stRentInfo;
			stRentInfo.stAcctHead.user_mode = USER_STOP;
			stRentInfo.stAcctHead.obversion_mode = tCurUserInfo.nObversionMode;
			stRentInfo.stAcctHead.trigger_cycle = tCurUserInfo.nTriggerCycle;
			int nRet = m_pCallServer->process(&stRentInfo,MSGCTRL_MODE_RESEND,p_dbm);
			if(nRet < 0)
	        {
	            DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::loadOldUser","call DCCallZKServerMaker::process fail");
	        } 
    	}
		m_pComboAdpt->releaseUserMap(true);//清空数据

	}		
	catch(UDBException &e)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR,-e.GetErrorCode(),"DCLoadOldUser::loadOldUser","error info[%s]",e.ToString());
        return -e.GetErrorCode();
    }

	gettimeofday(&tm, NULL);//性能日志
	lnend = (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);//性能日志
	lnUseTime = DIFFTIME_US(lnend, lnbegin);
	nTps1=0;nTps2=0;nTps3=0;
	if(lnUseTime>0 && lnAcctCounts>0)
		nTps1 =(int)((lnAcctCounts*1000000.0)/lnUseTime);
	if(lnUseTime>0 && lnCounts>0)
		nTps2 =(int)((lnCounts*1000000.0)/lnUseTime);
	if(lnUseTime>0 && lnSendMsgCount>0)
		nTps3 =(int)((lnSendMsgCount*1000000.0)/lnUseTime);
	// 输出统计信息
	std::string strStat;
	p_dbm->get_statistic()->to_string(strStat, true);	 //过滤未使用的
	DCBIZLOG(DCLOG_LEVEL_INFO, 0,"PERF RentLoad FrozenUser","LatnId[%d] LoadUseTime[%ldus] LoadAcctCounts[%ld][tps1=%d] LoadPrdCounts[%ld][tps2=%d] ToatalSendMsgCounts[%ld][tps3=%d] sqlperf:%s",nLatnId,lnUseTime,lnAcctCounts,nTps1,lnCounts,nTps2,lnSendMsgCount,nTps3, strStat.c_str());
	DCDATLOG("RB00007:%d!%ld!%ld!%d!%ld!%d!%ld!%d!%s",nLatnId,lnUseTime,lnAcctCounts,nTps1,lnCounts,nTps2,lnSendMsgCount,nTps3, strStat.c_str());
    DCPERFLOG((int)lnUseTime,"LatnId[%d] curUseTime[%ldus] curSendMsgCounts[%ld][tps=%d] LoadAcctCounts[%ld] LoadPrdCounts[%ld] sqlperf:%s",nLatnId,lnUseTime,lntmp,nTps3,lnAcctCounts,lnCounts,strStat.c_str());
     
	if (m_cfgPara.nOpenKpi)
	{		
		DCKpiSender::instance()->cycle_array_inc(m_ptrBPMon, monGroup, 1, "k1", NULL, lnCounts);
		DCKpiSender::instance()->cycle_array_inc(m_ptrBPMon, monGroup, 1, "k2", NULL, lnUseTime);
		DCKpiSender::instance()->cycle_array_inc(m_ptrBPMon, monGroup, 1, "k3", NULL, 0);
	}
	//重置统计信息
	p_dbm->get_statistic()->reset();
		
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "DCLoadOldUser::LoadFrozenUser end,LatnId[%d]",nLatnId);

	return 0;
}

//提取复机用户（非跨账户）资料
int DCLoadOldUser::LoadFrozenUserDcfServ(int nLatnId, std::vector<string> &vecSQLFields)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "DCLoadOldUser::LoadFrozenUser begin,LatnId[%d] nProcNum[%d] m_nProcId[%d]",nLatnId,m_inputParam.nProcNum,m_nProcId);
 
	long lnCounts=0,lnAcctCounts=0,lnSendMsgCount=0;
	long lntmp=0,lnSendMsgCountTmp=0;

	struct timeval tm;
	gettimeofday(&tm, NULL);
	unsigned long lnbegin = (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);
	unsigned long lnbeginTmp = lnbegin;
	unsigned long lnend = 0, lnUseTime = 0;
	int nTps1=0,nTps2=0,nTps3=0;
	int nState = 0;

	char monGroup[50] = {0};
	
	if (m_cfgPara.nOpenKpi)
	{
		DCKpiSender::instance()->GetFullGroup("TraceKpi", nLatnId, monGroup);
	}

	char buf[32];
	char szEffDate[16] = {0};
	char szExpDate[16] = {0};
	long lnLastPrdInstId=-99;
	long lnLastAcctId=-99;
	int nCurAcctPrdCount = 0;
	long lnCurPrdInstId=-99,lnCurAcctId=-99;
	T_UserInfo tLastUserInfo;  //上一个复机用户信息
	T_UserInfo tCurUserInfo;  //当前复机用户信息

	ocs::StRentMainInfo tPrdInst;
	T_FrozenInfo tFrozenInfo;
	int nRet = 0;

	try
	{
		char *sCycleBeginTime = DCDataCenter::instance()->sCycleBeginTime;
		char *sCycleEndTime = DCDataCenter::instance()->sCycleEndTime;

		UDBSQL* pQueryUserInfo = p_dbm->GetSQL("data_serv");
		if(pQueryUserInfo==NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::LoadFrozenUser","GetSQL data_serv failed");
			return -1;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","Get sql data_serv OK");

		//切换一个用户清空一次m_vecOfrInstTmp，因为已经存入m_stAcctBodyReq.VOfrInst
		m_pComboAdpt->releaseUserMap(false);
		lnCounts++;

		//Frozen_id, Prd_inst_id, State_time, obversion_mode
		if(4 == vecSQLFields.size())
		{
			tFrozenInfo.lFrozenId = atol(vecSQLFields[0].c_str());
			tFrozenInfo.lPrdInstId = atol(vecSQLFields[1].c_str());
			tFrozenInfo.nObversionMode = atoi(vecSQLFields[3].c_str());
			strncpy(tFrozenInfo.cStateTime, vecSQLFields[2].c_str(),strlen(tFrozenInfo.cStateTime));

			//任务对应表数据预占
			nRet = UpdateDealStateFrozen(tFrozenInfo.lFrozenId);
			if(nRet < 0)
			{
				//任务处理失败
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "LoadFrozenUser", "UpdateDealState failed,exit");
				return -1;
			}
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "task_params size[%d] error!", vecSQLFields.size());
			return -1;
		}
		tFrozenInfo.lLatnId = nLatnId;

		pQueryUserInfo->UnBindParam();
		pQueryUserInfo->BindParam(1,tFrozenInfo.lPrdInstId);
		pQueryUserInfo->BindParam(2,tFrozenInfo.lLatnId);
		pQueryUserInfo->Execute();
		if(pQueryUserInfo->Next())
		{
			pQueryUserInfo->GetValue(1,tPrdInst.lnAcctId);
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","Can not find frozen user info,lFrozenId[%ld],lLatnId[%ld]",tFrozenInfo.lFrozenId,tFrozenInfo.lLatnId);
			pQueryUserInfo->Close();
			//continue;
			return 0;
		}
		if(lnLastAcctId==-99)
		{
			lnLastAcctId = tPrdInst.lnAcctId;	//第一个账户
			
			tLastUserInfo.nUserMode = USER_STOP;
			tLastUserInfo.nObversionMode = tFrozenInfo.nObversionMode;
			char szTriggerCycle[9] = {0};
			strncpy(szTriggerCycle,tFrozenInfo.cStateTime,8);
			szTriggerCycle[8]='\0';
			tLastUserInfo.nTriggerCycle = atoi(szTriggerCycle);
		}
					
		//ACCTID切换后发送前一个账户消息
		if(lnLastAcctId != tPrdInst.lnAcctId)
		{
			DCBIZLOG(DCLOG_LEVEL_INFO,0,"","Change AcctId. LastAcctId[%ld] LastAcctCount[%d], CurAcctId[%ld].",lnLastAcctId,nCurAcctPrdCount,tPrdInst.lnAcctId);
			DCDATLOG("RB00005:%ld!%d!%ld",lnLastAcctId,nCurAcctPrdCount,tPrdInst.lnAcctId);
			lnAcctCounts++;
			// 查找套餐实例信息和明细,组装消息并发送
			if( AcctChangeDeal(lnLastAcctId, nLatnId, lnAcctCounts, nCurAcctPrdCount,lnLastPrdInstId,lnSendMsgCount,tLastUserInfo)<0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"","AcctChangeDeal failed,AcctId[%ld].",lnLastAcctId);

				//更新复机表状态
				nState = 2;
				nRet = UpdateFrozenDealed(tFrozenInfo.lFrozenId,nState,nLatnId);
				if (nRet < 0)
				{
					if(ReConnect()) //重连失败
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::loadFrozenUser",	"ReConnect failed,exit");	
						return -1;
					}
					else
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"DCLoadOldUser::loadFrozenUser", "ReConnect success,UpdateFrozenDealed Frozen_id[%ld] once again",tFrozenInfo.lFrozenId);
						if(UpdateFrozenDealed(tFrozenInfo.lFrozenId,nState,nLatnId)<0)
						{
							return -1;
						}
					}		
				}
			}
			
			lnLastAcctId = tPrdInst.lnAcctId;	//切换账号
			nCurAcctPrdCount = 0;
			
			tLastUserInfo.nUserMode = USER_STOP;
			tLastUserInfo.nObversionMode = tFrozenInfo.nObversionMode;
			char szTriggerCycle[9] = {0};
			strncpy(szTriggerCycle,tFrozenInfo.cStateTime,8);
			szTriggerCycle[8]='\0';
			tLastUserInfo.nTriggerCycle = atoi(szTriggerCycle);				

			DCDATLOG(); //主动触发日志输出

		}
		nCurAcctPrdCount++;
		
		pQueryUserInfo->GetValue(7,szEffDate);
		pQueryUserInfo->GetValue(8,szExpDate);			
		tPrdInst.lnProdInstId = tFrozenInfo.lPrdInstId;
		lnCurPrdInstId = tPrdInst.lnProdInstId;
		lnCurAcctId = tPrdInst.lnAcctId;

		tCurUserInfo.nUserMode = USER_STOP;
		tCurUserInfo.nObversionMode = tFrozenInfo.nObversionMode;
		char szTriggerCycle[9] = {0};
		strncpy(szTriggerCycle,tFrozenInfo.cStateTime,8);
		szTriggerCycle[8]='\0';
		tCurUserInfo.nTriggerCycle = atoi(szTriggerCycle);		
			
		/*无需校验生失效时间//过滤失效记录
		if ((strcmp(szEffDate,"") != 0) && (strcmp(szExpDate,"") != 0))
		{
			if (0 == strncmp(szEffDate,szExpDate,8))
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","MainPrd Filter by EffDate[%]=ExpDate[%s],PrdInstId[%ld] AcctId[%ld]",szEffDate,szExpDate,lnCurPrdInstId,lnCurAcctId);
				SAFE_DELETE_PTR(tPrdInst);
				continue;
			}
			if (strncmp(sCycleBeginTime,szExpDate,8) > 0 || strncmp(sCycleEndTime,szEffDate,8) < 0)
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","MainPrd Filter by EffDate[%s] or ExpDate[%s] out of this month[%d],PrdInstId[%ld] AcctId[%ld]",szEffDate,szExpDate,m_nBillingCycleId,lnCurPrdInstId,lnCurAcctId);
				SAFE_DELETE_PTR(tPrdInst);
				continue;
			}
		}*/
		pQueryUserInfo->GetValue(3,buf);
		tPrdInst.szAccNum = buf;
		pQueryUserInfo->GetValue(5,buf);
		tPrdInst.szUserTypeId = buf;				
		pQueryUserInfo->GetValue(6,buf);
		tPrdInst.lnOfferId = atol(buf);
		pQueryUserInfo->GetValue(10,buf);                 
		tPrdInst.lnCustId = atol(buf);
		pQueryUserInfo->GetValue(11,buf);
		tPrdInst.szStatusCd = buf;
		pQueryUserInfo->GetValue(12,buf);
		tPrdInst.szCreateDate = buf;
		pQueryUserInfo->GetValue(13,buf);
		tPrdInst.szFirstFinishDate = buf;
		pQueryUserInfo->GetValue(14,buf);
		tPrdInst.szInstallDate = buf;
		
		pQueryUserInfo->GetValue(18,buf);
		tPrdInst.lnProdId = atol(buf);	
		pQueryUserInfo->GetValue(19,buf);			
		tPrdInst.szAreaCode = buf;	
		pQueryUserInfo->Close();
		
		tPrdInst.szEffDate = szEffDate;
		tPrdInst.szExpDate = szExpDate;	
		tPrdInst.lnOfrInstId = -2;
		tPrdInst.lnGroupUserGroupId = tPrdInst.lnProdInstId;

		string strChargetatusCd;
		nRet = m_pComboAdpt->QueryChargeStatusCd(tPrdInst.szStatusCd,strChargetatusCd);
		if (nRet < 0)
		{				
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","Curr PrdInstId[%ld] AcctId[%ld] QueryChargeStatusCd filed",tPrdInst.lnProdInstId,tPrdInst.lnAcctId);
			return -1;
		}
		tPrdInst.szStatusCd = strChargetatusCd;

		if(m_pComboAdpt->ComboAdapt(tPrdInst, m_nLatnId)<0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","Curr PrdInstId[%ld] AcctId[%ld] ComboAdapt filed",lnCurPrdInstId,lnCurAcctId);
			m_pComboAdpt->releaseUserMap(true); //清空数据	
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"","loadFrozenUser fail end,LatnId[%d]",nLatnId);
			nState = 2;
		}
		else
		{
			nState = 1;
		}
		
		lnLastPrdInstId = lnCurPrdInstId;	
		//更新复机表状态
		nRet = UpdateFrozenDealed(tFrozenInfo.lFrozenId,nState,nLatnId);
		if (nRet < 0)
		{
			if(ReConnect()) //重连失败
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::loadFrozenUser",	"ReConnect failed,exit");	
				return -1;
			}
			else
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"DCLoadOldUser::loadFrozenUser", "ReConnect success,UpdateFrozenDealed Frozen_id[%ld] once again",tFrozenInfo.lFrozenId);
				if(UpdateFrozenDealed(tFrozenInfo.lFrozenId,nState,nLatnId)<0)
				{
					return -1;
				}
			}		
		}

		if(lnCounts%1000==0)//每一千用户输出一次tps
		{
			gettimeofday(&tm, NULL);//性能日志
			lnend = (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);//性能日志
			lnUseTime = DIFFTIME_US(lnend, lnbeginTmp);
			lnbeginTmp = lnend;
			nTps3=0;
			lntmp = lnSendMsgCount-lnSendMsgCountTmp;
			if(lnUseTime>0 && lntmp>0)
			{
				nTps3 =(int)((lntmp*1000000.0)/lnUseTime);
			}
			lnSendMsgCountTmp = lnSendMsgCount;
			DCBIZLOG(DCLOG_LEVEL_INFO, 0,"TPS loadFrozenUser","LatnId[%d] curUseTime[%ldus] curSendMsgCounts[%ld][tps=%d] LoadAcctCounts[%ld] LoadPrdCounts[%ld]",nLatnId,lnUseTime,lntmp,nTps3,lnAcctCounts,lnCounts);
			DCDATLOG("RB00006:%d!%ld!%ld!%d!%ld!%ld",nLatnId,lnUseTime,lntmp,nTps3,lnAcctCounts,lnCounts);

            std::string strStat;
            p_dbm->get_statistic()->to_string(strStat, true);	 //过滤未使用的
        	DCPERFLOG((int)lnUseTime,"LatnId[%d] curUseTime[%ldus] curSendMsgCounts[%ld][tps=%d] LoadAcctCounts[%ld] LoadPrdCounts[%ld] sqlperf:%s",nLatnId,lnUseTime,lntmp,nTps3,lnAcctCounts,lnCounts,strStat.c_str());
        }

		DCDATLOG(); //主动触发日志输出
	
		//发出最后一条
		if(nCurAcctPrdCount>0)
		{
			lnAcctCounts++;			
			if( AcctChangeDeal(lnCurAcctId, nLatnId, lnAcctCounts, nCurAcctPrdCount,lnCurPrdInstId,lnSendMsgCount,tCurUserInfo)<0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldUser::loadOldUser","AcctChangeDeal failed,AcctId[%ld].",lnCurAcctId);
				return -1;
			}
		
		}
		if(lnAcctCounts>0)
		{
			RentUserInfo stRentInfo;
			stRentInfo.stAcctHead.user_mode = USER_STOP;
			stRentInfo.stAcctHead.obversion_mode = tCurUserInfo.nObversionMode;
			stRentInfo.stAcctHead.trigger_cycle = tCurUserInfo.nTriggerCycle;
			int nRet = m_pCallServer->process(&stRentInfo,MSGCTRL_MODE_RESEND,p_dbm);
			if(nRet < 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::loadOldUser","call DCCallZKServerMaker::process fail");
			} 
		}
		m_pComboAdpt->releaseUserMap(true);//清空数据
	}
	catch(UDBException &e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-e.GetErrorCode(),"DCLoadOldUser::loadOldUser","error info[%s]",e.ToString());
		return -e.GetErrorCode();
	}

	gettimeofday(&tm, NULL);//性能日志
	lnend = (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);//性能日志
	lnUseTime = DIFFTIME_US(lnend, lnbegin);
	nTps1=0;nTps2=0;nTps3=0;
	if(lnUseTime>0 && lnAcctCounts>0)
		nTps1 =(int)((lnAcctCounts*1000000.0)/lnUseTime);
	if(lnUseTime>0 && lnCounts>0)
		nTps2 =(int)((lnCounts*1000000.0)/lnUseTime);
	if(lnUseTime>0 && lnSendMsgCount>0)
		nTps3 =(int)((lnSendMsgCount*1000000.0)/lnUseTime);
	// 输出统计信息
	std::string strStat;
	p_dbm->get_statistic()->to_string(strStat, true);	 //过滤未使用的
	DCBIZLOG(DCLOG_LEVEL_INFO, 0,"PERF RentLoad FrozenUser","LatnId[%d] LoadUseTime[%ldus] LoadAcctCounts[%ld][tps1=%d] LoadPrdCounts[%ld][tps2=%d] ToatalSendMsgCounts[%ld][tps3=%d] sqlperf:%s",nLatnId,lnUseTime,lnAcctCounts,nTps1,lnCounts,nTps2,lnSendMsgCount,nTps3, strStat.c_str());
	DCDATLOG("RB00007:%d!%ld!%ld!%d!%ld!%d!%ld!%d!%s",nLatnId,lnUseTime,lnAcctCounts,nTps1,lnCounts,nTps2,lnSendMsgCount,nTps3, strStat.c_str());
    DCPERFLOG((int)lnUseTime,"LatnId[%d] curUseTime[%ldus] curSendMsgCounts[%ld][tps=%d] LoadAcctCounts[%ld] LoadPrdCounts[%ld] sqlperf:%s",nLatnId,lnUseTime,lntmp,nTps3,lnAcctCounts,lnCounts,strStat.c_str());
      
	if (m_cfgPara.nOpenKpi)
	{		
		DCKpiSender::instance()->cycle_array_inc(m_ptrBPMon, monGroup, 1, "k1", NULL, lnCounts);
		DCKpiSender::instance()->cycle_array_inc(m_ptrBPMon, monGroup, 1, "k2", NULL, lnUseTime);
		DCKpiSender::instance()->cycle_array_inc(m_ptrBPMon, monGroup, 1, "k3", NULL, 0);
	}
	//重置统计信息
	p_dbm->get_statistic()->reset();
		
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "DCLoadOldUser::LoadFrozenUser end,LatnId[%d]",nLatnId);

	return 0;
}

//提取老用户（非跨账户）资料
int DCLoadOldUser::loadOldUser(DCDateTime &cur_time,int nLatnId,bool isTrial)//增加试算标识
{	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "DCLoadOldUser::loadOldUser begin,CurrDealTime[%s],LatnId[%d],nProType[%d]",cur_time.ToString(string("YYYYMMDDHHNNSS")).c_str(),nLatnId, m_inputParam.nProType);
 
	long lnCounts=0,lnAcctCounts=0,lnSendMsgCount=0;
	long lntmp=0,lnSendMsgCountTmp=0;

	struct timeval tm;
	gettimeofday(&tm, NULL);
	unsigned long lnbegin = (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);
	unsigned long lnbeginTmp = lnbegin;
	unsigned long lnend = 0, lnUseTime = 0;
	int nTps1=0,nTps2=0,nTps3=0;

	char monGroup[50] = {0};
	
	if (m_cfgPara.nOpenKpi)
	{		
		DCKpiSender::instance()->GetFullGroup("TraceKpi", nLatnId, monGroup);
	}

	char buf[32];
	char szEffDate[16] = {0};	
	char szExpDate[16] = {0};	
	long lnLastPrdInstId=-99;
	long lnLastAcctId=-99;
	int nCurAcctPrdCount = 0;  //当前acct_id下的用户数
	long lnCurPrdInstId=-99,lnCurAcctId=-99;
	bool bFilterFlag = false;

	char szSqlName[64]={0};
	ocs::StRentMainInfo tPrdInst;
	StPrdInstInfo tmpPrdInfo;
	set<StPrdInstInfo> setPrdInfo;
	map<long,STSpecialAcctInfo>::iterator itermap;
	set<StPrdInstInfo>::iterator itersetPrd;
	int nRet = 0;	
	m_pComboAdpt->m_multimapAcctUser.clear();

	try
	{
		char *sCycleBeginTime = DCDataCenter::instance()->sCycleBeginTime;
		char *sCycleEndTime = DCDataCenter::instance()->sCycleEndTime;

        if(PROC_TYPE_PRE_NORMAL == m_inputParam.nProType || isTrial )//增加试算标识
        {
			sprintf(szSqlName , "Query8400");
		}
		else
		{
			sprintf(szSqlName , "Query840|%s" , DCDataCenter::instance()->sCycleEndDate);
		}
		UDBSQL* pQuery = p_dbm->GetSQL(szSqlName);
		if(pQuery==NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::loadOldUser","GetSQL [%s] failed", szSqlName);
			return -1;
		}
		pQuery->UnBindParam();
		pQuery->BindParam(1,nLatnId);
		pQuery->BindParam(2,m_inputParam.nProcNum);
		pQuery->BindParam(3,m_nProcId);
		
		string sbuf;
		pQuery->GetSqlString(sbuf);
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"DCLoadOldUser::loadOldUser","nProType[%d] nProcNum[%d] m_nProcId[%d],Do SQL: begin to do Query840,sql :[%s] ",m_inputParam.nProType,m_inputParam.nProcNum,m_nProcId,sbuf.c_str());

		pQuery->Execute();		
		while(pQuery->Next())
		{
			pQuery->GetValue(1,tmpPrdInfo.lnAcctId);
			pQuery->GetValue(3,tmpPrdInfo.szAccNum);
			pQuery->GetValue(5,tmpPrdInfo.szUserTypeId);
			pQuery->GetValue(6,tmpPrdInfo.lnOfferId);
			pQuery->GetValue(7,tmpPrdInfo.szEffDate);
			pQuery->GetValue(8,tmpPrdInfo.szExpDate);
			pQuery->GetValue(9,tmpPrdInfo.lnProdInstId);
			pQuery->GetValue(10,tmpPrdInfo.lnCustId);
			pQuery->GetValue(11,tmpPrdInfo.szStatusCd);
			pQuery->GetValue(12,tmpPrdInfo.szCreateDate);
			pQuery->GetValue(13,tmpPrdInfo.szFirstFinishDate);
			pQuery->GetValue(14,tmpPrdInfo.szInstallDate);
			pQuery->GetValue(15,tmpPrdInfo.lnProdId);
			pQuery->GetValue(16,tmpPrdInfo.szAreaCode);	
			pQuery->GetValue(17,tmpPrdInfo.szBasicState);
			
			long lnAcctId = tmpPrdInfo.lnAcctId;
			bool bIsImmed = false;
			nRet = m_pComboAdpt->JudgeImmedAcct(lnAcctId,nLatnId,bIsImmed);
			if (nRet < 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","JudgeImmedAcct failed,lnAcctId[%ld],nLatnId[%d]",lnAcctId,nLatnId);
				return -1;
			}
			if (bIsImmed)
			{				
			    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","lnAcctId[%ld] is immed acct,not need to do",lnAcctId);
				continue;
			}

			/*无需校验生失效时间//过滤失效记录
			if ((strcmp(szEffDate,"") != 0) && (strcmp(szExpDate,"") != 0))
			{
				if (0 == strncmp(szEffDate,szExpDate,8))
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","MainPrd Filter by EffDate[%]=ExpDate[%s],PrdInstId[%ld] AcctId[%ld]",szEffDate,szExpDate,lnCurPrdInstId,lnCurAcctId);
					SAFE_DELETE_PTR(tPrdInst);
					continue;
				}
				if (strncmp(sCycleBeginTime,szExpDate,8) > 0 || strncmp(sCycleEndTime,szEffDate,8) < 0)
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","MainPrd Filter by EffDate[%s] or ExpDate[%s] out of this month[%d],PrdInstId[%ld] AcctId[%ld]",szEffDate,szExpDate,m_nBillingCycleId,lnCurPrdInstId,lnCurAcctId);
					SAFE_DELETE_PTR(tPrdInst);
					continue;
				}
			}*/

			string strChargetatusCd;
			nRet = m_pComboAdpt->QueryChargeStatusCd(tmpPrdInfo.szStatusCd,strChargetatusCd);
			if (nRet < 0)
			{				
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","Curr PrdInstId[%ld] AcctId[%ld] QueryChargeStatusCd filed",tmpPrdInfo.lnProdInstId,tmpPrdInfo.lnAcctId);
				return -1;
			}
			tmpPrdInfo.szStatusCd = strChargetatusCd;
			bFilterFlag = m_pComboAdpt->FilterStatusCd(tmpPrdInfo,isTrial,false);
			if (bFilterFlag)
			{				
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","Curr PrdInstId[%ld] AcctId[%ld] FilterStatusCd,szStatusCd[%s]",tmpPrdInfo.lnProdInstId,tmpPrdInfo.lnAcctId,tmpPrdInfo.szStatusCd.c_str());
				continue;
			}			

			tPrdInst.lnAcctId = tmpPrdInfo.lnAcctId;
			tPrdInst.lnProdInstId = tmpPrdInfo.lnProdInstId;
			
			tPrdInst.szAccNum = tmpPrdInfo.szAccNum;
			tPrdInst.szUserTypeId = tmpPrdInfo.szUserTypeId;
			tPrdInst.lnOfferId = tmpPrdInfo.lnOfferId;             
			tPrdInst.lnCustId = tmpPrdInfo.lnCustId;
			tPrdInst.szStatusCd = tmpPrdInfo.szStatusCd;
			tPrdInst.szCreateDate = tmpPrdInfo.szCreateDate;
			tPrdInst.szFirstFinishDate = tmpPrdInfo.szFirstFinishDate;
			tPrdInst.szInstallDate = tmpPrdInfo.szInstallDate;
			tPrdInst.lnProdId = tmpPrdInfo.lnProdId;	
			tPrdInst.szAreaCode = tmpPrdInfo.szAreaCode;

			tPrdInst.szEffDate = tmpPrdInfo.szEffDate;
			tPrdInst.szExpDate = tmpPrdInfo.szExpDate;	
			tPrdInst.szEventEffDate = tmpPrdInfo.szEffDate;
			tPrdInst.szEventExpDate = tmpPrdInfo.szExpDate;
			tPrdInst.lnOfrInstId = -2;
			tPrdInst.lnGroupUserGroupId = tPrdInst.lnProdInstId;
            tPrdInst.nBelongDay = m_inputParam.nProType==PROC_TYPE_PRE_NORMAL?2100:1200;//暂时用于保存用户预后属性，ComboAdapt后会更新为正确值

			if(PROC_TYPE_PRE_NORMAL == m_inputParam.nProType || isTrial )
			{
				;
			}
			else
			{
				//过滤跨账户，处理完非跨账户的再处理跨账户组				
				itermap = m_pComboAdpt->m_mapSpecialAcctType.find(tmpPrdInfo.lnAcctId);
				if(itermap!=m_pComboAdpt->m_mapSpecialAcctType.end())
				{
					if(itermap->second.nGroupId == 2)
					{
						DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","Filter CrossAcct[%ld].",tmpPrdInfo.lnAcctId);						
						m_pComboAdpt->m_multimapAcctUser.insert(make_pair(tmpPrdInfo.lnAcctId,tPrdInst)); 			
						continue;//此用户是跨账户
					}				
				}	
			}			
			setPrdInfo.insert(tmpPrdInfo);
		}
		pQuery->Close();		
		DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCLoadOldUser::loadOldUser","Query840/Query8400 get records[%d],m_inputParam.nProcNum[%d],m_nProcId[%d] ",setPrdInfo.size(), m_inputParam.nProcNum,m_nProcId);
		itersetPrd = setPrdInfo.begin();
		for(; itersetPrd != setPrdInfo.end(); itersetPrd++)
		{
			tmpPrdInfo = *itersetPrd;            
			lnCurPrdInstId = tmpPrdInfo.lnProdInstId;
            lnCurAcctId = tmpPrdInfo.lnAcctId;
			if(lnLastAcctId==-99)
				lnLastAcctId = lnCurAcctId;	//第一个账户
					
			//ACCTID切换后发送前一个账户消息
			if(lnLastAcctId != lnCurAcctId)
			{
				DCBIZLOG(DCLOG_LEVEL_INFO,0,"DCLoadOldUser::loadOldUser","Change AcctId. LastAcctId[%ld] LastAcctPrdCount[%d], CurAcctId[%ld].",lnLastAcctId,nCurAcctPrdCount,lnCurAcctId);
				DCDATLOG("RB00005:%ld!%d!%ld",lnLastAcctId,nCurAcctPrdCount,lnCurAcctId);
				lnAcctCounts++;
				T_UserInfo tUserInfo;
				tUserInfo.nObversionMode = 0;
				if(isTrial)//区分试算用户和老用户
					tUserInfo.nUserMode = USER_TRIAL;
				else
					tUserInfo.nUserMode = USER_OLD;
			    tUserInfo.nTriggerCycle = DCDataCenter::instance()->iBillingCycleId;
				// 查找套餐实例信息和明细,组装消息并发送
				if(nCurAcctPrdCount>0)
				{					
					nRet = AcctChangeDeal(lnLastAcctId, nLatnId, lnAcctCounts, nCurAcctPrdCount,lnLastPrdInstId,lnSendMsgCount,tUserInfo);
					if(nRet < 0)
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"DCLoadOldUser::loadOldUser","AcctChangeDeal failed,AcctId[%ld].",lnLastAcctId);
						if(nRet <= NO_FIND_SQL)
							return nRet;
					}
				}
				
				lnLastAcctId = lnCurAcctId;	//切换账号
				nCurAcctPrdCount = 0;
				m_pComboAdpt->releaseUserMap(true);//清理当前账户
				DCDATLOG(); //主动触发日志输出
			}
            
			tPrdInst.lnAcctId = tmpPrdInfo.lnAcctId;
			tPrdInst.lnProdInstId = tmpPrdInfo.lnProdInstId;			
			tPrdInst.szAccNum = tmpPrdInfo.szAccNum;
			tPrdInst.szUserTypeId = tmpPrdInfo.szUserTypeId;
			tPrdInst.lnOfferId = tmpPrdInfo.lnOfferId;             
			tPrdInst.lnCustId = tmpPrdInfo.lnCustId;
			tPrdInst.szStatusCd = tmpPrdInfo.szStatusCd;
			tPrdInst.szCreateDate = tmpPrdInfo.szCreateDate;
			tPrdInst.szFirstFinishDate = tmpPrdInfo.szFirstFinishDate;
			tPrdInst.szInstallDate = tmpPrdInfo.szInstallDate;
			tPrdInst.lnProdId = tmpPrdInfo.lnProdId;	
			tPrdInst.szAreaCode = tmpPrdInfo.szAreaCode;
			tPrdInst.szEffDate = tmpPrdInfo.szEffDate;
			tPrdInst.szExpDate = tmpPrdInfo.szExpDate;	
			tPrdInst.szEventEffDate = tmpPrdInfo.szEffDate;
			tPrdInst.szEventExpDate = tmpPrdInfo.szExpDate;
			tPrdInst.lnOfrInstId = -2;
			tPrdInst.lnGroupUserGroupId = tPrdInst.lnProdInstId;
			
			//切换一个用户清空一次m_vecOfrInstTmp，因为已经存入m_vecOfrInstOutput
			m_pComboAdpt->releaseUserMap(false);	 
			lnCounts++;
			nRet = m_pComboAdpt->ComboAdapt(tPrdInst, m_nLatnId);
			if(nRet < 0)
            {
			    DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::loadOldUser","Curr PrdInstId[%ld] AcctId[%ld] ComboAdapt filed",tPrdInst.lnProdInstId,tPrdInst.lnAcctId);
				m_pComboAdpt->releaseUserMap(false);//清理当前用户
				if(nRet <= NO_FIND_SQL)
					return nRet;
            }
			
			lnLastPrdInstId = lnCurPrdInstId;
			nCurAcctPrdCount++;			
			if(lnCounts%10000==0)//每一万用户输出一次tps
			{
				gettimeofday(&tm, NULL);//性能日志
				lnend = (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);//性能日志
				lnUseTime = DIFFTIME_US(lnend, lnbeginTmp);
				lnbeginTmp = lnend;
				nTps3=0;
				lntmp = lnSendMsgCount-lnSendMsgCountTmp;
				if(lnUseTime>0 && lntmp>0)
					nTps3 =(int)((lntmp*1000000.0)/lnUseTime);
				lnSendMsgCountTmp = lnSendMsgCount;
				DCBIZLOG(DCLOG_LEVEL_INFO, 0,"TPS loadOldUser","LatnId[%d] curUseTime[%ldus] curSendMsgCounts[%ld][tps=%d] LoadAcctCounts[%ld] LoadPrdCounts[%ld]",nLatnId,lnUseTime,lntmp,nTps3,lnAcctCounts,lnCounts);
				DCDATLOG("RB00006:%d!%ld!%ld!%d!%ld!%ld",nLatnId,lnUseTime,lntmp,nTps3,lnAcctCounts,lnCounts);

                std::string strStat;
                p_dbm->get_statistic()->to_string(strStat, true);	 //过滤未使用的
        	    DCPERFLOG((int)lnUseTime,"LatnId[%d] curUseTime[%ldus] curSendMsgCounts[%ld][tps=%d] LoadAcctCounts[%ld] LoadPrdCounts[%ld] sqlperf:%s",nLatnId,lnUseTime,lntmp,nTps3,lnAcctCounts,lnCounts,strStat.c_str());
            }
			
			DCDATLOG(); //主动触发日志输出
		}	
	
		//发出最后一条		
		if(nCurAcctPrdCount>0)
		{
			lnAcctCounts++;
			T_UserInfo tUserInfo;
			tUserInfo.nObversionMode = 0;
			if(isTrial)//区分试算用户和老用户
				tUserInfo.nUserMode = USER_TRIAL;
			else
				tUserInfo.nUserMode = USER_OLD;
		    tUserInfo.nTriggerCycle = DCDataCenter::instance()->iBillingCycleId;
			nRet = AcctChangeDeal(lnCurAcctId, nLatnId, lnAcctCounts, nCurAcctPrdCount,lnCurPrdInstId,lnSendMsgCount,tUserInfo);
			if(nRet < 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldUser::loadOldUser","AcctChangeDeal failed,AcctId[%ld].",lnCurAcctId);
				m_pComboAdpt->releaseUserMap(true);
				if(nRet <= NO_FIND_SQL)
					return nRet;
			}
    		RentUserInfo stRentInfo;
			int nRet = m_pCallServer->process(&stRentInfo,MSGCTRL_MODE_RESEND, p_dbm);
			if(nRet < 0)
	        {
	            DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::loadOldUser","call DCCallZKServerMaker::process fail");
	        } 		
		}
		m_pComboAdpt->releaseUserMap(true);//清空数据		

	}		
	catch(UDBException &e)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR,-e.GetErrorCode(),"DCLoadOldUser::loadOldUser","error info[%s]",e.ToString());
        return -e.GetErrorCode();
    }

	gettimeofday(&tm, NULL);//性能日志
	lnend = (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);//性能日志
	lnUseTime = DIFFTIME_US(lnend, lnbegin);
	nTps1=0;nTps2=0;nTps3=0;
	if(lnUseTime>0 && lnAcctCounts>0)
		nTps1 =(int)((lnAcctCounts*1000000.0)/lnUseTime);
	if(lnUseTime>0 && lnCounts>0)
		nTps2 =(int)((lnCounts*1000000.0)/lnUseTime);
	if(lnUseTime>0 && lnSendMsgCount>0)
		nTps3 =(int)((lnSendMsgCount*1000000.0)/lnUseTime);
	// 输出统计信息
	std::string strStat;
	p_dbm->get_statistic()->to_string(strStat, true);	 //过滤未使用的
	DCBIZLOG(DCLOG_LEVEL_INFO, 0,"PERF RentLoad OldUser","LatnId[%d] LoadUseTime[%ldus] LoadAcctCounts[%ld][tps1=%d] LoadPrdCounts[%ld][tps2=%d] ToatalSendMsgCounts[%ld][tps3=%d] sqlperf:%s",nLatnId,lnUseTime,lnAcctCounts,nTps1,lnCounts,nTps2,lnSendMsgCount,nTps3, strStat.c_str());
	DCDATLOG("RB00007:%d!%ld!%ld!%d!%ld!%d!%ld!%d!%s",nLatnId,lnUseTime,lnAcctCounts,nTps1,lnCounts,nTps2,lnSendMsgCount,nTps3, strStat.c_str());
    DCPERFLOG((int)lnUseTime,"LatnId[%d] curUseTime[%ldus] curSendMsgCounts[%ld][tps=%d] LoadAcctCounts[%ld] LoadPrdCounts[%ld] sqlperf:%s",nLatnId,lnUseTime,lntmp,nTps3,lnAcctCounts,lnCounts,strStat.c_str());
     
	if (m_cfgPara.nOpenKpi)
	{		
		DCKpiSender::instance()->cycle_array_inc(m_ptrBPMon, monGroup, 1, "k1", NULL, lnCounts);
		DCKpiSender::instance()->cycle_array_inc(m_ptrBPMon, monGroup, 1, "k2", NULL, lnUseTime);
		DCKpiSender::instance()->cycle_array_inc(m_ptrBPMon, monGroup, 1, "k3", NULL, 0);
	}
	//重置统计信息
	p_dbm->get_statistic()->reset();
		
	//DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCLoadOldUser::loadOldUser","DCLoadOldUser::loadOldUser end,LatnId[%d] LoadAcctCounts[%ld] LoadPrdCounts[%ld]",nLatnId,lnAcctCounts,lnCounts);

	return 0;
}


//判断是否到定时处理时间
bool DCLoadOldUser::IsNeedDeal(DCDateTime dtLast,DCDateTime dtcur,const int nSetHour,DCDateTime dtLastStart)
{
	if(dtLast > dtcur)
		return false;

	char sztmp[10]={0},sztmp2[10]={0};
	char szSetTime[16]={0};
	sprintf(sztmp, "%s", (dtcur.ToString(string("YYYYMMDD"))).c_str());
	sztmp[8]=0;
	sprintf(szSetTime,"%s%02d0000",sztmp,nSetHour);
	DCDateTime dtnext;
	dtnext.FromString(szSetTime,string("YYYYMMDDHHNNSS"));

	if(dtLast < dtnext && (dtcur > dtnext || dtcur == dtnext))
		return true;

	sprintf(sztmp2, "%s", (dtLastStart.ToString(string("YYYYMMDD"))).c_str());
	sztmp2[8]=0;
	int nToday = atoi(sztmp);
	int nLastDay = atoi(sztmp2);
	if((dtLast>dtnext || dtLast==dtnext) && nLastDay<nToday)//上次开始日期比今天小，说明上次为跨日处理
		return true;
	
	return false;
}

//更新定时任务配置表处理时间
int DCLoadOldUser::SetLastDealTime(const DCDateTime cur_time,const STEventPro stEvn,bool isStart)
{	
    long lnCycleEventId=stEvn.lnCycleEventId;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::SetLastDealTime begin lnCycleEventId[%ld]",lnCycleEventId);
	char szLastTime[16]={0};
	sprintf(szLastTime, "%s", (cur_time.ToString(string("YYYYMMDDHHNNSS"))).c_str());
	szLastTime[14]=0;
	string sbuf, sqlname;
    try
    {
    	if(isStart)
			sqlname = "UpdateCycleEventStart";
		else
			sqlname = "UpdateCycleEvent";
        UDBSQL* pUpdate = p_dbm->GetSQL(sqlname.c_str());			
		if(pUpdate==NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "DCLoadOldUser::SetLastDealTime","GetSQL [%s] failed",sqlname.c_str());
			return -1;
		} 
        pUpdate->UnBindParam();
        pUpdate->BindParam(1,szLastTime);
        pUpdate->BindParam(2,lnCycleEventId);
		pUpdate->GetSqlString(sbuf);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldUser::SetLastDealTime","Do SQL:Begin to do %s,sql :[%s]",sqlname.c_str(),sbuf.c_str());	
        
        pUpdate->Execute();
		pUpdate->Connection()->Commit();
		pUpdate->Close();
    }
    catch (UDBException& e)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "DCLoadOldUser::SetLastDealTime", "InitCycEvtPro throw out abnormity! sql[%s] %s", sbuf.c_str(), e.ToString());
        return -1;
    }
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::SetLastDealTime end");
    return 0;
}

//更新定时任务配置表处理时间
int DCLoadOldUser::SetLastDealTimeDcfServ(const DCDateTime cur_time, const int nProType, const int nProcId, const int nLatnId, bool isStart)
{	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::SetLastDealTime begin nProType[%d]nProcId[%d]nLatnId[%d]isStart[%d]",nProType, nProcId, nLatnId, isStart);
	char szLastTime[16]={0};
	sprintf(szLastTime, "%s", (cur_time.ToString(string("YYYYMMDDHHNNSS"))).c_str());
	szLastTime[14]=0;
	string sbuf, sqlname;
    try
    {
    	if(isStart)
			sqlname = "UpdateCycleEventStartDcfServ";
		else
			sqlname = "UpdateCycleEventDcfServ";
        UDBSQL* pUpdate = p_dbm->GetSQL(sqlname.c_str());			
		if(pUpdate==NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "DCLoadOldUser::SetLastDealTime","GetSQL [%s] failed",sqlname.c_str());
			return -1;
		} 
        pUpdate->UnBindParam();
        pUpdate->BindParam(1,szLastTime);
        pUpdate->BindParam(2,nProType);	//PROC_TYPE
		pUpdate->BindParam(3,nProcId);	//PROC_ID
		pUpdate->BindParam(4,nLatnId);	//LATN_ID
		pUpdate->GetSqlString(sbuf);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldUser::SetLastDealTime","Do SQL:Begin to do %s,sql :[%s]",sqlname.c_str(),sbuf.c_str());	
        
        pUpdate->Execute();
		pUpdate->Connection()->Commit();
		pUpdate->Close();
    }
    catch (UDBException& e)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "DCLoadOldUser::SetLastDealTime", "InitCycEvtPro throw out abnormity! sql[%s] %s", sbuf.c_str(), e.ToString());
        return -1;
    }
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::SetLastDealTime end");
    return 0;
}


//提取资料账户切换，发送上一个账户的套餐实例消息
int DCLoadOldUser::AcctChangeDeal(long lnAcctId, int nLatnId, long lnAcctCounts, int nCurAcctPrdCount,long lnLastSerId,long &lnSendMsgCount,T_UserInfo tUserInfo)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::AcctChangeDeal begin,lnAcctId[%ld],nLatnId[%d],lnAcctCounts[%ld],nCurAcctPrdCount[%d],lnLastSerId[%ld],nUserMode[%d],nObversionMode[%d]",lnAcctId,nLatnId,lnAcctCounts,nCurAcctPrdCount,lnLastSerId,tUserInfo.nUserMode,tUserInfo.nObversionMode);
	
	if(m_cfgPara.nContralGetOfrInst==1)//获取套餐实例开关
	// 查找套餐实例信息和明细
	//if(m_setOfrInstId.size()>0)
	{
		if( m_pComboAdpt->getAllAcctOfrfInstance(lnAcctId ,nLatnId,lnLastSerId)<0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldUser::AcctChangeDeal","getAllAcctOfrfInstance failed,AcctId[%ld].",lnAcctId);
			return -1;
		}
	}

     //预付费流程需剔除掉互斥销售品
	 if (PROC_TYPE_PRE_NORMAL == m_inputParam.nProType && MutexOfr() )
	 {
		 DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldUser::AcctChangeDeal","MutexOfr() fault!,AcctId[%ld].",lnAcctId);
         return -1;
	 }
	
	long lnOfrInstId = 0, lnPrdInstId=0;
	long lnPidId = self();//getpid()
	multimap<long,ocs::DetailInst>::iterator iter;
	int nPrdInstNum=0,nSubInstNum=0,nOfrInstNum=0;
	long lnTmp=0;
	vector<long> vecTransferId;
	
	int nInstSize = m_pComboAdpt->m_vecOfrInstOutput.size();	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","nInstSize is[%d],detail below:",nInstSize);	
	for ( std::vector<ocs::OfrInst>::iterator tIter = m_pComboAdpt->m_vecOfrInstOutput.begin(); tIter != m_pComboAdpt->m_vecOfrInstOutput.end(); ++tIter)
	{		
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","lnOfrId[%ld],lnOfrInstId[%ld],nCalcPriority[%d],szEffDate[%s],szExpDate[%s],szEventEffDate[%s],szEventExpDate[%s],nCalcPriority[%d],nDynInstFlag[%d],lnSelectGroupID[%ld]",tIter->lnOfrId,tIter->lnOfrInstId,tIter->nCalcPriority,tIter->szEffDate.c_str(),tIter->szExpDate.c_str(),tIter->szEventEffDate.c_str(),tIter->szEventExpDate.c_str(),tIter->nCalcPriority,tIter->nDynInstFlag,tIter->lnSelectGroupID);
	}	
	if(nInstSize>0)
	{
		//判断是否为大账户，跨账户，是则存入特殊分流账户表TB_CROSS_ACCOUNT_[LATN_ID]
		long lnSpecialId=0;
		int nAcctType = 0;
		nAcctType = m_pComboAdpt->CheckSpecialBigAcct(lnAcctId ,nLatnId, nCurAcctPrdCount,lnSpecialId);
		if(nAcctType <0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldUser::AcctChangeDeal","CheckSpecialBigAcct failed,AcctId[%ld].",lnAcctId);
			return -1;
		}
		
		//组装头部信息
		m_pComboAdpt->SetHeadInfo(nLatnId,m_nBillingCycleId,lnAcctId,nAcctType,tUserInfo);
		//int nGroupNum = nInstSize%m_nInstSendBatNum==0?(int)(nInstSize/m_nInstSendBatNum):(int)(nInstSize/m_nInstSendBatNum)+1;
		//m_pComboAdpt->m_stAcctHead.GroupNum = nGroupNum;//新增接口传递一组消息个数
		int nGroupNum = 1,nPriority=0,nLastPriority=0,nFeeType=0;			
		for(int i=0;i<nInstSize;)
		{
			ocs::IAcctBodyReq stAcctBodyReq;
			int j=0;
			nPrdInstNum=0;nSubInstNum=0;nOfrInstNum=0;

			m_pComboAdpt->m_stAcctHead.crossserv.clear();//避免重复发送
			m_pComboAdpt->m_stAcctHead.serv_id.clear();

			if(i==0)//账户下新增用户列表，用于账户变更操作,只第一条消息带
			{
				multimap<long,STTransferAcctPrd>::iterator iterprdbeg = m_pComboAdpt->m_multimapTransferAcct.lower_bound(lnAcctId);
				multimap<long,STTransferAcctPrd>::iterator iterprdend = m_pComboAdpt->m_multimapTransferAcct.upper_bound(lnAcctId);
				for(;iterprdbeg!=iterprdend;iterprdbeg++)
				{
					lnTmp = iterprdbeg->second.lnPrdInstId;
					m_pComboAdpt->m_stAcctHead.crossserv.push_back(lnTmp);
					lnTmp = iterprdbeg->second.lnTransferId;
					vecTransferId.push_back(lnTmp);
				}
				//现每个用户都要做过户操作，所以从servid取，crossserv暂时保存
				for(int iprd=0;iprd<m_pComboAdpt->m_vecGroupPrdInstId.size();iprd++)
				{
					m_pComboAdpt->m_stAcctHead.serv_id.push_back(m_pComboAdpt->m_vecGroupPrdInstId[iprd]);
				}
			}
			for(j=0;i<nInstSize;j++)
			{
				nPriority = m_pComboAdpt->m_vecOfrInstOutput[i].nCalcPriority;
				//m_cfgPara.nInstSendBatNum = 0不做限制 ，但是压缩文件大于1M需要写入DCA
				if(m_cfgPara.nInstSendBatNum > 0 && j>=m_cfgPara.nInstSendBatNum && (nLastPriority!=nPriority || nFeeType==FEE_TYPE_RENT || nFeeType==FEE_TYPE_PRODUCT))
				{
					break;
				}
				nLastPriority = nPriority;
				nFeeType = m_pComboAdpt->m_vecOfrInstOutput[i].nFeeType;
							
				lnOfrInstId = m_pComboAdpt->m_vecOfrInstOutput[i].lnOfrInstId;
				stAcctBodyReq.VOfrInst.push_back(m_pComboAdpt->m_vecOfrInstOutput[i]);
				iter=m_pComboAdpt->m_mmapOfrDetail.lower_bound(lnOfrInstId);
				for(;iter!=m_pComboAdpt->m_mmapOfrDetail.upper_bound(lnOfrInstId);iter++)
				{
					stAcctBodyReq.VDetailInst.push_back(iter->second);
				}
				switch(m_pComboAdpt->m_vecOfrInstOutput[i].nFeeType)
				{
				case 2:
					nPrdInstNum++;
					break;
				case 3:
					nSubInstNum++;
					break;
				default:
					nOfrInstNum++;
					break;
				}
				i++;
			}
			if(i==nInstSize)
			{
				m_pComboAdpt->m_stAcctHead.BillingFlag = 0;//标识一组消息中最后一条
				//最后一条消息也做过户
				if(m_pComboAdpt->m_stAcctHead.serv_id.size()==0)
				{
					for(int iprd=0;iprd<m_pComboAdpt->m_vecGroupPrdInstId.size();iprd++)
					{
						m_pComboAdpt->m_stAcctHead.serv_id.push_back(m_pComboAdpt->m_vecGroupPrdInstId[iprd]);
					}
				}
			}
			else
			{
				m_pComboAdpt->m_stAcctHead.BillingFlag = 1;
			}
			m_pComboAdpt->m_stAcctHead.GroupNum = nGroupNum++;//新增接口传递一组消息个数
			
			RentUserInfo stRentInfo;
			stRentInfo.lnSpecialId = lnSpecialId;
			stRentInfo.nAcctType = nAcctType;
			stRentInfo.nLatnId = nLatnId;
			stRentInfo.nBillingCycleId = m_nBillingCycleId;
			stRentInfo.nPrdInstNum = nPrdInstNum;
			stRentInfo.nSubInstNum = nSubInstNum;
			stRentInfo.nOfrInstNum = nOfrInstNum;
			stRentInfo.lnPidId = lnPidId;
			stRentInfo.stFmtHead = m_pComboAdpt->m_stFmtHead;
			stRentInfo.stAcctHead = m_pComboAdpt->m_stAcctHead;
			stRentInfo.stAcctBodyReq = stAcctBodyReq;
			stRentInfo.nGroupNum = nGroupNum;
			if(tUserInfo.nUserMode == USER_OLD)
                stRentInfo.nMsgType = MSGTYPE_OLD;
            else
                stRentInfo.nMsgType = MSGTYPE_STOP;//复机流程
			stRentInfo.lnRouteAcctId = lnSpecialId;			
			if(vecTransferId.size()>0)
			{
				stRentInfo.vecTransferId = vecTransferId;
				vecTransferId.clear();
			}
			
			int nRet = m_pCallServer->process(&stRentInfo,MSGCTRL_MODE_NORMAL,p_dbm);
			if(nRet < 0)
	        {
	            DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::AcctChangeDeal","call DCCallZKServerMaker::process fail");
				m_pComboAdpt->releaseUserMap(true);//清空数据
	            //return -1;
	            break;
	        }
			lnSendMsgCount++;
            if(tUserInfo.nUserMode == USER_OLD)
			    WriteRentLog(nLatnId,lnPidId,nPrdInstNum,nSubInstNum,nOfrInstNum,false);
		}
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","lnAcctId[%ld] ofr inst size is empty",lnAcctId);
	}
	m_pComboAdpt->releaseUserMap(true);//清空数据
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::AcctChangeDeal end ");
	return 0;
}

//互斥表互斥
int DCLoadOldUser::MutexOfr()
{
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::MutexOfr begin,m_vecOfrInstOutput.size[%d]",m_pComboAdpt->m_vecOfrInstOutput.size());

	std::multimap<int,ST_CycOfrRel>  mapCycOfrRel;
	std::multimap<int,ST_CycOfrRel>::iterator it;
	std::multimap<int,ST_CycOfrRel>::iterator it_begin;
	std::multimap<int,ST_CycOfrRel>::iterator it_end;
	
	std::map<long,int>mapEraseOfrId;  //要删除的套餐id
	std::map<long,int>::iterator itEraOfr;

	int nRet = 0;
	bool bEraseFlag = false;  //删除标志
	nRet = LoadCycOfrRel( mapCycOfrRel);
	if(nRet)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"","LoadCycOfrRel error");
		return -1;
	}
	//找出互斥掉的套餐
	for ( std::vector<ocs::OfrInst>::iterator tIter = m_pComboAdpt->m_vecOfrInstOutput.begin(); tIter != m_pComboAdpt->m_vecOfrInstOutput.end(); ++tIter)
	{
		std::multimap<int,ST_CycOfrRel>::iterator it;
		std::multimap<int,ST_CycOfrRel>::iterator it_begin = mapCycOfrRel.lower_bound(tIter->lnOfrId);
		std::multimap<int,ST_CycOfrRel>::iterator it_end = mapCycOfrRel.upper_bound(tIter->lnOfrId);

		for ( it = it_begin; it != it_end; ++it )
		{
			bEraseFlag =  FindOfrId(it->second.nRefOfrId);
			if(bEraseFlag)
			{
			    mapEraseOfrId.insert(std::map<long,int>::value_type(it->second.nRefOfrId,1));
			}		
			
		}
	}

	//删除互斥掉的套餐
	for ( std::vector<ocs::OfrInst>::iterator tIter2 = m_pComboAdpt->m_vecOfrInstOutput.begin(); tIter2 != m_pComboAdpt->m_vecOfrInstOutput.end();)
	{
		itEraOfr = mapEraseOfrId.find(tIter2->lnOfrId);
        if( itEraOfr != mapEraseOfrId.end())
        {
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","lnOfrId[%ld] is been Mutex,need erase",tIter2->lnOfrId);
		    tIter2 = m_pComboAdpt->m_vecOfrInstOutput.erase(tIter2);			
	    }
        else
        {
		    tIter2++ ;
	    }
	}	
	
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::MutexOfr end,m_vecOfrInstOutput.size[%d]",m_pComboAdpt->m_vecOfrInstOutput.size());
	return 0;
}

int DCLoadOldUser::LoadCycOfrRel(std::multimap<int,ST_CycOfrRel> & mapCycOfrRel)
{
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::LoadCycOfrRel begin");

    try
    {
		UDBSQL *m_pCycOfr = p_dbm->GetSQL("CYCLE_OFR_REL");
		if (NULL == m_pCycOfr )
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"","Get sql CYCLE_OFR_REL error");
			return -1;
		}
		
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","Get Sql CYCLE_OFR_REL OK!");
		int nOfrId(-1);
		ST_CycOfrRel stCycOfrRel;
		mapCycOfrRel.clear();
		m_pCycOfr->UnBindParam();			
		m_pCycOfr->Execute();
		while(m_pCycOfr->Next())
		{
		    m_pCycOfr->GetValue(1,nOfrId);  //main_ofr_id
			m_pCycOfr->GetValue(2,stCycOfrRel.nRefOfrId);  //ref_ofr_id
			m_pCycOfr->GetValue(3,stCycOfrRel.nIfDeduct);
			m_pCycOfr->GetValue(4,stCycOfrRel.nDictValue);
			m_pCycOfr->GetValue(5,stCycOfrRel.nConstValue);
			m_pCycOfr->GetValue(6,stCycOfrRel.nPriGroupId);		
			
			mapCycOfrRel.insert(multimap<int,ST_CycOfrRel>::value_type(nOfrId,stCycOfrRel));
		}

		m_pCycOfr->Close();
    }
	catch(UDBException &e)
    {		
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"","LoadCycOfrRel error,ErrorCode[%d],error info[%s]",-e.GetErrorCode(),e.ToString());
        return -e.GetErrorCode();
    }
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::LoadCycOfrRel end,size:%d",mapCycOfrRel.size());

	return 0;
}

bool DCLoadOldUser::FindOfrId(long lnOfrId)
{
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::FindOfrId begin,lnOfrId[%ld]",lnOfrId);

	bool bFind = false;
    std::vector<ocs::OfrInst>::iterator it = m_pComboAdpt->m_vecOfrInstOutput.begin();
	for(; it!=m_pComboAdpt->m_vecOfrInstOutput.end(); )
    {
        if( it->lnOfrId == lnOfrId)
        {
		    bFind = true; 
			break;
	    }
        else
        {
		    it++ ;
	    }
    }
    
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::FindOfrId end,bFind[%d]",bFind);
	return bFind;
}

//更新消息发送日志表
bool DCLoadOldUser::WriteRentLog(int nLatnId,long lnPidId,int nPrdInstNum,int nSubInstNum,int nOfrInstNum, bool bEnd)
{
	string strsql;
    char szSqlName[64]={0};
    char szCurTime[16] = {0};
    
    m_nPrdInstNum += nPrdInstNum;
    m_nSubInstNum += nSubInstNum;
    m_nOfrInstNum += nOfrInstNum;
    if(++m_writeLogCount % 50 > 0 && !bEnd)
    {
        return true;
    }
    PublicLib::GetTime(szCurTime, YYYYMMDDHHMMSS);
    
    for (int i = 0; i < 2; ++i) // 重试一次
    {
        try
        {
            sprintf(szSqlName, "AppendRentSendLog|%d", nLatnId);
            UDBSQL *pUpdate = p_dbm->GetSQL(szSqlName);
            if (pUpdate == NULL)
            {
                DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "DCLoadOldUser::WriteRentLog", "not find sql[%s]", szSqlName);
                return false;
            }
            pUpdate->UnBindParam();
            pUpdate->BindParam(1, m_nPrdInstNum);
            pUpdate->BindParam(2, m_nSubInstNum);
            pUpdate->BindParam(3, m_nOfrInstNum);
            pUpdate->BindParam(4, szCurTime);
            pUpdate->BindParam(5, lnPidId);
            pUpdate->BindParam(6, m_nBillingCycleId);

            pUpdate->GetSqlString(strsql);
            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "DCLoadOldUser::WriteRentLog", "Do SQL: begin to do [AppendRentSendLog],sql :[%s] ", strsql.c_str());

            pUpdate->Execute();
            if (pUpdate->GetRowCount() > 0)
            {
                pUpdate->Connection()->Commit();
                pUpdate->Close();
                return true;
            }
            pUpdate->Close();

            sprintf(szSqlName, "InsertRentSendLog|%d", nLatnId);
            UDBSQL *pExec = p_dbm->GetSQL(szSqlName);
            if (pExec == NULL)
            {
                DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "DCLoadOldUser::WriteRentLog", "not find sql[%s]", szSqlName);
                return false;
            }
            pExec->UnBindParam();
            pExec->BindParam(1, lnPidId);
            pExec->BindParam(2, m_nBillingCycleId);
            pExec->BindParam(3, m_nPrdInstNum);
            pExec->BindParam(4, m_nSubInstNum);
            pExec->BindParam(5, m_nOfrInstNum);

            pExec->GetSqlString(strsql);
            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "DCLoadOldUser::WriteRentLog", "Do SQL: begin to do [InsertRentSendLog],sql :[%s] ", strsql.c_str());

            pExec->Execute();
            if (pExec->GetRowCount() > 0)
            {
                pExec->Connection()->Commit();
                pExec->Close();
                return true;
            }
            pExec->Connection()->Rollback();
            pExec->Close();
        }
        catch (std::exception &e)
        {
            if (i == 0)
            {
                p_dbm->CheckReset();
            }
            else
            {
                DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "DCLoadOldUser::WriteRentLog", "WriteRentLog failed: [%s] sql[%s]", e.what(), strsql.c_str());
            }
        }
    }
    
    return false;
}

//处理新装用户
int DCLoadOldUser::DealNewUser()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::DealNewUser begin");

	int nRet = 0;

	std::list<string>::iterator it = m_ltLatn.begin();
	for(; it != m_ltLatn.end(); it++)
	{
        m_nLatnId = atoi((*it).c_str());
		//如果账期或本地网变更,则重新刷新账期
		char szTime[16]={0};
		PublicLib::GetTime(szTime, YYYYMMDD);
		int nBillingCycleID = atoi(szTime);
		if (m_nBillingCycleId != nBillingCycleID || m_nLatnId!=DCDataCenter::instance()->m_nLatnId)
		{
			m_nBillingCycleId = nBillingCycleID;
			DCDataCenter::instance()->RefreshBillingCycle((int)(nBillingCycleID/100),m_nLatnId);
			if(!((m_pComboAdpt->m_pEventType)->reloadCondition()))
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCLoadOldUser::DealNewUser","DCRDEventType reloadCondition error\n");
				return -1;
			}
		}	

		if( m_pComboAdpt->LoadSpecialAcct(m_nLatnId)<0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldUser::DealNewUser","LoadSpecialAcct failed, LatnId[%d].",m_nLatnId);
			return -1;
		}

		//处理账户变更
		nRet = DealTransferPrd(m_nLatnId);
	    if (nRet < 0)
	    {
	        if(ReConnect()) //重连失败
			{
		        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::DealNewUser",	"ReConnect failed,exit");	
				return -1;
			}				        

			nRet = DealTransferPrd(m_nLatnId);
		    if (nRet < 0)
		    {
		    	DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::DealNewUser","DealFeeCacheAcct failed");
		        return -1;
		    }
	    }
		
		//查询新装
		nRet = QueryInstChange(m_nLatnId);
	    if (nRet < 0)
	    {
	        if(ReConnect()) //重连失败
			{
		        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::DealNewUser",	"ReConnect failed,exit");	
				return -1;
			}				        

			nRet = QueryInstChange(m_nLatnId);
		    if (nRet < 0)
		    {
		    	DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::DealNewUser","QueryInstChange failed");
		        return -1;
		    }
	    }
				
	}

    if(PROC_TYPE_PRE_NORMAL == m_inputParam.nProType)
    {
		DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCLoadOldUser::DealNewUser","Send New user finished,sleep %d s and then deal Frozen user",m_cfgPara.nNewUserSleepTime);
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCLoadOldUser::DealNewUser","Send New user finished,sleep %d s and then deal old user",m_cfgPara.nNewUserSleepTime);
	}
	DCDATLOG("RA00002:");
	ACE_Time_Value aSleepS(m_cfgPara.nNewUserSleepTime);
	ACE_OS::sleep(aSleepS); 
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::DealNewUser end");
    return 0;
}

//处理新装用户
int DCLoadOldUser::DealNewUserDcfServ()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::DealNewUser begin");

	int nRet = 0;

	//如果账期或本地网变更,则重新刷新账期
	char szTime[16]={0};
	PublicLib::GetTime(szTime, YYYYMMDD);
	int nBillingCycleID = atoi(szTime);
	if (m_nBillingCycleId != nBillingCycleID || m_nLatnId!=DCDataCenter::instance()->m_nLatnId)
	{
		m_nBillingCycleId = nBillingCycleID;
		DCDataCenter::instance()->RefreshBillingCycle((int)(nBillingCycleID/100),m_nLatnId);		
		if(!((m_pComboAdpt->m_pEventType)->reloadCondition()))
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCLoadOldUser::DealNewUser","DCRDEventType reloadCondition error\n");
			return -1;
		}
	}	

	if( m_pComboAdpt->LoadSpecialAcct(m_nLatnId)<0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldUser::DealNewUser","LoadSpecialAcct failed, LatnId[%d].",m_nLatnId);
		return -1;
	}

	//处理账户变更(不区分预付后附)
	if(PROC_TYPE_TRANSFER_PRD == m_inputParam.nProType)
	{
		nRet = DealTransferPrd(m_nLatnId);
		if (nRet < 0)
		{
			if(ReConnect()) //重连失败
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::DealNewUser",	"ReConnect failed,exit");	
				return -1;
			}

			nRet = DealTransferPrd(m_nLatnId);
			if (nRet < 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::DealNewUser","DealFeeCacheAcct failed");
				return -1;
			}
		}
	}
	else
	{
		//查询新装
		nRet = QueryInstChangeDcfServ(m_nLatnId);
		if (nRet < 0)
		{
			if(ReConnect()) //重连失败
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::DealNewUser",	"ReConnect failed,exit");	
				return -1;
			}

			nRet = QueryInstChangeDcfServ(m_nLatnId);
			if (nRet < 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::DealNewUser","QueryInstChange failed");
				return -1;
			}
		}
	}

    if(PROC_TYPE_PRE_NORMAL == m_inputParam.nProType)
    {
		DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCLoadOldUser::DealNewUser","Send New user finished,sleep %d s and then deal Frozen user",m_cfgPara.nNewUserSleepTime);
	}
	else if(PROC_TYPE_POST_NORAML == m_inputParam.nProType)
	{
		DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCLoadOldUser::DealNewUser","Send New user finished,sleep %d s and then deal old user",m_cfgPara.nNewUserSleepTime);
	}
	DCDATLOG("RA00002:");
	ACE_Time_Value aSleepS(m_cfgPara.nNewUserSleepTime);
	ACE_OS::sleep(aSleepS); 
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::DealNewUser end");
    return 0;
}

int DCLoadOldUser::UpdateDealState(const long ofr_inst_change_id, const int nLatnId)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","UpdateDealState begin,ofr_inst_change_id[%d]",ofr_inst_change_id);

	// <sql name="UpdateDealStateChange" bind="2" sub="553,554,555,556,558,562,563,564,566">   
	//   UPDATE IBASELECT.SA5G_HB_INTERFACE_CHANGE_[@]@CUST1.AHDX 
	//   SET deal_state=11
	//   WHERE Interface_ofr_change_id = ?
	//   AND deal_state=21
	// </sql>  

	int nAffect = 0;
    char sqlname[50] = {0};
    string sbuf;

	try
	{
		sprintf(sqlname, "UpdateDealStateChange|%d", nLatnId);
		UDBSQL* pUpdate = p_dbm->GetSQL(sqlname);
		if(pUpdate==NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "UpdateDealState","GetSQL [%s] failed",sqlname);
			return -1;
		} 
		pUpdate->UnBindParam();
		pUpdate->BindParam(1,ofr_inst_change_id);

		pUpdate->GetSqlString(sbuf);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"UpdateDealState","Do SQL:Begin to do %s,sql :[%s]",sqlname,sbuf.c_str());
		
		pUpdate->Execute();
		nAffect = pUpdate->GetRowCount();
		pUpdate->Connection()->Commit();
		pUpdate->Close();
	}
	catch (UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"UpdateDealState","InitCycEvtPro throw out abnormity! sql[%s] %s",sbuf.c_str(),e.ToString());
        return -1;
    }

	if(0 == nAffect)
	{
		//未能取到对应任务的正确的表数据
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"UpdateDealState","GetRowCount[%d]Get task error!", nAffect);
		return -1;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","UpdateDealState end");
	return 0;
}

//查询interface_ofr_change记录 销售品变更接口表
int DCLoadOldUser::QueryInstChange(int nLatnId)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::QueryInstChange begin,nProcNum[%d],m_nProcId[%d]",m_inputParam.nProcNum,m_nProcId);
    
	char buf[64] = {0};
	int nRet=0, nState=0, nNum=0, nInstSize=0,nInstSizeBefore=0;
	std::string strQuerySQL = "";
	set<long> setAcctId,setPrdGroup;
	long lnOfrInstId = 0;
	long lnPidId = self();//getpid()
	multimap<long,ocs::DetailInst>::iterator iter;
	int nPrdInstNum=0,nSubInstNum=0,nOfrInstNum=0;
	
	char szTime[24] = {0};
	PublicLib::GetTime(szTime,YYYYMM);
	int nBillingCycleID = atoi(szTime);

	vector<T_instChange> vecChangeInst;
	set<long> setBatchCrossAcct,setBatchAcct;//用于过滤同一批处理过的账户
	set<long>::iterator iterBacthAcct;

	char monGroup[50] = {0};
	
	if (m_cfgPara.nOpenKpi)
	{		
		DCKpiSender::instance()->GetFullGroup("TraceKpi", nLatnId, monGroup);
	}

	struct timeval tm;
	gettimeofday(&tm, NULL);
	unsigned long lnbegin = (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);
	unsigned long lnend = 0, lnUseTime = 0;
	
	try
    {   
    	char szSqlName[64]={0};
		if(PROC_TYPE_PRE_NORMAL == m_inputParam.nProType) //预付费
		{
			sprintf(szSqlName,"QueryOfrInstChangePre|%d",nLatnId);
		}
		else
		{
			sprintf(szSqlName,"QueryOfrInstChange|%d",nLatnId);
		}
		
        UDBSQL* pQuery = p_dbm->GetSQL(szSqlName);
        if(!pQuery)
        {
            DCBIZLOG(DCLOG_LEVEL_ERROR,0,"DCLoadOldUser::QueryInstChange","Get SQL QueryOfrInstChange error.");
            return -1;
        }
		T_instChange inst;

        pQuery->UnBindParam();
		pQuery->BindParam(1,m_inputParam.nProcNum);
		pQuery->BindParam(2,m_nProcId);
		pQuery->GetSqlString(strQuerySQL);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldUser::QueryInstChange","Do SQL:Begin to do,sql :[%s]",strQuerySQL.c_str());
        pQuery->Execute();
		nNum = 0;
        
        while(pQuery->Next())
        {
        	pQuery->GetValue(1,buf);
            inst.ofr_inst_change_id = atol(buf);               
			pQuery->GetValue(2,buf);
            inst.ofr_inst_id = atol(buf);
			pQuery->GetValue(3,buf);
            inst.crt_date.FromString(buf,"YYYYMMDDHHNNSS"); 
			inst.nLatnId = nLatnId;

			vecChangeInst.push_back(inst);
        }
		pQuery->Close();

		for(int idx=0;idx<vecChangeInst.size();idx++)
		{
			inst.ofr_inst_change_id = vecChangeInst[idx].ofr_inst_change_id;
			inst.ofr_inst_id = vecChangeInst[idx].ofr_inst_id;
			inst.nLatnId = vecChangeInst[idx].nLatnId;
			
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","deal New user:ofr_inst_change_id[%ld],ofr_inst_id[%ld]",inst.ofr_inst_change_id,inst.ofr_inst_id);
			
			m_pComboAdpt->releaseUserMap(true);
			nNum++;
			
			nRet = m_pComboAdpt->GetOfrInstanceInfo(inst.ofr_inst_id,inst.nLatnId,inst.ofr_id);	
			if(nRet<0)
			{
		        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::QueryInstChange",	"QueryInstOtherInfo failed by prdInstId[%ld],nLatnId[%d]",inst.ofr_detail_inst_ref_id,inst.nLatnId);	

		        if(ReConnect()) //重连失败
		        {
			        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::QueryInstChange",	"ReConnect failed,exit");	
			        return -1;
		        }
				else  //重连成功,需要获取套餐实例
				{
				    DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"DCLoadOldUser::QueryInstChange", "ReConnect success,load ofr_inst_change_id[%ld] once again",inst.ofr_inst_change_id);
					nRet = m_pComboAdpt->GetOfrInstanceInfo(inst.ofr_inst_id,inst.nLatnId,inst.ofr_id);
					if(nRet<0)
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::QueryInstChange",	"QueryInstOtherInfo failed by prdInstId[%ld],nLatnId[%d]",inst.ofr_detail_inst_ref_id,inst.nLatnId);	
                        return -1;
					}
				}

			}

			nState = 1;
			if(nRet==0)//==1标识套餐实例失效或未获取到资料
			{
				setAcctId.clear();
				
				//判断是否为大账户，跨账户，是则存入特殊分流账户表TB_CROSS_ACCOUNT_[LATN_ID]
				long lnSpecialId=0;
				int nAcctType = m_pComboAdpt->CheckSpecialCrossAcct(nLatnId, inst.ofr_inst_id,true, lnSpecialId,setAcctId);				
				if(nAcctType <0)
				{
					if(nAcctType==-2)//套餐实例没有找到归属账户
					{
                        nRet = UpdateInstDealed(inst.ofr_inst_change_id, 2, nLatnId);
                        if (nRet < 0)
                        {
                            if (ReConnect()) //重连失败
                            {
                                DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::QueryInstChange", "ReConnect failed,exit");
                                return -1;
                            }
                            else
                            {
                                DCBIZLOG(DCLOG_LEVEL_INFO, 0, "DCLoadOldUser::QueryInstChange", "ReConnect success,UpdateInstDealed ofr_inst_change_id[%ld] 2 once again", inst.ofr_inst_change_id);
                                if (UpdateInstDealed(inst.ofr_inst_change_id, 2, nLatnId) < 0)
                                {
                                    DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "DCLoadOldUser::QueryInstChange", "ReConnect success,UpdateInstDealed ofr_inst_change_id[%ld] 2 failed once again", inst.ofr_inst_change_id);
                                    return -1;
                                }
                            }
                        }
						continue;
					}
					DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldUser::QueryInstChange","CheckSpecialCrossAcct failed,OfrInstId[%ld].",inst.ofr_inst_id);
					return -1;
				}
				if(PROC_TYPE_PRE_NORMAL != m_inputParam.nProType)
				{
				    if(nAcctType == SPECIAL_ACCT_COMM)
				    {
					    iterBacthAcct = setBatchAcct.find(lnSpecialId);
				    }
				    else
				    {
					    iterBacthAcct = setBatchAcct.end();//过滤同一批里处理过的
				    }
				    if(nAcctType != SPECIAL_ACCT_COMM || iterBacthAcct!=setBatchAcct.end())//大账户或跨账户的套餐实例变更不走新装流程，并更新记录状态为结束
				    {
					    nRet = UpdateInstDealed(inst.ofr_inst_change_id,nState,nLatnId);
					    if (nRet < 0)
					    {
				            if(ReConnect()) //重连失败
						    {
							    DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::QueryInstChange",	"ReConnect failed,exit");	
							    return -1;
						    }
						    else
						    {
                            	DCBIZLOG(DCLOG_LEVEL_INFO, 0, "DCLoadOldUser::QueryInstChange", "ReConnect success,UpdateInstDealed ofr_inst_change_id[%ld] once again", inst.ofr_inst_change_id);
								if(UpdateInstDealed(inst.ofr_inst_change_id,nState,nLatnId)<0)
								{
                                	DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "DCLoadOldUser::QueryInstChange", "ReConnect success,UpdateInstDealed ofr_inst_change_id[%ld] failed once again", inst.ofr_inst_change_id);
								    return -1;
							    }
						    }	
					    }					
					    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","nAcctType[%d] is not normal acct",nAcctType);
					    continue;
				    }
				}
				setBatchAcct.insert(lnSpecialId);
							
				m_pComboAdpt->releaseUserMap(true);
				
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","New User,ofr_inst_change_id[%ld],setAcctId is[%d]",inst.ofr_inst_change_id,setAcctId.size());
				if(!setAcctId.empty())
				{
					if(false==m_pComboAdpt->LoadUserByAcctList(setAcctId,nLatnId,lnSpecialId,nAcctType,setPrdGroup))
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldUser::QueryInstChange","LoadUserByAcctList failed,AcctId/CrossModGroupId[%ld] AcctType[%d].",lnSpecialId,nAcctType);
						return -1;
					}
					/*
                    if(PROC_TYPE_PRE_NORMAL == m_inputParam.nProType)
                    {                    
					    nInstSizeBefore = m_pComboAdpt->m_vecOfrInstOutput.size();
					    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","inst change,nInstSizeBefore is[%d]",nInstSizeBefore);

	                    for ( std::vector<ocs::OfrInst>::iterator tIter = m_pComboAdpt->m_vecOfrInstOutput.begin(); tIter != m_pComboAdpt->m_vecOfrInstOutput.end();)
	                    {
                            if(inst.ofr_id != tIter->lnOfrId)
                            {
			                    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","lnOfrId[%ld] is not order ofr,need erase",tIter->lnOfrId);
		                        tIter = m_pComboAdpt->m_vecOfrInstOutput.erase(tIter);			
	                        }
                            else
                            {
		                        tIter++ ;
	                        }
	                    }	
					}*/
					nInstSize = m_pComboAdpt->m_vecOfrInstOutput.size();
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","inst change,ofr_inst_change_id[%ld],nInstSize is[%d],detail below:",inst.ofr_inst_change_id,nInstSize);
	                for ( std::vector<ocs::OfrInst>::iterator tIter = m_pComboAdpt->m_vecOfrInstOutput.begin(); tIter != m_pComboAdpt->m_vecOfrInstOutput.end(); ++tIter)
	                {		
		                DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","lnOfrId[%ld],lnOfrInstId[%ld],nCalcPriority[%d],szEffDate[%s],szExpDate[%s],szEventEffDate[%s],szEventExpDate[%s],nCalcPriority[%d],nDynInstFlag[%d]",tIter->lnOfrId,tIter->lnOfrInstId,tIter->nCalcPriority,tIter->szEffDate.c_str(),tIter->szExpDate.c_str(),tIter->szEventEffDate.c_str(),tIter->szEventExpDate.c_str(),tIter->nCalcPriority,tIter->nDynInstFlag);
	                }

					T_UserInfo tUserInfo;					
					tUserInfo.nObversionMode = 0;
					tUserInfo.nUserMode = USER_NEW;
					char szTriggerCycle[9] = {0};
			        strncpy(szTriggerCycle,inst.crt_date.ToString(string("YYYYMMDDHHNNSS")).c_str(),8);
			        szTriggerCycle[8]='\0';
			        tUserInfo.nTriggerCycle = atoi(szTriggerCycle);
					
					//组装消息发送
					m_pComboAdpt->SetHeadInfo(inst.nLatnId,nBillingCycleID,lnSpecialId,nAcctType,tUserInfo);
					//int nGroupNum = nInstSize%m_nInstSendBatNum==0?(int)(nInstSize/m_nInstSendBatNum):(int)(nInstSize/m_nInstSendBatNum)+1;
					//m_pComboAdpt->m_stAcctHead.GroupNum = nGroupNum;//新增接口传递一组消息个数
					int nGroupNum = 1,nPriority=0,nLastPriority=0,nFeeType=0;
					for(int i=0;i<nInstSize;)
					{
						ocs::IAcctBodyReq stAcctBodyReq;
						int j=0;
						nPrdInstNum=0;nSubInstNum=0;nOfrInstNum=0;
						
						m_pComboAdpt->m_stAcctHead.crossserv.clear();//避免重复发送
						m_pComboAdpt->m_stAcctHead.serv_id.clear();

						if(i==0)
						{
							//现每个用户都要做过户操作，所以从servid取，crossserv暂时保存
							for(int iprd=0;iprd<m_pComboAdpt->m_vecGroupPrdInstId.size();iprd++)
							{
								m_pComboAdpt->m_stAcctHead.crossserv.push_back(m_pComboAdpt->m_vecGroupPrdInstId[iprd]);
								m_pComboAdpt->m_stAcctHead.serv_id.push_back(m_pComboAdpt->m_vecGroupPrdInstId[iprd]);
							}
						}
						
						for(j=0;i<nInstSize;j++)
						{
							nPriority = m_pComboAdpt->m_vecOfrInstOutput[i].nCalcPriority;
							//m_cfgPara.nInstSendBatNum = 0不做限制 ，但是压缩文件大于1M需要写入DCA
							if(m_cfgPara.nInstSendBatNum > 0 && j>=m_cfgPara.nInstSendBatNum && (nLastPriority!=nPriority || nFeeType==FEE_TYPE_RENT || nFeeType==FEE_TYPE_PRODUCT))
							{
								break;
							}
							nLastPriority = nPriority;
							nFeeType = m_pComboAdpt->m_vecOfrInstOutput[i].nFeeType;
							
							lnOfrInstId = m_pComboAdpt->m_vecOfrInstOutput[i].lnOfrInstId;
							stAcctBodyReq.VOfrInst.push_back(m_pComboAdpt->m_vecOfrInstOutput[i]);
							iter=m_pComboAdpt->m_mmapOfrDetail.lower_bound(lnOfrInstId);
							for(;iter!=m_pComboAdpt->m_mmapOfrDetail.upper_bound(lnOfrInstId);iter++)
							{
								stAcctBodyReq.VDetailInst.push_back(iter->second);
							}
							switch(m_pComboAdpt->m_vecOfrInstOutput[i].nFeeType)
							{
							case 2:
								nPrdInstNum++;
								break;
							case 3:
								nSubInstNum++;
								break;
							default:
								nOfrInstNum++;
								break;
							}
							i++;
						}
						if(i==nInstSize)
						{
							m_pComboAdpt->m_stAcctHead.BillingFlag = 0;//标识一组消息中最后一条
							//最后一条消息也做过户
							if(m_pComboAdpt->m_stAcctHead.serv_id.size()==0)
							{
								for(int iprd=0;iprd<m_pComboAdpt->m_vecGroupPrdInstId.size();iprd++)
								{
									m_pComboAdpt->m_stAcctHead.serv_id.push_back(m_pComboAdpt->m_vecGroupPrdInstId[iprd]);
								}
							}
						}
						else
						{
							m_pComboAdpt->m_stAcctHead.BillingFlag = 1;
						}
						m_pComboAdpt->m_stAcctHead.GroupNum = nGroupNum++;//新增接口传递一组消息个数
						
						RentUserInfo stRentInfo;
						stRentInfo.lnSpecialId = lnSpecialId;
						stRentInfo.nAcctType = nAcctType;
						stRentInfo.nLatnId = nLatnId;
						stRentInfo.nBillingCycleId = m_nBillingCycleId;
						stRentInfo.nPrdInstNum = nPrdInstNum;
						stRentInfo.nSubInstNum = nSubInstNum;
						stRentInfo.nOfrInstNum = nOfrInstNum;
						stRentInfo.lnPidId = lnPidId;
						stRentInfo.stFmtHead = m_pComboAdpt->m_stFmtHead;
						stRentInfo.stAcctHead = m_pComboAdpt->m_stAcctHead;
						stRentInfo.stAcctBodyReq = stAcctBodyReq;
						stRentInfo.nGroupNum = nGroupNum;
						stRentInfo.nMsgType = MSGTYPE_NEW;
						stRentInfo.vecTransferId.push_back(inst.ofr_inst_change_id);

						nRet = m_pCallServer->process(&stRentInfo,MSGCTRL_MODE_NORMAL,p_dbm);
						if(nRet < 0)
				        {
				            DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::QueryInstChange","call DCCallZKServerMaker::process fail,ofr_inst_change_id[%ld]",inst.ofr_inst_change_id);
				            nState = 2;
							break;
				        } 
						//仅全量处理流程写日志
						//WriteRentLog(nLatnId,lnPidId,nPrdInstNum,nSubInstNum,nOfrInstNum);
					}
				}
			}
			else
			{
				nState = 2;
			}

			//更新新装接口表状态
			nRet = UpdateInstDealed(inst.ofr_inst_change_id,nState,nLatnId);
			if (nRet < 0)
			{
		        if(ReConnect()) //重连失败
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::QueryInstChange",	"ReConnect failed,exit");	
					return -1;
				}else
				{
                    DCBIZLOG(DCLOG_LEVEL_INFO, 0, "DCLoadOldUser::QueryInstChange", "ReConnect success,UpdateInstDealed ofr_inst_change_id[%ld] once again", inst.ofr_inst_change_id);
					if(UpdateInstDealed(inst.ofr_inst_change_id,nState,nLatnId)<0)
					{
                        DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "DCLoadOldUser::QueryInstChange", "ReConnect success,UpdateInstDealed ofr_inst_change_id[%ld] failed once again", inst.ofr_inst_change_id);
						return -1;
					}
				}		
			}

        }
		
		gettimeofday(&tm, NULL);//性能日志
		lnend = (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);//性能日志
		lnUseTime = DIFFTIME_US(lnend, lnbegin);
		
		if (m_cfgPara.nOpenKpi)
		{			
			DCKpiSender::instance()->cycle_array_inc(m_ptrBPMon, monGroup, 2, "k1", NULL, nNum);
			DCKpiSender::instance()->cycle_array_inc(m_ptrBPMon, monGroup, 2, "k2", NULL, lnUseTime);
			DCKpiSender::instance()->cycle_array_inc(m_ptrBPMon, monGroup, 2, "k3", NULL, 0);
		}
	
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCLoadOldUser::QueryInstChange","QueryInstChange by %s find [%d] records!",strQuerySQL.c_str(),nNum);
    	if(nNum>0)
    	{
    		RentUserInfo stRentInfo;
			stRentInfo.stAcctHead.user_mode = USER_NEW;
			nRet = m_pCallServer->process(&stRentInfo,MSGCTRL_MODE_RESEND,p_dbm);
			if(nRet < 0)
	        {
	            DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::QueryInstChange","call DCCallZKServerMaker::process fail");
	        }
            
            // 输出统计信息
    		int nTps=0;
    		if(lnUseTime>0 && nNum>0)
    			nTps =(int)((nNum*1000000.0)/lnUseTime);    		
    		std::string strStat;
    		p_dbm->get_statistic()->to_string(strStat, true);	 //过滤未使用的
    		DCBIZLOG(DCLOG_LEVEL_INFO, 0,"PERF RentLoad QueryInstChange","LatnId[%d]LoadUseTime[%ldus] LoadInstCounts[%ld][tps=%d] sqlperf:%s",lnUseTime,nNum,nTps,nLatnId, strStat.c_str());
    		DCDATLOG("RD00005:%d!%ld!%ld!%d!%s",nLatnId,lnUseTime,nNum,nTps, strStat.c_str());
    		DCPERFLOG((int)lnUseTime,"LatnId[%d] LoadUseTime[%ldus] LoadInstCounts[%ld][tps=%d] sqlperf:%s",nLatnId,lnUseTime,nNum,nTps,strStat.c_str());
            //重置统计信息
    		p_dbm->get_statistic()->reset();
    	}
        m_pComboAdpt->releaseUserMap(true);
	}	
	catch(UDBException &e)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR,-e.GetErrorCode(),"DCLoadOldUser::QueryInstChange","error info[%s] sql[%s]",e.ToString(),strQuerySQL.c_str());
        return -e.GetErrorCode();
    }
	
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::QueryInstChange end!");
    return 0;
}

//查询interface_ofr_change记录 销售品变更接口表
int DCLoadOldUser::QueryInstChangeDcfServ(int nLatnId)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::QueryInstChange begin,nProcNum[%d],m_nProcId[%d]",m_inputParam.nProcNum,m_nProcId);
    
	char buf[64] = {0};
	int nRet=0, nState=0, nNum=0, nInstSize=0,nInstSizeBefore=0;
	std::string strQuerySQL = "";
	set<long> setAcctId,setPrdGroup;
	long lnOfrInstId = 0;
	long lnPidId = self();//getpid()
	multimap<long,ocs::DetailInst>::iterator iter;
	int nPrdInstNum=0,nSubInstNum=0,nOfrInstNum=0;
	
	char szTime[24] = {0};
	PublicLib::GetTime(szTime,YYYYMM);
	int nBillingCycleID = atoi(szTime);

	vector<T_instChange> vecChangeInst;
	set<long> setBatchCrossAcct,setBatchAcct;//用于过滤同一批处理过的账户
	set<long>::iterator iterBacthAcct;

	char monGroup[50] = {0};
	
	if (m_cfgPara.nOpenKpi)
	{		
		DCKpiSender::instance()->GetFullGroup("TraceKpi", nLatnId, monGroup);
	}

	struct timeval tm;
	gettimeofday(&tm, NULL);
	unsigned long lnbegin = (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);
	unsigned long lnend = 0, lnUseTime = 0;
	
	try
    {
		T_instChange inst;
		//Interface_ofr_change_id, ofr_inst_id, date_format(Create_date,'%Y%m%d%H%i%s') 
		if(3 == m_inputParam.vecSQLFields.size())
		{
			inst.ofr_inst_change_id = atol(m_inputParam.vecSQLFields[0].c_str());
			inst.ofr_inst_id = atol(m_inputParam.vecSQLFields[1].c_str());
			inst.crt_date.FromString(m_inputParam.vecSQLFields[2].c_str(),"YYYYMMDDHHNNSS"); 
			inst.nLatnId = nLatnId;

			//任务对应表数据预占
			nRet = UpdateDealState(inst.ofr_inst_change_id, inst.nLatnId);
			if(nRet < 0)
			{
				//任务处理失败
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "process", "UpdateDealState failed,exit");
				return -1;
			}
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "task_params size[%d] error!", m_inputParam.vecSQLFields.size());
			return -1;
		}

		//vecChangeInst.push_back(inst);

		//string tmpBillingCycle = m_inputParam.vecSQLFields[2].substr(0, 6);
		//if(nBillingCycleID == atoi(tmpBillingCycle.c_str()))
		//{
		//	m_inputParam.nProType = PROC_TYPE_POST_NORAML;
		//}
		//else
		//{//因新装记录无法区分预后，目前先只走预付费逻辑
			m_inputParam.nProType = PROC_TYPE_PRE_NORMAL;
		//}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","deal New user:ofr_inst_change_id[%ld],ofr_inst_id[%ld]",inst.ofr_inst_change_id,inst.ofr_inst_id);
		
		m_pComboAdpt->releaseUserMap(true);
		nNum++;
		
		nRet = m_pComboAdpt->GetOfrInstanceInfo(inst.ofr_inst_id,inst.nLatnId,inst.ofr_id);	
		if(nRet<0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::QueryInstChange",	"QueryInstOtherInfo failed by prdInstId[%ld],nLatnId[%d]",inst.ofr_detail_inst_ref_id,inst.nLatnId);	

			if(ReConnect()) //重连失败
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::QueryInstChange",	"ReConnect failed,exit");	
				return -1;
			}
			else  //重连成功,需要获取套餐实例
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"DCLoadOldUser::QueryInstChange", "ReConnect success,load ofr_inst_change_id[%ld] once again",inst.ofr_inst_change_id);
				nRet = m_pComboAdpt->GetOfrInstanceInfo(inst.ofr_inst_id,inst.nLatnId,inst.ofr_id);
				if(nRet<0)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::QueryInstChange",	"QueryInstOtherInfo failed by prdInstId[%ld],nLatnId[%d]",inst.ofr_detail_inst_ref_id,inst.nLatnId);	
					return -1;
				}
			}

		}

		nState = 1;
		if(nRet==0)//==1标识套餐实例失效或未获取到资料
		{
			setAcctId.clear();
			
			//判断是否为大账户，跨账户，是则存入特殊分流账户表TB_CROSS_ACCOUNT_[LATN_ID]
			long lnSpecialId=0;
			int nAcctType = m_pComboAdpt->CheckSpecialCrossAcct(nLatnId, inst.ofr_inst_id,true, lnSpecialId,setAcctId);				
			if(nAcctType <0)
			{
				if(nAcctType==-2)//套餐实例没有找到归属账户
				{
					nRet = UpdateInstDealed(inst.ofr_inst_change_id, 2, nLatnId);
					if (nRet < 0)
					{
						if (ReConnect()) //重连失败
						{
							DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::QueryInstChange", "ReConnect failed,exit");
							return -1;
						}
						else
						{
							DCBIZLOG(DCLOG_LEVEL_INFO, 0, "DCLoadOldUser::QueryInstChange", "ReConnect success,UpdateInstDealed ofr_inst_change_id[%ld] 2 once again", inst.ofr_inst_change_id);
							if (UpdateInstDealed(inst.ofr_inst_change_id, 2, nLatnId) < 0)
							{
								DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "DCLoadOldUser::QueryInstChange", "ReConnect success,UpdateInstDealed ofr_inst_change_id[%ld] 2 failed once again", inst.ofr_inst_change_id);
								return -1;
							}
						}
					}
					//continue;
				}
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldUser::QueryInstChange","CheckSpecialCrossAcct failed,OfrInstId[%ld].",inst.ofr_inst_id);
				return -1;
			}
			if(PROC_TYPE_PRE_NORMAL != m_inputParam.nProType)
			{
				if(nAcctType == SPECIAL_ACCT_COMM)
				{
					iterBacthAcct = setBatchAcct.find(lnSpecialId);
				}
				else
				{
					iterBacthAcct = setBatchAcct.end();//过滤同一批里处理过的
				}
				if(nAcctType != SPECIAL_ACCT_COMM || iterBacthAcct!=setBatchAcct.end())//大账户或跨账户的套餐实例变更不走新装流程，并更新记录状态为结束
				{
					nRet = UpdateInstDealed(inst.ofr_inst_change_id,nState,nLatnId);
					if (nRet < 0)
					{
						if(ReConnect()) //重连失败
						{
							DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::QueryInstChange",	"ReConnect failed,exit");	
							return -1;
						}
						else
						{
							DCBIZLOG(DCLOG_LEVEL_INFO, 0, "DCLoadOldUser::QueryInstChange", "ReConnect success,UpdateInstDealed ofr_inst_change_id[%ld] once again", inst.ofr_inst_change_id);
							if(UpdateInstDealed(inst.ofr_inst_change_id,nState,nLatnId)<0)
							{
								DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "DCLoadOldUser::QueryInstChange", "ReConnect success,UpdateInstDealed ofr_inst_change_id[%ld] failed once again", inst.ofr_inst_change_id);
								return -1;
							}
						}	
					}					
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","nAcctType[%d] is not normal acct",nAcctType);
					//continue;
				}
			}
			setBatchAcct.insert(lnSpecialId);
						
			m_pComboAdpt->releaseUserMap(true);
			
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","New User,ofr_inst_change_id[%ld],setAcctId is[%d]",inst.ofr_inst_change_id,setAcctId.size());
			if(!setAcctId.empty())
			{
				if(false==m_pComboAdpt->LoadUserByAcctList(setAcctId,nLatnId,lnSpecialId,nAcctType,setPrdGroup))
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldUser::QueryInstChange","LoadUserByAcctList failed,AcctId/CrossModGroupId[%ld] AcctType[%d].",lnSpecialId,nAcctType);
					return -1;
				}
				/*
				if(PROC_TYPE_PRE_NORMAL == m_inputParam.nProType)
				{                    
					nInstSizeBefore = m_pComboAdpt->m_vecOfrInstOutput.size();
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","inst change,nInstSizeBefore is[%d]",nInstSizeBefore);

					for ( std::vector<ocs::OfrInst>::iterator tIter = m_pComboAdpt->m_vecOfrInstOutput.begin(); tIter != m_pComboAdpt->m_vecOfrInstOutput.end();)
					{
						if(inst.ofr_id != tIter->lnOfrId)
						{
							DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","lnOfrId[%ld] is not order ofr,need erase",tIter->lnOfrId);
							tIter = m_pComboAdpt->m_vecOfrInstOutput.erase(tIter);			
						}
						else
						{
							tIter++ ;
						}
					}	
				}*/
				nInstSize = m_pComboAdpt->m_vecOfrInstOutput.size();
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","inst change,ofr_inst_change_id[%ld],nInstSize is[%d],detail below:",inst.ofr_inst_change_id,nInstSize);
				for ( std::vector<ocs::OfrInst>::iterator tIter = m_pComboAdpt->m_vecOfrInstOutput.begin(); tIter != m_pComboAdpt->m_vecOfrInstOutput.end(); ++tIter)
				{		
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","lnOfrId[%ld],lnOfrInstId[%ld],nCalcPriority[%d],szEffDate[%s],szExpDate[%s],szEventEffDate[%s],szEventExpDate[%s],nCalcPriority[%d],nDynInstFlag[%d]",tIter->lnOfrId,tIter->lnOfrInstId,tIter->nCalcPriority,tIter->szEffDate.c_str(),tIter->szExpDate.c_str(),tIter->szEventEffDate.c_str(),tIter->szEventExpDate.c_str(),tIter->nCalcPriority,tIter->nDynInstFlag);
				}

				T_UserInfo tUserInfo;					
				tUserInfo.nObversionMode = 0;
				tUserInfo.nUserMode = USER_NEW;
				char szTriggerCycle[9] = {0};
				strncpy(szTriggerCycle,inst.crt_date.ToString(string("YYYYMMDDHHNNSS")).c_str(),8);
				szTriggerCycle[8]='\0';
				tUserInfo.nTriggerCycle = atoi(szTriggerCycle);
				
				//组装消息发送
				m_pComboAdpt->SetHeadInfo(inst.nLatnId,nBillingCycleID,lnSpecialId,nAcctType,tUserInfo);
				//int nGroupNum = nInstSize%m_nInstSendBatNum==0?(int)(nInstSize/m_nInstSendBatNum):(int)(nInstSize/m_nInstSendBatNum)+1;
				//m_pComboAdpt->m_stAcctHead.GroupNum = nGroupNum;//新增接口传递一组消息个数
				int nGroupNum = 1,nPriority=0,nLastPriority=0,nFeeType=0;
				for(int i=0;i<nInstSize;)
				{
					ocs::IAcctBodyReq stAcctBodyReq;
					int j=0;
					nPrdInstNum=0;nSubInstNum=0;nOfrInstNum=0;
					
					m_pComboAdpt->m_stAcctHead.crossserv.clear();//避免重复发送
					m_pComboAdpt->m_stAcctHead.serv_id.clear();

					if(i==0)
					{
						//现每个用户都要做过户操作，所以从servid取，crossserv暂时保存
						for(int iprd=0;iprd<m_pComboAdpt->m_vecGroupPrdInstId.size();iprd++)
						{
							m_pComboAdpt->m_stAcctHead.crossserv.push_back(m_pComboAdpt->m_vecGroupPrdInstId[iprd]);
							m_pComboAdpt->m_stAcctHead.serv_id.push_back(m_pComboAdpt->m_vecGroupPrdInstId[iprd]);
						}
					}
					
					for(j=0;i<nInstSize;j++)
					{
						nPriority = m_pComboAdpt->m_vecOfrInstOutput[i].nCalcPriority;
						//m_cfgPara.nInstSendBatNum = 0不做限制 ，但是压缩文件大于1M需要写入DCA
						if(m_cfgPara.nInstSendBatNum > 0 && j>=m_cfgPara.nInstSendBatNum && (nLastPriority!=nPriority || nFeeType==FEE_TYPE_RENT || nFeeType==FEE_TYPE_PRODUCT))
						{
							break;
						}
						nLastPriority = nPriority;
						nFeeType = m_pComboAdpt->m_vecOfrInstOutput[i].nFeeType;
						
						lnOfrInstId = m_pComboAdpt->m_vecOfrInstOutput[i].lnOfrInstId;
						stAcctBodyReq.VOfrInst.push_back(m_pComboAdpt->m_vecOfrInstOutput[i]);
						iter=m_pComboAdpt->m_mmapOfrDetail.lower_bound(lnOfrInstId);
						for(;iter!=m_pComboAdpt->m_mmapOfrDetail.upper_bound(lnOfrInstId);iter++)
						{
							stAcctBodyReq.VDetailInst.push_back(iter->second);
						}
						switch(m_pComboAdpt->m_vecOfrInstOutput[i].nFeeType)
						{
						case 2:
							nPrdInstNum++;
							break;
						case 3:
							nSubInstNum++;
							break;
						default:
							nOfrInstNum++;
							break;
						}
						i++;
					}
					if(i==nInstSize)
					{
						m_pComboAdpt->m_stAcctHead.BillingFlag = 0;//标识一组消息中最后一条
						//最后一条消息也做过户
						if(m_pComboAdpt->m_stAcctHead.serv_id.size()==0)
						{
							for(int iprd=0;iprd<m_pComboAdpt->m_vecGroupPrdInstId.size();iprd++)
							{
								m_pComboAdpt->m_stAcctHead.serv_id.push_back(m_pComboAdpt->m_vecGroupPrdInstId[iprd]);
							}
						}
					}
					else
					{
						m_pComboAdpt->m_stAcctHead.BillingFlag = 1;
					}
					m_pComboAdpt->m_stAcctHead.GroupNum = nGroupNum++;//新增接口传递一组消息个数
					
					RentUserInfo stRentInfo;
					stRentInfo.lnSpecialId = lnSpecialId;
					stRentInfo.nAcctType = nAcctType;
					stRentInfo.nLatnId = nLatnId;
					stRentInfo.nBillingCycleId = m_nBillingCycleId;
					stRentInfo.nPrdInstNum = nPrdInstNum;
					stRentInfo.nSubInstNum = nSubInstNum;
					stRentInfo.nOfrInstNum = nOfrInstNum;
					stRentInfo.lnPidId = lnPidId;
					stRentInfo.stFmtHead = m_pComboAdpt->m_stFmtHead;
					stRentInfo.stAcctHead = m_pComboAdpt->m_stAcctHead;
					stRentInfo.stAcctBodyReq = stAcctBodyReq;
					stRentInfo.nGroupNum = nGroupNum;
					stRentInfo.nMsgType = MSGTYPE_NEW;
					stRentInfo.vecTransferId.push_back(inst.ofr_inst_change_id);

					nRet = m_pCallServer->process(&stRentInfo,MSGCTRL_MODE_NORMAL,p_dbm);
					if(nRet < 0)
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::QueryInstChange","call DCCallZKServerMaker::process fail,ofr_inst_change_id[%ld]",inst.ofr_inst_change_id);
						nState = 2;
						break;
					} 
					//仅全量处理流程写日志
					//WriteRentLog(nLatnId,lnPidId,nPrdInstNum,nSubInstNum,nOfrInstNum);
				}
			}
		}
		else
		{
			nState = 2;
		}

		//更新新装接口表状态
		nRet = UpdateInstDealed(inst.ofr_inst_change_id,nState,nLatnId);
		if (nRet < 0)
		{
			if(ReConnect()) //重连失败
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::QueryInstChange",	"ReConnect failed,exit");	
				return -1;
			}else
			{
				DCBIZLOG(DCLOG_LEVEL_INFO, 0, "DCLoadOldUser::QueryInstChange", "ReConnect success,UpdateInstDealed ofr_inst_change_id[%ld] once again", inst.ofr_inst_change_id);
				if(UpdateInstDealed(inst.ofr_inst_change_id,nState,nLatnId)<0)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "DCLoadOldUser::QueryInstChange", "ReConnect success,UpdateInstDealed ofr_inst_change_id[%ld] failed once again", inst.ofr_inst_change_id);
					return -1;
				}
			}		
		}

		gettimeofday(&tm, NULL);//性能日志
		lnend = (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);//性能日志
		lnUseTime = DIFFTIME_US(lnend, lnbegin);
		
		if (m_cfgPara.nOpenKpi)
		{			
			DCKpiSender::instance()->cycle_array_inc(m_ptrBPMon, monGroup, 2, "k1", NULL, nNum);
			DCKpiSender::instance()->cycle_array_inc(m_ptrBPMon, monGroup, 2, "k2", NULL, lnUseTime);
			DCKpiSender::instance()->cycle_array_inc(m_ptrBPMon, monGroup, 2, "k3", NULL, 0);
		}
	
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCLoadOldUser::QueryInstChange","QueryInstChange by %s find [%d] records!",strQuerySQL.c_str(),nNum);
    	if(nNum>0)
    	{
    		RentUserInfo stRentInfo;
			stRentInfo.stAcctHead.user_mode = USER_NEW;
			nRet = m_pCallServer->process(&stRentInfo,MSGCTRL_MODE_RESEND,p_dbm);
			if(nRet < 0)
	        {
	            DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::QueryInstChange","call DCCallZKServerMaker::process fail");
	        } 
    	}
        m_pComboAdpt->releaseUserMap(true);
	}	
	catch(UDBException &e)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR,-e.GetErrorCode(),"DCLoadOldUser::QueryInstChange","error info[%s] sql[%s]",e.ToString(),strQuerySQL.c_str());
        return -e.GetErrorCode();
    }
	
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::QueryInstChange end!");
    return 0;
}


//更新新装表（销售品变更接口表）处理状态
int DCLoadOldUser::UpdateInstDealed(long lnOfrInstChangeID, int nState,int nLatnId)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::UpdateInstDealed begin,lnOfrInstChangeID[%ld],nState[%d]",lnOfrInstChangeID,nState);
	int nRowCount = 0;
	string strQuerySQL;
	try
	{
    	char szSqlName[64]={0};		
		sprintf(szSqlName,"UpdateOfrInstChange|%d",nLatnId);
		UDBSQL* pQuery1 = p_dbm->GetSQL(szSqlName);		
		if (NULL == pQuery1)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,0,"","get query[UpdateOfrInstChange] failed.");
			return -1;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","Get sql UpdateOfrInstChange OK");
		
		pQuery1->UnBindParam();
		pQuery1->BindParam(1,nState);
		pQuery1->BindParam(2,lnOfrInstChangeID);
		pQuery1->GetSqlString(strQuerySQL);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldUser::UpdateInstDealed","Do SQL:Begin to do,sql :[%s]",strQuerySQL.c_str());
		pQuery1->Execute();
		nRowCount = pQuery1->GetRowCount();
		pQuery1->Connection()->Commit();
		pQuery1->Close();
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCLoadOldUser::UpdateInstDealed","Begin to update table tb_prd_ofr_inst_change :inst_change_id:%ld",lnOfrInstChangeID);
	}
	catch(UDBException &e)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR,-e.GetErrorCode(),"DCLoadOldUser::UpdateInstDealed","error info[%s] sql[%s]",e.ToString(),strQuerySQL.c_str());
        return -e.GetErrorCode();
    }
	
	if((nState == 1 || nState == 2) && nRowCount > 0)
	{
		string strDeleteSQL;
		try
		{
			char szSqlName[64]={0};
			sprintf(szSqlName,"DeleteOfrInstChange|%d",nLatnId);
			UDBSQL* pDelete = p_dbm->GetSQL(szSqlName);
			if (NULL == pDelete)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR,0,"","get query[DeleteOfrInstChange] failed.");
				return -1;
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","Get sql DeleteOfrInstChange OK");
			pDelete->UnBindParam();
			pDelete->BindParam(1,lnOfrInstChangeID);
			pDelete->GetSqlString(strDeleteSQL);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldUser::UpdateInstDealed","Do SQL:Begin to do,sql :[%s]",strDeleteSQL.c_str());
			pDelete->Execute();
			pDelete->Connection()->Commit();
			pDelete->Close();
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCLoadOldUser::UpdateInstDealed","Begin to delete table tb_prd_ofr_inst_change :inst_change_id:%ld",lnOfrInstChangeID);
		}
		catch(UDBException &e)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-e.GetErrorCode(),"DCLoadOldUser::UpdateInstDealed","error info[%s] sql[%s]",e.ToString(),strDeleteSQL.c_str());
			return -e.GetErrorCode();
		}
	}
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::UpdateInstDealed end,lnOfrInstChangeID[%ld],nState[%d]",lnOfrInstChangeID,nState);
	return 0;
}

//更新复机表处理状态
int DCLoadOldUser::UpdateFrozenDealed(long lnFrozenID, int nState,int nLatnId)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::UpdateFrozenDealed begin,lnFrozenID[%ld],nState[%d]",lnFrozenID,nState);
	string strQuerySQL;
	try
	{
    	char szSqlName[64]={0};
		sprintf(szSqlName,"UpdateFrozenUser");				
		UDBSQL* pQuery1 = p_dbm->GetSQL(szSqlName);		
		if (NULL == pQuery1)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,0,"","get query[UpdateFrozenUser] failed.");
			return -1;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","Get sql UpdateFrozenUser OK");
		pQuery1->UnBindParam();
		pQuery1->BindParam(1,nState);
		pQuery1->BindParam(2,lnFrozenID);
		pQuery1->BindParam(3,nLatnId);
		pQuery1->GetSqlString(strQuerySQL);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","Do SQL:Begin to do,sql :[%s]",strQuerySQL.c_str());
		pQuery1->Execute();
		pQuery1->Connection()->Commit();
		pQuery1->Close();
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Begin to update table tb_prd_frozen_user,Frozen_id:%ld",lnFrozenID);
	}
	catch(UDBException &e)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR,-e.GetErrorCode(),"","error info[%s] sql[%s]",e.ToString(),strQuerySQL.c_str());
        return -e.GetErrorCode();
    }	
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::UpdateFrozenDealed end,lnFrozenID[%ld],nState[%d]",lnFrozenID,nState);
	return 0;
}



//查询账户变更表，触发用户过户操作,新装流程调用 //acctbolt不依赖此表做过户操作，但rentload需要更新此表处理状态
int DCLoadOldUser::DealTransferPrd(int nLatnId)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::DealTransferPrd begin");
    
	long lnCurAcctId=0,lnLastAcctId=-99,lnPrdInstId=0,lnTransferId=0;
	set<long> setPrdGroup;
	vector<long> vecTransferId,vecInvaildTransfer,vecSendTransferId;
	vecTransferId.clear();
	vecInvaildTransfer.clear();
	vecSendTransferId.clear();

	char szTime[24] = {0};
	PublicLib::GetTime(szTime,YYYYMM);
	int nBillingCycleID = atoi(szTime);

	try
    {
    	if(m_bDcfServMode)
    	{
			if( m_pComboAdpt->LoadTransferPrdAcctDcfServ(nLatnId,m_inputParam.vecSQLFields,vecInvaildTransfer)<0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldUser::DealTransferPrd","LoadTransferPrdAcct failed, LatnId[%d].",nLatnId);
				return -1;
			}
    	}
		else
		{
			if( m_pComboAdpt->LoadTransferPrdAcct(nLatnId,vecInvaildTransfer)<0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldUser::DealTransferPrd","LoadTransferPrdAcct failed, LatnId[%d].",nLatnId);
				return -1;
			}
		}
		//先将无效的置为异常
		for(int idx=0;idx<vecInvaildTransfer.size();idx++)
		{
			if(UpdateTransferState(vecInvaildTransfer[idx],2,nLatnId)<0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldUser::DealTransferPrd","UpdateTransferState failed,TransferId[%ld] LatnId[%d].",vecInvaildTransfer[idx],nLatnId);
				//只报错不退出
			}
		}
		
		m_pComboAdpt->releaseUserMap(true);

		multimap<long,STTransferAcctPrd>::iterator iterprdbeg = m_pComboAdpt->m_multimapTransferAcct.begin();
		multimap<long,STTransferAcctPrd>::iterator iterprdend = m_pComboAdpt->m_multimapTransferAcct.end();
		for(;iterprdbeg!=iterprdend;iterprdbeg++)
		{
			lnCurAcctId = iterprdbeg->first;
			lnTransferId = iterprdbeg->second.lnTransferId;
			if(m_setLastSendTransferPrdRec.find(lnTransferId)!=m_setLastSendTransferPrdRec.end())
			{
			    continue;
			}
			if(-99 == lnLastAcctId)//第一条数据的时候
			{
				lnLastAcctId = iterprdbeg->first;
			}
			if(lnCurAcctId!=lnLastAcctId)
			{				
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","transfer change acct,lnLastAcctId[%ld],lnCurAcctId[%ld]",lnLastAcctId,lnCurAcctId);
				AssemblyMessage(nLatnId,nBillingCycleID,lnLastAcctId,setPrdGroup,vecTransferId,MSGTYPE_TRANSFER);
				setPrdGroup.clear();
				vecTransferId.clear();
				m_pComboAdpt->releaseUserMap(true);
				lnLastAcctId = lnCurAcctId;	//切换账号
			}

			lnPrdInstId = iterprdbeg->second.lnPrdInstId;
			setPrdGroup.insert(lnPrdInstId);
			vecTransferId.push_back(lnTransferId);
			vecSendTransferId.push_back(lnTransferId);
        }

		if(!setPrdGroup.empty())//最后一个
		{			
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","last transfer acct,lnCurAcctId[%ld]",lnCurAcctId);
			AssemblyMessage(nLatnId,nBillingCycleID,lnCurAcctId,setPrdGroup,vecTransferId,MSGTYPE_TRANSFER);
			setPrdGroup.clear();
            vecTransferId.clear();
			m_pComboAdpt->releaseUserMap(true);
		}

		//保存新的上次发送过的transferid
		m_setLastSendTransferPrdRec.clear();
		for(int idx=0;idx<vecSendTransferId.size();idx++)
		{
			m_setLastSendTransferPrdRec.insert(vecSendTransferId[idx]);
		}
		
	}	
	catch(std::exception& e)
	{		
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldUser::DealTransferPrd","DealTransferPrd exception: [%s]",e.what());
	}
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::DealTransferPrd end!");
    return 0;
}


//更新账户变更表处理状态
int DCLoadOldUser::UpdateTransferState(long lnTransferID, int nState,int nLatnId)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::UpdateTransferState begin,lnTransferID[%ld],nState[%d]",lnTransferID,nState);
	string strQuerySQL;
	try
	{
    	char szSqlName[64]={0};
		sprintf(szSqlName,"UpdateTransferPrdAcct|%d",nLatnId);
		UDBSQL* pQuery1 = p_dbm->GetSQL(szSqlName);
        if (pQuery1 == NULL)
        {
            DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "get query[UpdateTransferPrdAcct] failed.");
            return -1;
        }
		pQuery1->UnBindParam();
		pQuery1->BindParam(1,nState);
		pQuery1->BindParam(2,lnTransferID);
		pQuery1->GetSqlString(strQuerySQL);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldUser::UpdateTransferState","Do SQL:Begin to do,sql :[%s]",strQuerySQL.c_str());
		pQuery1->Execute();
		pQuery1->Connection()->Commit();
		pQuery1->Close();
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCLoadOldUser::UpdateTransferState","Execute [UpdateTransferPrdAcct] success :transfer_id:%ld",lnTransferID);
	}
	catch(UDBException &e)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR,-e.GetErrorCode(),"DCLoadOldUser::UpdateTransferState","error info[%s] sql[%s]",e.ToString(),strQuerySQL.c_str());
        return -e.GetErrorCode();
    }	
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::UpdateTransferState end,lnTransferID[%ld],nState[%d]",lnTransferID,nState);
	return 0;
}


//提取用户资料并组装消息，用于新装流程
//nMsgType =0老用户 =1套餐变更 =2用户过户 =3费用缓存触发需要提用户套餐 =4费用缓存触发
int DCLoadOldUser::AssemblyMessage(int nLatnId,int nBillingCycleID,long lnAcctId,set<long>& setPrdGroup,vector<long>& vecTransferId,int nMsgType)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::AssemblyMessage begin,lnAcctId[%ld],nBillingCycleID[%d]",lnAcctId,nBillingCycleID);
	long lnSpecialId=0,lnTemp=0;
	int nAcctType=0,nInstSize=0;
	set<long> setAcctId;
	setAcctId.insert(lnAcctId);
	long lnPidId = getpid();
	std::vector<ocs::OfrInst> vecOfrInstOutput;
	
	//判断账户类型
	multimap<long,ocs::DetailInst>::iterator iter;
	map<long,STSpecialAcctInfo>::iterator itermap = m_pComboAdpt->m_mapSpecialAcctType.find(lnAcctId);
	if(itermap!=m_pComboAdpt->m_mapSpecialAcctType.end())
	{
		if(itermap->second.nGroupId == 2)
		{
			lnSpecialId = itermap->second.lnModGroupId;
			nAcctType = SPECIAL_ACCT_INST;
		}
		else if(itermap->second.nGroupId == 3)
		{
			lnSpecialId = lnAcctId;
			nAcctType = SPECIAL_ACCT_MOST;
		}
		else
		{
			lnSpecialId = lnAcctId;
			nAcctType = SPECIAL_ACCT_BIG;
		}
	}
	else
	{
		lnSpecialId = lnAcctId;
		nAcctType = SPECIAL_ACCT_COMM;
	}

	if(nMsgType<4)//费用缓存触发的用户为空，不用找套餐
	{
		if(false==m_pComboAdpt->LoadUserByAcctList(setAcctId,nLatnId,lnSpecialId,nAcctType,setPrdGroup))
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldUser::AssemblyMessage","LoadUserByAcctList failed,AcctId/CrossModGroupId[%ld].",lnSpecialId);
			return -1;
		}
		vecOfrInstOutput.clear();
		for(int idx=0;idx<m_pComboAdpt->m_vecOfrInstOutput.size();idx++)
		{
			if(m_pComboAdpt->m_vecOfrInstOutput[idx].nFeeType==FEE_TYPE_RENT || m_pComboAdpt->m_vecOfrInstOutput[idx].nFeeType==FEE_TYPE_PRODUCT)
			{
				continue;//实时流程不带主附产品套餐
			}
			vecOfrInstOutput.push_back(m_pComboAdpt->m_vecOfrInstOutput[idx]);
		}
		nInstSize = vecOfrInstOutput.size();
	}

	T_UserInfo tUserInfo;	
	tUserInfo.nObversionMode = 0;
	tUserInfo.nUserMode = USER_NEW;	
	tUserInfo.nTriggerCycle = -1;
					
	//组装消息发送
	m_pComboAdpt->SetHeadInfo(nLatnId,nBillingCycleID,lnAcctId,nAcctType,tUserInfo);//lnSpecialId改为lnAcctId,因为msgsrc=1

	m_pComboAdpt->m_stAcctHead.GroupNum = 0;//新增接口传递一组消息个数
	m_pComboAdpt->m_stAcctHead.messagesource = 1;//过户和费用缓存触发走实时租费流程
	m_pComboAdpt->m_stAcctHead.BillingFlag = 0;

	set<long>::iterator iterset = setPrdGroup.begin();
	for(;iterset!=setPrdGroup.end();iterset++)
	{
		lnTemp = *iterset;
		m_pComboAdpt->m_stAcctHead.serv_id.push_back(lnTemp);
		m_pComboAdpt->m_stAcctHead.crossserv.push_back(lnTemp);
	}

	ocs::IAcctBodyReq stAcctBodyReq;	
	for(int idx=0;idx<nInstSize;idx++)
	{
		lnTemp = vecOfrInstOutput[idx].lnOfrInstId;
		stAcctBodyReq.VOfrInst.push_back(vecOfrInstOutput[idx]);
		
		iter=m_pComboAdpt->m_mmapOfrDetail.lower_bound(lnTemp);
		for(;iter!=m_pComboAdpt->m_mmapOfrDetail.upper_bound(lnTemp);iter++)
		{
			stAcctBodyReq.VDetailInst.push_back(iter->second);
		}	
	}
		
	RentUserInfo stRentInfo;
	stRentInfo.lnSpecialId = lnSpecialId;
	stRentInfo.nAcctType = nAcctType;
	stRentInfo.nLatnId = nLatnId;
	stRentInfo.nBillingCycleId = m_nBillingCycleId;
	stRentInfo.nPrdInstNum = 0;
	stRentInfo.nSubInstNum = 0;
	stRentInfo.nOfrInstNum = 0;
	stRentInfo.lnPidId = lnPidId;
	stRentInfo.stFmtHead = m_pComboAdpt->m_stFmtHead;
	stRentInfo.stAcctHead = m_pComboAdpt->m_stAcctHead;
	stRentInfo.stAcctBodyReq = stAcctBodyReq;
	stRentInfo.nGroupNum = 0;
	stRentInfo.nMsgType = nMsgType;
	stRentInfo.vecTransferId=vecTransferId;//用于返回后处理接口表

	int nRet = m_pCallServer->process(&stRentInfo,MSGCTRL_MODE_NORMAL,p_dbm);
	if(nRet < 0)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::AssemblyMessage","call DCCallZKServerMaker::process fail");
		//return -1;
    } 
	//仅全量处理流程写日志
	//WriteRentLog(nLatnId,lnPidId,nPrdInstNum,nSubInstNum,nOfrInstNum);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::AssemblyMessage end,lnAcctId[%ld],nBillingCycleID[%d]",lnAcctId,nBillingCycleID);

	return 0;
	
}


//提取老用户（跨账户）资料
int DCLoadOldUser::loadOldCrossUser(DCDateTime &cur_time,int nLatnId,bool isTrial)
{	
	
	map<long,vector<long> > mapCross;//缓存所有的跨账户信息
	mapCross.clear();
	vector<long> vecCross;
	vecCross.clear();
	map<long,vector<long> >::iterator mCrsiter;
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "DCLoadOldUser::loadOldCrossUser begin,CurrDealTime[%s],LatnId[%d],nProType[%d]",cur_time.ToString(string("YYYYMMDDHHNNSS")).c_str(),nLatnId,m_inputParam.nProType);
	long lnAcctCounts=0,lnSendMsgCount=0;

	if (PROC_TYPE_PRE_NORMAL == m_inputParam.nProType)
	{
		return 0;
	}

	struct timeval tm;
	gettimeofday(&tm, NULL);
	unsigned long lnbegin = (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);
	unsigned long lnbeginTmp = lnbegin;
	unsigned long lnend = 0, lnUseTime = 0;
	int nTps1=0,nTps3=0;
	
	long AcctId=-99;
	long ModGroupId=-99;
	long LastModGroupId=-99;
	set<long> setPrdGroup;
	set<long> setAcctList;
	m_vecCurAcctList.clear();

	char CrsSqlname[64]={0};
	string str;
	int nRet = 0;
		
	char monGroup[50] = {0};
	if (m_cfgPara.nOpenKpi)
	{		
		DCKpiSender::instance()->GetFullGroup("TraceKpi", nLatnId, monGroup);
	}
	
	try
	{
		sprintf(CrsSqlname,"LoadOldCrossAcct|%d",nLatnId);
		UDBSQL *CrsQuery = p_dbm->GetSQL(CrsSqlname);
		if(NULL==CrsQuery)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::LoadOldCrossAcct","GetSQL [%s] failed", CrsSqlname);
			return NO_FIND_SQL;

		}
		CrsQuery->UnBindParam();
		CrsQuery->BindParam(1,(long)m_inputParam.nProcNum);
		CrsQuery->BindParam(2,(long)m_nProcId); 
		CrsQuery->GetSqlString(str);		
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldUser::LoadOldCrossAcct","Do SQL: begin to do LoadOldCrossAcct,sql :[%s] ",str.c_str());
		CrsQuery->Execute();
		while(CrsQuery->Next())//把所有的跨账户信息保存下来
		{
			CrsQuery->GetValue(1,AcctId);
			CrsQuery->GetValue(2,ModGroupId);
			DCBIZLOG(DCLOG_LEVEL_TRACE, 0,"DCLoadOldUser::LoadOldCrossAcct","Get CrossAcct AcctId[%ld] ModGroupId[%ld]",AcctId,ModGroupId);

			if(LastModGroupId == -99)
			{
				LastModGroupId = ModGroupId;
			}
			if(LastModGroupId != ModGroupId)
			{
				mapCross.insert(map<long,vector<long> >::value_type(LastModGroupId,vecCross));
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadOldUser::LoadOldCrossAcct","Get CrossAcct size[%d], ModGroupId[%ld]", vecCross.size(), LastModGroupId);
				vecCross.clear();
				vecCross.push_back(AcctId);
				LastModGroupId=ModGroupId;
				continue;
			}
			vecCross.push_back(AcctId);
			LastModGroupId=ModGroupId;
		}
		CrsQuery->Close();
		if(LastModGroupId != -99)
		{
			mapCross.insert(map<long,vector<long> >::value_type(LastModGroupId,vecCross));
		}
		int idx=0;
		for(mCrsiter=mapCross.begin();mCrsiter!=mapCross.end();mCrsiter++)
		{
			m_vecCurAcctList.clear();
			setAcctList.clear();
			
			ModGroupId = mCrsiter->first;
			m_vecCurAcctList = mCrsiter->second;
			for(idx=0;idx<m_vecCurAcctList.size();idx++)
			{
			    lnAcctCounts++;//性能统计账户数
				setAcctList.insert(m_vecCurAcctList[idx]);
			}

			nRet = m_pComboAdpt->LoadUserByAcctList(setAcctList,nLatnId,ModGroupId,SPECIAL_ACCT_INST,setPrdGroup);
			if(nRet < 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldUser::loadOldCrossUser","LoadUserByAcctList failed,CrossModGroupId[%ld].",ModGroupId);
				m_pComboAdpt->releaseUserMap(true);
				if( nRet <= NO_FIND_SQL)
					return nRet;
				continue;
			}
			
			DCBIZLOG(DCLOG_LEVEL_INFO,0,"DCLoadOldUser::loadOldCrossUser","Send CrossModGroupId Message, CurCrossOfrInstId[%ld].",ModGroupId);
			// 查找套餐实例信息和明细,组装消息并发送
			AcctId = m_vecCurAcctList[idx];
			nRet = CrossOfrInstChangeDeal(ModGroupId, nLatnId, AcctId,lnSendMsgCount, isTrial);
			if( nRet < 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"DCLoadOldUser::loadOldCrossUser","CrossOfrInstChangeDeal failed,CrossModGroupId[%ld].",ModGroupId);
				m_pComboAdpt->releaseUserMap(true);
				if( nRet <= NO_FIND_SQL)
					return nRet;
			}
		
			DCDATLOG(); //主动触发日志输出
			if (lnSendMsgCount % 10 == 0) // 每处理10个跨账户组，执行一下
            {
                RentUserInfo stRentInfo;
                stRentInfo.stAcctHead.user_mode = USER_OLD;
                int nRet = m_pCallServer->process(&stRentInfo, MSGCTRL_MODE_RESEND,p_dbm);
                if (nRet < 0)
                {
                    DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::loadOldCrossUser", "call DCCallZKServerMaker::process fail");
                }
            }
            usleep(1200); // 降低cpu使用率
		
		}
	
		if(lnSendMsgCount>0)
		{
			RentUserInfo stRentInfo;
			nRet = m_pCallServer->process(&stRentInfo,MSGCTRL_MODE_RESEND, p_dbm);
			if(nRet < 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::loadOldCrossUser","call DCCallZKServerMaker::process fail");
			} 
            
            gettimeofday(&tm, NULL);//性能日志
            lnend = (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);//性能日志
            lnUseTime = DIFFTIME_US(lnend, lnbegin);
            if(lnUseTime>0 && lnAcctCounts>0)
                nTps1 =(int)((lnAcctCounts*1000000.0)/lnUseTime);
            if(lnUseTime>0 && lnSendMsgCount>0)
                nTps3 =(int)((lnSendMsgCount*1000000.0)/lnUseTime);
            // 输出统计信息
            std::string strStat;
            p_dbm->get_statistic()->to_string(strStat, true);    //过滤未使用的
            DCBIZLOG(DCLOG_LEVEL_INFO, 0,"TPS RentLoad CrossUser","LatnId[%d] LoadUseTime[%ldus] LoadAcctCounts[%ld][tps1=%d] TotalSendMsgCounts[%ld][tps3=%d]",nLatnId,lnUseTime,lnAcctCounts,nTps1,lnSendMsgCount,nTps3);
            DCDATLOG("RC00008:%d!%ld!%ld!%d!%ld!%d!%s",nLatnId,lnUseTime,lnAcctCounts,nTps1,lnSendMsgCount,nTps3, strStat.c_str());
            DCPERFLOG((int)lnUseTime,"LatnId[%d] LoadUseTime[%ldus] LoadAcctCounts[%ld][tps1=%d] TotalSendMsgCounts[%ld][tps3=%d] sqlperf:%s",nLatnId,lnUseTime,lnAcctCounts,nTps1,lnSendMsgCount,nTps3,strStat.c_str());
	    }
		m_pComboAdpt->releaseUserMap(true);//清空数据

	}		
	catch(UDBException &e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-e.GetErrorCode(),"DCLoadOldUser::loadOldCrossUser","error info[%s]",e.ToString());
		return -e.GetErrorCode();
	}

	gettimeofday(&tm, NULL);//性能日志
	lnend = (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);//性能日志
	lnUseTime = DIFFTIME_US(lnend, lnbegin);
	if(lnUseTime>0 && lnAcctCounts>0)
		nTps1 =(int)((lnAcctCounts*1000000.0)/lnUseTime);
	if(lnUseTime>0 && lnSendMsgCount>0)
		nTps3 =(int)((lnSendMsgCount*1000000.0)/lnUseTime);
	// 输出统计信息
	std::string strStat;
	p_dbm->get_statistic()->to_string(strStat, true);	 //过滤未使用的
	DCBIZLOG(DCLOG_LEVEL_INFO, 0,"PERF RentLoad CrossUser","LatnId[%d] LoadUseTime[%ldus] LoadAcctCounts[%ld][tps1=%d] TotalSendMsgCounts[%ld][tps3=%d] sqlperf:%s",nLatnId,lnUseTime,lnAcctCounts,nTps1,lnSendMsgCount,nTps3, strStat.c_str());
	DCDATLOG("RC00008:%d!%ld!%ld!%d!%ld!%d!%s",nLatnId,lnUseTime,lnAcctCounts,nTps1,lnSendMsgCount,nTps3, strStat.c_str());
    DCPERFLOG((int)lnUseTime,"LatnId[%d] LoadUseTime[%ldus] LoadAcctCounts[%ld][tps1=%d] TotalSendMsgCounts[%ld][tps3=%d] sqlperf:%s",nLatnId,lnUseTime,lnAcctCounts,nTps1,lnSendMsgCount,nTps3,strStat.c_str());

	if (m_cfgPara.nOpenKpi)
	{
		DCKpiSender::instance()->cycle_array_inc(m_ptrBPMon, monGroup, 2, "k1", NULL, lnAcctCounts);
		DCKpiSender::instance()->cycle_array_inc(m_ptrBPMon, monGroup, 2, "k2", NULL, lnUseTime);
		DCKpiSender::instance()->cycle_array_inc(m_ptrBPMon, monGroup, 2, "k3", NULL, 0);
	}
	
	//重置统计信息
	p_dbm->get_statistic()->reset();
	//DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCLoadOldUser::loadOldUser","DCLoadOldUser::loadOldUser end,LatnId[%d] LoadAcctCounts[%ld] LoadPrdCounts[%ld]",nLatnId,lnAcctCounts,lnCounts);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"", "DCLoadOldUser::loadOldCrossUser end,CurrDealTime[%s],LatnId[%d]",cur_time.ToString(string("YYYYMMDDHHNNSS")).c_str(),nLatnId);

	return 0;
}


//跨账户组ID变更，发送上一组账户的套餐实例消息
int DCLoadOldUser::CrossOfrInstChangeDeal(long lnModGroupId, int nLatnId, long lnLastAcctId,long &lnSendMsgCount,bool isTrial)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::CrossOfrInstChangeDeal begin,lnModGroupId[%ld],lnLastAcctId[%ld]",lnModGroupId,lnLastAcctId);

	long lnAcctId=lnModGroupId;
	long lnOfrInstId = 0;
	long lnPidId = self();//getpid()
	multimap<long,ocs::DetailInst>::iterator iter;
	int nPrdInstNum=0,nSubInstNum=0,nOfrInstNum=0;
	long lnTmp=0;
	vector<long> vecTransferId;

	/*if(m_cfgPara.nContralGetOfrInst==1)//获取套餐实例开关
	// 查找套餐实例信息和明细
	//if(m_setOfrInstId.size()>0)
	{
		if( m_pComboAdpt->getAllAcctOfrfInstance(lnModGroupId ,nLatnId,lnLastAcctId)<0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadOldUser::CrossOfrInstChangeDeal","getAllAcctOfrfInstance failed,CrossOfrInstId[%ld].",lnModGroupId);
			return -1;
		}
		//lnAcctId = lnLastAcctId;
	}*/
	//预付费流程需剔除掉互斥销售品
    if (PROC_TYPE_PRE_NORMAL == m_inputParam.nProType && MutexOfr() )
    {
	    DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"","MutexOfr() fault!,AcctId[%ld].",lnAcctId);
        return -1;
    }
	int nInstSize = m_pComboAdpt->m_vecOfrInstOutput.size();	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","cross ofr inst,nInstSize is[%d]",nInstSize);
	if(nInstSize>0)
	{
	    T_UserInfo tUserInfo;
		tUserInfo.nObversionMode = 0;
		if(isTrial)//区分试算用户和老用户
			tUserInfo.nUserMode = USER_TRIAL;
		else
			tUserInfo.nUserMode = USER_OLD;		
		tUserInfo.nTriggerCycle = DCDataCenter::instance()->iBillingCycleId;
		//组装头部信息
		m_pComboAdpt->SetHeadInfo(nLatnId,m_nBillingCycleId,lnAcctId,SPECIAL_ACCT_INST,tUserInfo);
		//int nGroupNum = nInstSize%m_nInstSendBatNum==0?(int)(nInstSize/m_nInstSendBatNum):(int)(nInstSize/m_nInstSendBatNum)+1;
		//m_pComboAdpt->m_stAcctHead.GroupNum = nGroupNum;//新增接口传递一组消息个数
		int nGroupNum = 1,nPriority=0,nLastPriority=0,nFeeType=0;			
		for(int i=0;i<nInstSize;)
		{
			ocs::IAcctBodyReq stAcctBodyReq;
			int j=0;
			nPrdInstNum=0;nSubInstNum=0;nOfrInstNum=0;

			m_pComboAdpt->m_stAcctHead.crossserv.clear();//避免重复发送
			m_pComboAdpt->m_stAcctHead.serv_id.clear();

			if(i==0)
			{
				//账户下新增用户列表，用于账户变更操作
				for(int iact=0;iact<m_vecCurAcctList.size();iact++)
				{
					lnTmp = m_vecCurAcctList[iact];
					multimap<long,STTransferAcctPrd>::iterator iterprdbeg = m_pComboAdpt->m_multimapTransferAcct.lower_bound(lnTmp);
					multimap<long,STTransferAcctPrd>::iterator iterprdend = m_pComboAdpt->m_multimapTransferAcct.upper_bound(lnTmp);
					for(;iterprdbeg!=iterprdend;iterprdbeg++)
					{
						lnTmp = iterprdbeg->second.lnPrdInstId;
						m_pComboAdpt->m_stAcctHead.crossserv.push_back(lnTmp);
						lnTmp = iterprdbeg->second.lnTransferId;
						vecTransferId.push_back(lnTmp);
					}
				}
				//现每个用户都要做过户操作，所以从servid取，crossserv暂时保存
				for(int iprd=0;iprd<m_pComboAdpt->m_vecGroupPrdInstId.size();iprd++)
				{
					m_pComboAdpt->m_stAcctHead.serv_id.push_back(m_pComboAdpt->m_vecGroupPrdInstId[iprd]);
				}
			}
			for(j=0;i<nInstSize;j++)
			{
				nPriority = m_pComboAdpt->m_vecOfrInstOutput[i].nCalcPriority;
				//m_cfgPara.nInstSendBatNum = 0不做限制 ，但是压缩文件大于1M需要写入DCA
				if(m_cfgPara.nInstSendBatNum > 0 && j>=m_cfgPara.nInstSendBatNum && (nLastPriority!=nPriority || nFeeType==FEE_TYPE_RENT || nFeeType==FEE_TYPE_PRODUCT))
					break;
				nLastPriority = nPriority;
				nFeeType = m_pComboAdpt->m_vecOfrInstOutput[i].nFeeType;
							
				lnOfrInstId = m_pComboAdpt->m_vecOfrInstOutput[i].lnOfrInstId;
				stAcctBodyReq.VOfrInst.push_back(m_pComboAdpt->m_vecOfrInstOutput[i]);
				iter=m_pComboAdpt->m_mmapOfrDetail.lower_bound(lnOfrInstId);
				for(;iter!=m_pComboAdpt->m_mmapOfrDetail.upper_bound(lnOfrInstId);iter++)
					stAcctBodyReq.VDetailInst.push_back(iter->second);
				switch(m_pComboAdpt->m_vecOfrInstOutput[i].nFeeType)
				{
				case 2:
					nPrdInstNum++;
					break;
				case 3:
					nSubInstNum++;
					break;
				default:
					nOfrInstNum++;
					break;
				}
				i++;
			}
			if(i==nInstSize)
			{
				m_pComboAdpt->m_stAcctHead.BillingFlag = 0;//标识一组消息中最后一条
				//最后一条消息也做过户
				if(m_pComboAdpt->m_stAcctHead.serv_id.size()==0)
				{
					for(int iprd=0;iprd<m_pComboAdpt->m_vecGroupPrdInstId.size();iprd++)
					{
						m_pComboAdpt->m_stAcctHead.serv_id.push_back(m_pComboAdpt->m_vecGroupPrdInstId[iprd]);
					}
				}
			}
			else
			{
				m_pComboAdpt->m_stAcctHead.BillingFlag = 1;
			}
			m_pComboAdpt->m_stAcctHead.GroupNum = nGroupNum++;//新增接口传递一组消息个数

			RentUserInfo stRentInfo;
			stRentInfo.lnSpecialId = lnModGroupId;
			stRentInfo.nAcctType = SPECIAL_ACCT_INST;
			stRentInfo.nLatnId = nLatnId;
			stRentInfo.nBillingCycleId = m_nBillingCycleId;
			stRentInfo.nPrdInstNum = nPrdInstNum;
			stRentInfo.nSubInstNum = nSubInstNum;
			stRentInfo.nOfrInstNum = nOfrInstNum;
			stRentInfo.lnPidId = lnPidId;
			stRentInfo.stFmtHead = m_pComboAdpt->m_stFmtHead;
			stRentInfo.stAcctHead = m_pComboAdpt->m_stAcctHead;
			stRentInfo.stAcctBodyReq = stAcctBodyReq;
			stRentInfo.nGroupNum = nGroupNum;
			stRentInfo.nMsgType = 0;
			stRentInfo.lnRouteAcctId = lnLastAcctId;
			if(vecTransferId.size()>0)
			{
				stRentInfo.vecTransferId = vecTransferId;
				vecTransferId.clear();
			}

			int nRet = m_pCallServer->process(&stRentInfo,MSGCTRL_MODE_NORMAL,p_dbm);
			if(nRet < 0)
	        {
	            DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadOldUser::CrossOfrInstChangeDeal","call DCCallZKServerMaker::process fail");
				m_pComboAdpt->releaseUserMap(true);//清空数据
	            //return -1;
	            break;
	        }
			lnSendMsgCount++;
			WriteRentLog(nLatnId,m_tno,nPrdInstNum,nSubInstNum,nOfrInstNum,false);
		}
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","lnAcctId[%ld] ofr inst size is empty",lnAcctId);
	}
	
	m_pComboAdpt->releaseUserMap(true);//清空数据
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadOldUser::CrossOfrInstChangeDeal end ");
	return 0;
}





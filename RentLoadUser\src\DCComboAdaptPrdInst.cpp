#include "DCComboAdaptPrdInst.h"
#include <utility>
#include <unistd.h>
#include <sys/types.h>
#include "DCParseXml.h"
#include "DCPerfStatistic.h"

#define DIFFTIME_US(Tb, Ta) ((((Tb)>>24) - ((Ta)>>24))*1000000 + ((Tb)&0xFFFFFF) - ((Ta)&0xFFFFFF))

using namespace std;

typedef pair<long,long> PAIRDETAIL;

//bool SORTOFRINST(const ocs::OfrInst A,const ocs::OfrInst B)
//{
//	return A.nCalcPriority >= B.nCalcPriority;
//}

DCComboAdaptPrdInst::DCComboAdaptPrdInst()
{
	m_pEventType=NULL;
	m_isGetAcctOfrInst = false;
	m_nProType = 0;
}

DCComboAdaptPrdInst::~DCComboAdaptPrdInst()
{
	SAFE_DELETE_PTR(m_pEventType);
}

void DCComboAdaptPrdInst::setInputPara(const STInputPara &inputPara,const STConfigPara &cfgpara)
{
	m_nProType = inputPara.nProType;    
    m_nBigAcctThreshold = cfgpara.nBigAcctThreshold;
    m_nGetDetail = cfgpara.nGetDetail;
    m_nMostBigAcctThreshold = cfgpara.nMostBigAcctThreshold;
    m_nContralGetOfrInst = cfgpara.nContralGetOfrInst;
	m_cfgpara = cfgpara;
}

//初始化
int DCComboAdaptPrdInst::init(DCDBManer* pDbm)
{
	int nRet = 0;
	m_pDbm = pDbm;

	//事件类型判断
	m_pEventType = NULL;
	m_pEventType = new DCRDEventType();
	nRet = m_pEventType->Init(m_pDbm);
	if (nRet < 0)
	{
        DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCComboAdaptPrdInst::init","init DCRDEventType failed, error code[%d]", nRet);
		return -1;
	}
	
	m_nBillingCycleId = 0;
	
	DCBIZLOG(DCLOG_LEVEL_INFO, 0,"","DCComboAdaptPrdInst::init success");
	DCDATLOG("RD00001:");
	return 0;
}


//发送完消息后清理容器
void DCComboAdaptPrdInst::releaseUserMap(bool isChangeAcct)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::releaseUserMap begin,isChangeAcct[%d]",isChangeAcct);
	if (m_vecOfrInstTmp.size()>0)
	{		
		m_vecOfrInstTmp.clear();
	}
	if(isChangeAcct)
	{
		m_vtmpOfr.clear();
		m_setOfrInstId.clear();
		m_mapOfrInstTmp.clear();
		//m_stAcctBodyReq.VOfrInst.clear();
		//m_stAcctBodyReq.VDetailInst.clear();
		//m_stAcctBodyReq.VRealRatingFee.clear();
		//m_stAcctBodyReq.VReqRatingFee.clear();
		m_stAcctHead.serv_id.clear();
		m_stAcctHead.crossserv.clear();
		m_vecOfrInstOutput.clear();
		m_mmapOfrDetail.clear();
		//m_vOfrInstDyn.clear();
		m_vOfrDetailDyn.clear();
		m_multimapOfrInstTmp.clear();
		m_multimapOfrInstPri.clear();
		m_isGetAcctOfrInst=false;
		m_vecGroupPrdInstId.clear();
	}
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::releaseUserMap end! ");
}


//处理主产品实例，包含实例过滤、销售品过滤，提取附属产品实例，保存主附产品实例，提取普选优惠实例ID
int DCComboAdaptPrdInst::ComboAdapt(ocs::StRentMainInfo& tPrdInst, int nLatnId)
{
	DCBIZLOG(DCLOG_LEVEL_INFO,0,"","DCComboAdaptPrdInst::ComboAdapt begin,ServId[%ld] AcctId[%ld] LatnId[%d],lnOfferId[%ld]",tPrdInst.lnProdInstId,tPrdInst.lnAcctId,nLatnId,tPrdInst.lnOfferId);
	DCDATLOG("RD00002:%ld!%ld!%d",tPrdInst.lnProdInstId,tPrdInst.lnAcctId,nLatnId);
	
	string strsql;	
	int nRet = 0;

    //过滤主产品实例
    char szSqlName[64]={0};
	sprintf(szSqlName,"data_prd_prd_main_inst_filter|%d",nLatnId);
	UDBSQL* p_main_prd_filter = m_pDbm->GetSQL(szSqlName);
	if(p_main_prd_filter == NULL)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","not find sql[%s]",szSqlName);
		return NO_FIND_SQL;
	}
	p_main_prd_filter->UnBindParam();
	p_main_prd_filter->BindParam(1,tPrdInst.lnProdInstId);
	p_main_prd_filter->GetSqlString(strsql);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::ComboAdapt","Do SQL: begin to do [data_prd_prd_main_inst_filter],sql :[%s] ",strsql.c_str());
    p_main_prd_filter->Execute();
    if(p_main_prd_filter->Next())
    {	
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::ComboAdapt","main prd_inst_id [%ld] filter.",tPrdInst.lnProdInstId);
		p_main_prd_filter->Close();
		return 0;
    }
	p_main_prd_filter->Close();
		
	//过滤销售品
	UDBSQL*p_main_ofr_filter = m_pDbm->GetSQL("data_prd_prd_ofr_main_inst_filter");
	if(p_main_ofr_filter == NULL)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCComboAdaptPrdInst::ComboAdapt","not find sql[data_prd_prd_ofr_main_inst_filter]");
		return NO_FIND_SQL;
	}
	p_main_ofr_filter->UnBindParam();
	p_main_ofr_filter->BindParam(1,nLatnId);
	p_main_ofr_filter->BindParam(2,tPrdInst.lnOfferId);
	p_main_ofr_filter->GetSqlString(strsql);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::ComboAdapt","Do SQL: begin to do [data_prd_prd_ofr_main_inst_filter],sql :[%s] ",strsql.c_str());
	p_main_ofr_filter->Execute();
	if(p_main_ofr_filter->Next())
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::ComboAdapt","main ofr_id [%ld] filter.",tPrdInst.lnOfferId);
		p_main_ofr_filter->Close();
		return 0;
	}
	p_main_ofr_filter->Close();
    
    //费用归属天 
	char szTime[24] = {0};
	PublicLib::GetTime(szTime,YYYYMM);
	int nDay = 0;
	//如果系统账期和处理账期相同，则实例天为归属天，否则为32天
	m_nBillingCycleId = DCDataCenter::instance()->iBillingCycleId;
	if (atoi(szTime) == m_nBillingCycleId )
	{
		nDay = DCDataCenter::instance()->iEndDay;
	}
	else
	{
		nDay = 32;
	}
	
	tPrdInst.nDay = nDay;
	tPrdInst.nBelongDay = DCDataCenter::instance()->iEndDay;
	tPrdInst.nBillCycleId = m_nBillingCycleId;
    m_vecOfrInstTmp.push_back(tPrdInst);
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::ComboAdapt","Main Ofr Inst record [PrdInstId:%ld OfrInstId:%ld OfrId:%ld AcctId:%ld lnGroupUserGroupId:%ld BillingCycleId:%d].",
		tPrdInst.lnProdInstId, tPrdInst.lnOfrInstId,tPrdInst.lnOfferId, tPrdInst.lnAcctId, tPrdInst.lnGroupUserGroupId, tPrdInst.nBillCycleId);

	// 查找附属产品
	if ( GetSubPrdInfo(tPrdInst.lnProdInstId,nLatnId,tPrdInst.lnAcctId )<0 )
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCComboAdaptPrdInst::ComboAdapt","GetSubPrdInfo failed,prdinstid [%ld].",tPrdInst.lnProdInstId);
		return -1;
	}

	// 保存主附产品实例并查出套餐OfrInstId
	if ( getAllUserOfrfInstance(tPrdInst,nLatnId )<0 )
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCComboAdaptPrdInst::ComboAdapt","getAllUserOfrfInstance failed,prdinstid [%ld].",tPrdInst.lnProdInstId);
		return -1;
	}

	// if(m_nContralGetOfrInst==1)//获取套餐实例开关
	// 添加tPrdInst.nEventTypeId != 902  停机保号就不用找套餐了
	if ((PROC_TYPE_PRE_NORMAL != m_nProType || tPrdInst.nEventTypeId != 902) && m_nContralGetOfrInst == 1)
	{
		//查找普选优惠实例和明细
		if ( GetDynOfrInst(tPrdInst.lnProdInstId,nLatnId)<0 )
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCComboAdaptPrdInst::ComboAdapt","GetDynOfrInst failed,prdinstid [%ld].",tPrdInst.lnProdInstId);
			return -1;
		}
	}

	m_vecGroupPrdInstId.push_back(tPrdInst.lnProdInstId);
	DCBIZLOG(DCLOG_LEVEL_INFO,0,"","DCComboAdaptPrdInst::ComboAdapt end,ServId[%ld] AcctId[%ld] LatnId[%d]",tPrdInst.lnProdInstId,tPrdInst.lnAcctId,nLatnId);
	return 0;
}


//提取并处理附属产品实例，包含销售品过滤
int DCComboAdaptPrdInst::GetSubPrdInfo(long lnServId, int nLatnId, long lnAcctId)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCComboAdaptPrdInst::GetSubPrdInfo","ServId[%ld] LatnId[%d] to find SubInstInfo",lnServId,nLatnId);

	ocs::StRentMainInfo  stSubInfo;
	
	char szbuf[30]={0};
	char szEffDate[18]={0};
	char szExpDate[18]={0};
	char szStatusCd[10]={0},szCompleteDate[18]={0},szOfferId[18]={0};
	string strsql;
	bool isTrue=false;
	const char *szFilterOfrId="12039,126,12723,12727,12730,12732,12733,12734,12735,12736,12738,12753,12760,12761,12762,2021,27388,378,380,386,387,392,393,397,422,437,440,46035,46036,48110030,500044100,503030,54101,74500,8039,8087,8088,84400,84500,20130315,20130316,20130317";
    //预付费指定过滤的ofrid
    const char *szPreFilterOfrId="54131,566174084,807946024,126,500044100,48110030,21708,12867,2021,8039,566174085,12758,74500,807960060";
	long lnSubPrdInstId=0;
	
    //费用归属天 
	char szTime[24] = {0};
	PublicLib::GetTime(szTime,YYYYMM);
	int nDay = 0;
	//如果系统账期和处理账期相同，则实例天为归属天，否则为32天
	if (atoi(szTime) == m_nBillingCycleId )
	{
		nDay = DCDataCenter::instance()->iEndDay;
	}
	else
	{
		nDay = 32;
	}

	char *sCycleBeginTime = DCDataCenter::instance()->sCycleBeginTime;
	char *sCycleEndTime = DCDataCenter::instance()->sCycleEndTime;
	
	try
	{
		UDBSQL*pQuery = m_pDbm->GetSQL("data_prd_prd_sub_inst");
		if(pQuery == NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","not find sql[data_prd_prd_sub_inst]");
			return -1;
		}
		UDBSQL*pQueryOfr = m_pDbm->GetSQL("data_prd_prd_ofr_inst_filter");
		if(pQueryOfr == NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCComboAdaptPrdInst::GetSubPrdInfo","not find sql[data_prd_prd_ofr_inst_filter]");
			return -1;
		}
		UDBSQL* p_sub_prd_filter = m_pDbm->GetSQL("data_prd_prd_sub_inst_filter");
		if(p_sub_prd_filter == NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCComboAdaptPrdInst::GetSubPrdInfo","not find sql[data_prd_prd_sub_inst_filter]");
			return -1;
		}

		pQuery->UnBindParam();
		pQuery->BindParam(1,lnServId);
		pQuery->BindParam(2,nLatnId);
		pQuery->GetSqlString(strsql);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::GetSubPrdInfo","Do SQL: begin to do [data_prd_prd_sub_inst],sql :[%s] lnServId[%ld]",strsql.c_str(),lnServId);

	    pQuery->Execute();
	    while(pQuery->Next())
	    {
	    	memset(szEffDate,0x00,sizeof(szEffDate));
	    	memset(szEffDate,0x00,sizeof(szExpDate));
	        pQuery->GetValue(1,szEffDate);	
	        pQuery->GetValue(2,szExpDate);

	        pQuery->GetValue(9,szStatusCd);
	        pQuery->GetValue(11,szCompleteDate);	//FirstFinishDate
	        pQuery->GetValue(6,szOfferId);
	        pQuery->GetValue(7,lnSubPrdInstId);	//prod_inst_id

            //预付费流程需剔除掉互斥销售品
        	if (PROC_TYPE_PRE_NORMAL == m_nProType && InCompare(szOfferId,szPreFilterOfrId) )
        	{
        		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","pre user sub ofr_id [%ld] filter",szOfferId);
                continue;
        	}
			/*where ((a.ofr_id in (12039,126,12723,12727,12730,12732,12733,12734,12735,12736,12738,12753,12760,12761,12762,2021,27388,378,380,386,387,392,393,397,422,
			437,440,46035,46036,48110030,500044100,503030,54101,74500,8039,8087,8088,84400,84500,20130315,20130316,20130317) and a.prd_inst_stas_id=1001 and a.complete_date< '20171201') 

			or (a.ofr_id in (12039,126,12723,12727,12730,12732,12733,12734,12735,12736,12738,12753,12760,12761,12762,
			2021,27388,378,380,386,387,392,393,397,422,437,440,46035,46036,48110030,500044100,503030,54101,74500,8039,8087,8088,84400,84500,20130315,
			20130316,20130317) and a.prd_inst_stas_id=1101 and a.complete_date>= '20171231')

			or a.ofr_id not in (12039,126,12723,12727,12730,12732,12733,12734,12735,12736,12738,12753,12760,12761,12762,2021,27388,378,380,386,387,
			392,393,397,422,437,440,46035,46036,48110030,500044100,503030,54101,74500,8039,8087,8088,84400,84500,20130315,20130316,20130317))

			and ((a.prd_inst_stas_id not in (3001, 1101, 1401, 1105)) or (a.prd_inst_stas_id = 1101 AND a.complete_date >='20171201'))
			and  A.COMPLETE_DATE <= '20171231'*/
			
			if( !InCompare(szStatusCd,"130000,150000,110000,1105") || ( strcmp(szStatusCd,"110000")==0 && strncmp(szCompleteDate,DCDataCenter::instance()->sCycleBeginDate,8)>=0)   )
			{
				isTrue=true;
			}else
				continue;
			if( strncmp(szCompleteDate,DCDataCenter::instance()->sCycleEndDate,8)<=0   )
			{
				isTrue=true;
			}
			else
				continue;
			
			isTrue=false;
			if(InCompare(szOfferId,szFilterOfrId) && strcmp(szStatusCd,"100000")==0 && strncmp(szCompleteDate,DCDataCenter::instance()->sCycleBeginDate,8)<0)
				isTrue=true;
			else if(InCompare(szOfferId,szFilterOfrId) && strcmp(szStatusCd,"110000")==0 && strncmp(szCompleteDate,DCDataCenter::instance()->sCycleEndDate,8)>=0)
				isTrue=true;
			else if(!InCompare(szOfferId,szFilterOfrId))
				isTrue=true;
			if(!isTrue)
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::GetSubPrdInfo","sub ofr_inst_id [%ld] filter.",lnSubPrdInstId);
				continue;
			}
			// 过滤prod_inst_id
			p_sub_prd_filter->UnBindParam();
			p_sub_prd_filter->BindParam(1, nLatnId);
			p_sub_prd_filter->BindParam(2, lnSubPrdInstId);
			p_sub_prd_filter->GetSqlString(strsql);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::GetSubPrdInfo","Do SQL: begin to do [data_prd_prd_sub_inst_filter],sql :[%s] lnSubPrdInstId[%ld]",strsql.c_str(),lnSubPrdInstId);
			p_sub_prd_filter->Execute();
			if (p_sub_prd_filter->Next())
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::GetSubPrdInfo","sub prod_inst_id [%ld] filter.", lnSubPrdInstId);
				continue;
			}	    	
			stSubInfo.szEffDate=szEffDate;
			stSubInfo.szExpDate=szExpDate;
			stSubInfo.szEventEffDate=szEffDate;
			stSubInfo.szEventExpDate=szExpDate;	
			  
	        pQuery->GetValue(6,stSubInfo.lnOfferId);
			//过滤销售品
			pQueryOfr->UnBindParam();
			pQueryOfr->BindParam(1,nLatnId);
			pQueryOfr->BindParam(2,stSubInfo.lnOfferId);
			pQueryOfr->GetSqlString(strsql);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::GetSubPrdInfo","Do SQL: begin to do [data_prd_prd_ofr_inst_filter],sql :[%s] lnOfferId[%ld]",strsql.c_str(),stSubInfo.lnOfferId);
			pQueryOfr->Execute();
			if(pQueryOfr->Next())
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::GetSubPrdInfo","sub ofr_id [%ld] filter.",stSubInfo.lnOfferId);
				continue;
			}
			
	        stSubInfo.lnGroupUserGroupId = lnSubPrdInstId;	//prod_inst_id
	        pQuery->GetValue(8,stSubInfo.lnCustId);	
	        stSubInfo.lnAcctId = lnAcctId;	
			stSubInfo.lnOfrInstId = -3;
			stSubInfo.lnProdInstId = lnServId; //acc_prod_inst_id
			stSubInfo.nEventTypeId = 900;//初始化
			stSubInfo.nCalcPriority = 0;
			
			//实例日期DD,费用归属日期YYYYMMDD
			stSubInfo.nDay = nDay;
			stSubInfo.nBelongDay = DCDataCenter::instance()->iEndDay;
			stSubInfo.nBillCycleId = m_nBillingCycleId;
			
	        pQuery->GetValue(9,szbuf);
			stSubInfo.szStatusCd = szbuf;
	        pQuery->GetValue(10,szbuf);	
			stSubInfo.szCreateDate = szbuf;	
	        pQuery->GetValue(11,szbuf);	
			stSubInfo.szFirstFinishDate = szbuf;	
	        pQuery->GetValue(12,szbuf);	
			stSubInfo.szInstallDate = szbuf;	
	        pQuery->GetValue(13,stSubInfo.lnProdId);	
	        pQuery->GetValue(4,szbuf);	
			stSubInfo.szUserTypeId = szbuf;		
			
			stSubInfo.lnOfrInstId = -3;
				
	    	m_vecOfrInstTmp.push_back(stSubInfo);
		
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::GetSubPrdInfo","Sub Ofr Inst record [PrdInstId:%ld OfrInstId:%ld OfrId:%ld CustId:%ld GroupId:%ld BillingCycleId:%d].",
				stSubInfo.lnProdInstId, stSubInfo.lnOfrInstId,stSubInfo.lnOfferId, stSubInfo.lnCustId, stSubInfo.lnGroupUserGroupId, stSubInfo.nBillCycleId);
				
	    }

		pQuery->Close();
		pQueryOfr->Close();
		p_sub_prd_filter->Close();
	} 
	catch(std::exception& e)
	{		
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCComboAdaptPrdInst::GetSubPrdInfo","Query [data_prd_prd_sub_inst] failed: %s",e.what());
		return -1;
	}
	return 0;	
}


//获取主附产品实例的月租事件类型，保存实例信息，获取用户A1 J1 F1 G1套餐实例ID
int DCComboAdaptPrdInst::getAllUserOfrfInstance(ocs::StRentMainInfo& tPrdInst,int nLatnId)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCComboAdaptPrdInst::getAllUserOfrfInstance","PrdInstId[%ld] LatnId[%d] to find InstInfo",tPrdInst.lnProdInstId,nLatnId);
	int nRet = 0;
	string strsql;
    char szBeginTime[15];
	PublicLib::GetTime(szBeginTime, YYYYMMDDHHMMSS);
	//得到当前系统时间
 	m_pEventType->SetCurrTime(szBeginTime);

	ocs::OfrInst stOfrInst;
	STRentEventType *m_pStRentEventType;
	vector<ocs::StRentMainInfo>::iterator iter = m_vecOfrInstTmp.begin();
	while(iter != m_vecOfrInstTmp.end())
	{
		stOfrInst.szEffDate = iter->szEffDate;
		stOfrInst.szExpDate = iter->szExpDate;
		stOfrInst.szEventEffDate = iter->szEventEffDate;
		stOfrInst.szEventExpDate = iter->szEventExpDate;
		stOfrInst.nEventTypeId = iter->nEventTypeId;
		stOfrInst.lnOfrId = iter->lnOfferId;
		stOfrInst.lnOfrInstId = iter->lnOfrInstId;
		stOfrInst.lnSelectGroupID = iter->lnGroupUserGroupId;
		stOfrInst.nCalcPriority = iter->nCalcPriority;	
		stOfrInst.nFeeType = -stOfrInst.lnOfrInstId;
		stOfrInst.nDynInstFlag = 0;//=1是普选优惠实例
				
		StInstElementValue stInfo;	
		
		strcpy(stInfo.sOfferState , tPrdInst.szStatusCd.c_str());//status_cd
		strcpy(stInfo.sCompletedDate , tPrdInst.szFirstFinishDate.c_str());//first_finish_date --prod_inst_
		strcpy(stInfo.sSubCompleted , tPrdInst.szFirstFinishDate.c_str());//first_finish_date --prod_inst_sub_
		strcpy(stInfo.sUserTypeId , tPrdInst.szUserTypeId.c_str());
		strcpy(stInfo.sInstallDate , tPrdInst.szInstallDate.c_str());//install_date
		strcpy(stInfo.sCrtDate , tPrdInst.szCreateDate.c_str());
		sprintf(stInfo.sPrdId ,"%ld", tPrdInst.lnProdId);
		if(PROC_TYPE_PRE_NORMAL == m_nProType)
		{
			stInfo.nIfPrepay = 1;
		}

		if(stOfrInst.nFeeType == FEE_TYPE_PRODUCT)
		{	
			strcpy(stInfo.sSubCompleted , iter->szFirstFinishDate.c_str());//first_finish_date --prod_inst_sub_
			if(strcmp(stInfo.sOfferState,"")==0)
				strcpy(stInfo.sOfferState , iter->szStatusCd.c_str());//status_cd
			if(strcmp(stInfo.sCompletedDate,"")==0)
				strcpy(stInfo.sCompletedDate , iter->szFirstFinishDate.c_str());//first_finish_date --prod_inst_
			if(strcmp(stInfo.sUserTypeId,"")==0)
				strcpy(stInfo.sUserTypeId , iter->szUserTypeId.c_str());
			if(strcmp(stInfo.sInstallDate,"")==0)
				strcpy(stInfo.sInstallDate , iter->szInstallDate.c_str());//install_date
			if(strcmp(stInfo.sCrtDate,"")==0)
				strcpy(stInfo.sCrtDate , iter->szCreateDate.c_str());
			if(strcmp(stInfo.sPrdId,"")==0)
				sprintf(stInfo.sPrdId ,"%ld", iter->lnProdId);
		}

		m_pEventType->SetTimeInfo(stInfo);
		
		nRet = m_pEventType->JudgeEventType(iter->lnOfrInstId,iter->lnProdInstId,iter->szEventEffDate,iter->szEventExpDate);
		if ( nRet > 0)
		{
			//类型替换
			if(m_pEventType->GetEventType(&m_pStRentEventType))
			{
				//取得起算时间和止算时间
				iter->szEventEffDate= m_pStRentEventType->sEffTime;
				iter->szEventExpDate= m_pStRentEventType->sExpTime;
				iter->nEventTypeId = m_pStRentEventType->nEventTypeId;
	
				stOfrInst.szEventEffDate = iter->szEventEffDate;
				stOfrInst.szEventExpDate = iter->szEventExpDate;
				stOfrInst.nEventTypeId = iter->nEventTypeId;
				// 获取主产品实例的事件类型
				if (stOfrInst.lnOfrInstId == -FEE_TYPE_RENT)
				{
					tPrdInst.nEventTypeId = stOfrInst.nEventTypeId;
				}
				//m_stAcctBodyReq.VOfrInst.push_back(stOfrInst);
				m_vecOfrInstOutput.push_back(stOfrInst);
			}
		}
		else
		{
			if(stOfrInst.lnOfrInstId== -FEE_TYPE_RENT)
				DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCComboAdaptPrdInst::getAllUserOfrfInstance","get event_type_id failed. main inst filter. main_prd_inst_id[%ld],lnOfferId[%ld]",iter->lnGroupUserGroupId,iter->lnOfferId);
			else
				DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCComboAdaptPrdInst::getAllUserOfrfInstance","get event_type_id failed. sub int filter. sub_prd_inst_id[%ld],lnOfferId[%ld]",iter->lnGroupUserGroupId,iter->lnOfferId);
		}
		
		++iter;
	}
	
	// if(m_nContralGetOfrInst==1)//获取套餐实例开关
	// 添加tPrdInst.nEventTypeId != 902  用户停机保号了就不用获取套餐了
	if ( (PROC_TYPE_PRE_NORMAL != m_nProType || tPrdInst.nEventTypeId != 902) && m_nContralGetOfrInst == 1)
	{
		long lnGroupId=0;
		set<long> setGroupId;
		m_vOfrInstId.clear();
		m_setA1OfrInstId.clear();
		//获取所有OfrInstId
		{
			nRet = GetOfrDetailInst(tPrdInst.lnProdInstId,"A1",nLatnId,tPrdInst.lnProdInstId);
			if ( nRet < 0 )
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCComboAdaptPrdInst::getAllUserOfrfInstance","Find detail info failed,lnPrdInstId[%ld]+[A1].",tPrdInst.lnProdInstId);
				return -1;
			}

			/*J1 刷DCA转为了A1
			nRet = GetOfrDetailInst(tPrdInst.lnProdInstId,"J1",nLatnId,tPrdInst.lnProdInstId);
			if ( nRet < 0 )
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCComboAdaptPrdInst::getAllUserOfrfInstance","Find detail info failed,lnPrdInstId[%ld]+[J1].",tPrdInst.lnProdInstId);
				return -1;
			}*/
			/*因无客户级套餐
			nRet = GetOfrDetailInst(tPrdInst.lnCustId,"D1",nLatnId);
			if ( nRet < 0 )
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"","Find detail info failed,lnCustId[%ld]+[D1].",tPrdInst.lnCustId);
				return -1;
			}*/
			//因消息为账户级，只提一次
			if(!m_isGetAcctOfrInst)
			{
				nRet = GetOfrDetailInst(tPrdInst.lnAcctId,"E1",nLatnId,tPrdInst.lnProdInstId);
				if ( nRet < 0 )
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"DCComboAdaptPrdInst::getAllUserOfrfInstance","Find detail info failed,lnAcctId[%ld]+[E1].",tPrdInst.lnAcctId);
					return -1;
				}
				m_isGetAcctOfrInst = true;
			}
			for(int iLoop = 0; iLoop < m_vOfrInstId.size(); ++iLoop)//查询O1
			{
				nRet = GetOfrDetailInst(m_vOfrInstId[iLoop], "O1", nLatnId, tPrdInst.lnProdInstId);
				if(nRet < 0)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCComboAdaptPrdInst::getAllUserOfrfInstance","Find detail info failed,ObjectId[%ld]+[O1].", m_vOfrInstId[iLoop]);
					return nRet;
				}
			}			
			for(std::set<long>::iterator itA1=m_setA1OfrInstId.begin();itA1!=m_setA1OfrInstId.end();itA1++) //查询O2
			{
				long lnA1OfferInstId = *itA1;
				nRet = GetOfrDetailInst(lnA1OfferInstId, "O2", nLatnId, tPrdInst.lnProdInstId);
				if(nRet < 0)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCComboAdaptPrdInst::getAllUserOfrfInstance","Find detail info failed,ObjectId[%ld]+[O2].", lnA1OfferInstId);
					return nRet;
				}
			}
			try
			{
				//F1类型, 选通过PrdInstId查找GroupId, 再通过GroupId查找OfrInstId
				UDBSQL*pQuery = m_pDbm->GetSQL("data_prd_grp_user_group");
				if(pQuery == NULL)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCComboAdaptPrdInst::getAllUserOfrfInstance","not find sql[data_prd_grp_user_group]");
					return -1;
				}
				pQuery->UnBindParam();
				//pQuery->BindParam(1,tPrdInst.szAccNum.c_str());
				//pQuery->BindParam(2,tPrdInst.szAreaCode.c_str());
				pQuery->BindParam(1,tPrdInst.lnProdInstId);
				pQuery->BindParam(2,nLatnId);
				pQuery->GetSqlString(strsql);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::getAllUserOfrfInstance","Do SQL: begin to do [data_prd_grp_user_group],sql :[%s] ",strsql.c_str());
			    pQuery->Execute();
			    while(pQuery->Next())
			    {
			   		pQuery->GetValue(1,lnGroupId);
					setGroupId.insert(lnGroupId);
			    }
				pQuery->Close();
			}
			catch(UDBException &e)
		    {
		        DCBIZLOG(DCLOG_LEVEL_ERROR,-e.GetErrorCode(),"DCComboAdaptPrdInst::getAllUserOfrfInstance","error info[%s] sql[%s]",e.ToString(),strsql.c_str());
		        return -e.GetErrorCode();
		    }
		    //通过GROUP_ID找ofr_inst_id
			for (set<long>::iterator itGroup = setGroupId.begin(); itGroup != setGroupId.end(); ++itGroup)
		    {
		    	lnGroupId = *itGroup;
				nRet = GetOfrDetailInst(lnGroupId,"F1",nLatnId,tPrdInst.lnProdInstId);
				if ( nRet < 0 )
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"DCComboAdaptPrdInst::getAllUserOfrfInstance","Find detail info failed,lnGroupId[%ld]+[F1].",lnGroupId);
					return -1;
				}

				nRet = GetOfrDetailInst(lnGroupId,"G1",nLatnId,tPrdInst.lnProdInstId);
				if ( nRet < 0 )
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"DCComboAdaptPrdInst::getAllUserOfrfInstance","Find detail info failed,lnGroupId[%ld]+[G1].",lnGroupId);
					return -1;
				}
			}			  
		}
	}
	return 0;
}

int DCComboAdaptPrdInst::QueryPlan(long lnOfrId,int& nOfferType,long& lnPlanId,string& strOfferName,int& nPriority)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","QueryPlan begin,lnOfrId[%ld]",lnOfrId);
	lnPlanId = 0;
	nOfferType = 0;
	strOfferName = "";
	nPriority = 0;
	string strsql;
	try
	{
		UDBSQL*pQuery = m_pDbm->GetSQL("pricing_plan_find");
		if(pQuery == NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","not find sql[pricing_plan_find]");
			return -1;
		}
		pQuery->UnBindParam();
		pQuery->BindParam(1,lnOfrId);
		pQuery->GetSqlString(strsql);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","Do SQL: begin to do [pricing_plan_find],sql :[%s] ",strsql.c_str());

	    pQuery->Execute();
	    while(pQuery->Next())
	    {	
	    	pQuery->GetValue(1,lnPlanId);
			pQuery->GetValue(3,nOfferType);			    			
		    pQuery->GetValue(4,strOfferName);
			pQuery->GetValue(5,nPriority);
			break;
	    }
		pQuery->Close();
	
	} 
	catch(std::exception& e)
	{		
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"","QueryPlan failed: [%s] sql[%s]",e.what(),strsql.c_str());
	}	
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","QueryPlan end,lnOfrId[%ld],nOfferType[%d],lnPlanId[%ld],nPriority[%d]",lnOfrId,nOfferType,lnPlanId,nPriority);
	return 0;
}

int DCComboAdaptPrdInst::QueryOfrExtAttr(long lnOfrId,long lnAttrId,string& strDefaultValue)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","QueryOfrExtAttr begin,lnOfrId[%ld],lnAttrId[%ld]",lnOfrId,lnAttrId);
	strDefaultValue = "";
	string strsql;
	try
	{
		UDBSQL*pQuery = m_pDbm->GetSQL("QueryOfrExtAttr");
		if(pQuery == NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","not find sql[QueryOfrExtAttr]");
			return -1;
		}
		pQuery->UnBindParam();
		pQuery->BindParam(1,lnOfrId);
		pQuery->BindParam(2,lnAttrId);
		pQuery->GetSqlString(strsql);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","Do SQL: begin to do [QueryOfrExtAttr],sql :[%s] ",strsql.c_str());

	    pQuery->Execute();
	    while(pQuery->Next())
	    {	
	    	pQuery->GetValue(1,strDefaultValue);			
			break;
	    }
		pQuery->Close();
	
	} 
	catch(std::exception& e)
	{		
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"","QueryOfrExtAttr failed: [%s] sql[%s]",e.what(),strsql.c_str());
		return -1;
	}	
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","QueryOfrExtAttr end,lnOfrId[%ld],lnAttrId[%ld],strDefaultValue[%s]",lnOfrId,lnAttrId,strDefaultValue.c_str());
	return 0;
}


//是否需要过滤销售品实例,需要过滤为true 不需要为false
bool DCComboAdaptPrdInst::filterOfrInstId(long lnOfrInstId,int nLatnId)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","DCComboAdaptPrdInst::filterOfrInstId begin,lnOfrInstId[%ld]",lnOfrInstId);

	string strsql;
	try
	{
	    char szSqlName[64]={0};
		sprintf(szSqlName,"data_prd_fileter_ofrinstid|%d",nLatnId);
		UDBSQL*pQuery = m_pDbm->GetSQL(szSqlName);
		if(pQuery == NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCComboAdaptPrdInst::filterOfrInstId","not find sql[%s]",szSqlName);
			return false;
		}
		pQuery->UnBindParam();
		pQuery->BindParam(1,lnOfrInstId);

		pQuery->GetSqlString(strsql);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::filterOfrInstId","Do SQL: begin to do [data_prd_fileter_ofrinstid],sql :[%s] ",strsql.c_str());

	    pQuery->Execute();
	    if(pQuery->Next())
	    {	
	    	pQuery->Close();
	        return true;
	    }
		pQuery->Close();
	
	} 
	catch(std::exception& e)
	{		
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCComboAdaptPrdInst::filterOfrInstId","filterOfrInstId failed: [%s] sql[%s]",e.what(),strsql.c_str());
	}
	return false;
}

//获取销售品优先级(账务那边先处理基础套餐,再处理附属,最后处理优惠套餐,优惠套餐按优先级从高到低来处理)
int DCComboAdaptPrdInst::getPriority(long lnOfrId,int nLatnId)
{	
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","getPriority begin,lnOfrId[%ld]",lnOfrId);
	int tmp_priority = 0;
	string strValue = "";
	string strsql;
	int nRet = 0;
	bool bFindFlag = false;	
	try
	{
		char szSql[64]={0};
		sprintf(szSql,"data_prd_get_priorty");
		UDBSQL*pQuery = m_pDbm->GetSQL(szSql);
		if(pQuery == NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","not find sql[%s]",szSql);
			return -1;
		}
		pQuery->UnBindParam();
		pQuery->BindParam(1,nLatnId);
		pQuery->BindParam(2,lnOfrId);
		pQuery->GetSqlString(strsql);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","Do SQL: begin to do [data_prd_get_priorty],sql :[%s] ",strsql.c_str());

		pQuery->Execute();
		if(pQuery->Next())
		{	
			pQuery->GetValue(1,strValue);
			if (!strValue.empty())
			{					
				bFindFlag = true;
				tmp_priority = atoi(strValue.c_str());
			    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","find a record:lnOfrId[%ld],tmp_priority[%d]",lnOfrId,tmp_priority);
			}
		}
		pQuery->Close();

	} 
	catch(std::exception& e)
	{		
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"","getPriority failed: [%s] sql[%s]",e.what(),strsql.c_str());
		return -1;
	}
	if (bFindFlag)
	{		
		return tmp_priority;
	}
	int nOfferType = 0;
	long lnPricingPlanId = 0;
	string strOfferName = "";
	nRet = QueryPlan(lnOfrId,nOfferType,lnPricingPlanId,strOfferName,tmp_priority);
	if (nRet < 0)
	{		
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","QueryPlan failed,lnOfrId[%ld]",lnOfrId);
		return -1;
	}		
	return tmp_priority;
	
}

int DCComboAdaptPrdInst::QueryChargeStatusCd(string strCRMStatusCd,string& strChargetatusCd)
{
	int tmp_priority = 0;
	string strsql;	
	bool bFindFlag=false;
	try
	{
		char szSql[64]={0};
		sprintf(szSql,"QUERY_PAR_STATUS_CHARGE_RULE");
		UDBSQL*pQuery = m_pDbm->GetSQL(szSql);
		if(pQuery == NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","not find sql[%s]",szSql);
			return -1;
		}
		pQuery->UnBindParam();
		pQuery->BindParam(1,strCRMStatusCd.c_str());

		pQuery->GetSqlString(strsql);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","Do SQL: begin to do [QUERY_PAR_STATUS_CHARGE_RULE],strCRMStatusCd[%s],sql :[%s] ",strCRMStatusCd.c_str(),strsql.c_str());

		pQuery->Execute();
		if(pQuery->Next())
		{	
			pQuery->GetValue(1,strChargetatusCd);			
			bFindFlag = true;
		}
		pQuery->Close();
		if (!bFindFlag)
		{
			strChargetatusCd = strCRMStatusCd;
		}

	} 
	catch(UDBException& e)
	{		
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"","QUERY_PAR_STATUS_CHARGE_RULE failed: [%s] sql[%s]",e.what(),strsql.c_str());
		return -1;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","QueryChargeStatusCd end,strCRMStatusCd[%s],strChargetatusCd[%s],bFindFlag[%d]", strCRMStatusCd.c_str(),strChargetatusCd.c_str(),bFindFlag);

	return 0;
}


int DCComboAdaptPrdInst::getPriorityFromTbOffer(long lnOfrId,int& tmp_priority)
{	
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","getPriorityFromTbOffer begin,lnOfrId[%ld]",lnOfrId);
	tmp_priority = 0;
	string strsql;
	try
	{
		char szSql[64]={0};
		sprintf(szSql,"pricing_plan_find");
		UDBSQL*pQuery = m_pDbm->GetSQL(szSql);
		if(pQuery == NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","not find sql[%s]",szSql);
			return false;
		}
		pQuery->UnBindParam();
		pQuery->BindParam(1,lnOfrId);

		pQuery->GetSqlString(strsql);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","Do SQL: begin to do [query_offer_pricing_plan],sql :[%s] ",strsql.c_str());

		pQuery->Execute();
		if(pQuery->Next())
		{	
			pQuery->GetValue(5,tmp_priority);
		}
		pQuery->Close();

	} 
	catch(std::exception& e)
	{		
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"","getPriorityFromTbOffer failed: [%s] sql[%s]",e.what(),strsql.c_str());
		return -1;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","getPriorityFromTbOffer end,lnOfrId[%ld],tmp_priority[%d]",lnOfrId,tmp_priority);
	return 0;
}



//根据RoleId+RoleType获取销售品实例ID
//prd_inst_id+A1 prd_inst_id+J1 acct_id+E1 group_id+F1 group_id+G1
int DCComboAdaptPrdInst::GetOfrDetailInst(long RoleId,const char* RoleType,int nLatnId,long lnServId)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::getOfrDetailInst begin,OfrRoleId[%ld],OfrInstRefType[%s]", RoleId, RoleType);

	char *sCycleBeginTime = DCDataCenter::instance()->sCycleBeginTime;
	char *sCycleEndTime = DCDataCenter::instance()->sCycleEndTime;

	ST_ShiftOfrInst tmpOfrInst;
    char szSqlName[64]={0};
	sprintf(szSqlName,"data_ofr_detail_inst_o");
    UDBSQL   * pSQL = m_pDbm->GetSQL(szSqlName);
    if(pSQL==NULL)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCComboAdaptPrdInst::GetOfrDetailInst", "Not find  SQL [%s] ",szSqlName);
        return -1;
    }
	long lnOfrInstId=0,lnOfrId=0;
	char szEffDate[16]={0},szExpDate[16]={0},szModDate[16]={0};
	char stmp[16] = { 0 };
	int nRecNum=0,nFlag=0, nOfrStasId=0;
	int OfferType=0;
	string strsql;
	int nRet = 0;
    try
    {
		pSQL->UnBindParam();
		pSQL->BindParam(1,RoleId);
		pSQL->BindParam(2,RoleType);
		pSQL->BindParam(3,nLatnId);

		pSQL->GetSqlString(strsql);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::GetOfrDetailInst","Do SQL: begin to do [data_ofr_detail_inst_o],sql :[%s] ",strsql.c_str());
        pSQL->Execute();
        while(pSQL->Next())
        {
    		pSQL->GetValue(2,lnOfrInstId);
			//过滤明细的生失效时间
			pSQL->GetValue(8,szEffDate);
			pSQL->GetValue(9,szExpDate);
			if ((strcmp(szEffDate,"") != 0) && (strcmp(szExpDate,"") != 0))
			{
				if (strncmp(sCycleBeginTime,szExpDate,8) > 0 || strncmp(sCycleEndTime,szEffDate,8) < 0)
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::GetOfrDetailInst","ofr_inst_id[%ld] filter by detail date.effdate[%s] expdate[%s]",lnOfrInstId,szEffDate,szExpDate);
					continue;
				}
			}
		
			if (m_setOfrInstId.find(lnOfrInstId) == m_setOfrInstId.end())
			{
				UDBSQL*pQuery = m_pDbm->GetSQL("data_prd_ofr_inst");
				if(pQuery == NULL)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCComboAdaptPrdInst::GetOfrDetailInst","not find sql[data_prd_ofr_inst]");
					return false;
				}
				pQuery->UnBindParam();
				pQuery->BindParam(1,lnOfrInstId);
				pQuery->BindParam(2,nLatnId);
				pQuery->GetSqlString(strsql);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::GetOfrDetailInst","Do SQL: begin to do [data_prd_ofr_inst],sql :[%s] ",strsql.c_str());

			    pQuery->Execute();
			    while(pQuery->Next())
			    {	
					pQuery->GetValue(1,lnOfrId );
					pQuery->GetValue(3,szEffDate );
					pQuery->GetValue(4,szExpDate );
					pQuery->GetValue(5,stmp );
					nOfrStasId = atoi(stmp);					
					pQuery->GetValue(6,szModDate);	
					pQuery->GetValue(7,OfferType);	

					string strDefaultValue = "";
					nRet = QueryOfrExtAttr(lnOfrId,301,strDefaultValue);  //daily rent ofr
					if (nRet<0)
					{					
						DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "QueryOfrExtAttr failed,lnOfrId[%ld],attr_id[301]", lnOfrId);
						return -1;
					}	
					bool b7Cancel = false; //7 day unsubscribe without reason ofrinst
					if (m_cfgpara.SevenCancelSwitch)
					{
						bool bFind = false;						
						long ln7CancelAttrId = 0;					
						std::string strParaValue;							
						std::string strParmGroup = "ACCT.CONFIG";
						std::string strParmKey = "7_CANCEL_OFRINST";						
						nRet = QuerySysParam(strParmGroup,strParmKey,strParaValue,bFind); 
						if (nRet<0)
						{					
							DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "QuerySysParam failed,lnOfrInstId[%ld]", lnOfrInstId);
							return -1;
						}
						if (bFind)
						{
							ln7CancelAttrId = atol(strParaValue.c_str());
							nRet = Query7CancelOfrInstAttr(lnOfrInstId,nLatnId,ln7CancelAttrId,b7Cancel);  
							if (nRet<0)
							{					
								DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "QueryOfrInstAttr failed,lnOfrInstId[%ld],attr_id[%ld]", lnOfrInstId,ln7CancelAttrId);
								return -1;
							}
						}	
					}					
							
					
					//过滤失效记录
	 				//where ((eff_date <= '[CYCLE_END_DATE]' and exp_date >= '[CYCLE_END_DATE]') or (eff_date <= '[CYCLE_END_DATE]' and exp_date >= '[CYCLE_BEGIN_DATE]'  and  ofr_id>990000 and ofr_id <1000000   AND  eff_date<=exp_date))
					//and ((ofr_inst_stas_id = 1001) or (ofr_inst_stas_id in (1101,1201) and mod_date >= '[CYCLE_BEGIN_DATE]'))
					if ((strcmp(szEffDate,"") != 0) && (strcmp(szExpDate,"") != 0))
					{
						if (strncmp(sCycleBeginTime,szExpDate,8) > 0 || strncmp(sCycleEndTime,szEffDate,8) < 0)
						{
							DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::GetOfrDetailInst","ofr_inst_id[%ld] filter by inst date.effdate[%s] expdate[%s]",lnOfrInstId,szEffDate,szExpDate);
							continue;
						}
						nFlag=0;
						if ((strncmp(szEffDate,DCDataCenter::instance()->sCycleEndDate,8) <= 0 && strncmp(szExpDate,DCDataCenter::instance()->sCycleEndDate,8) >= 0) ||
							(strncmp(szEffDate,DCDataCenter::instance()->sCycleEndDate,8) <= 0 && strncmp(szExpDate,DCDataCenter::instance()->sCycleBeginDate,8) >= 0 && ("1" == strDefaultValue || b7Cancel) && strncmp(szEffDate,szExpDate,8)<=0))
						{
							if (nOfrStasId==1000 || ((nOfrStasId==1100 || nOfrStasId==1097) && strncmp(szModDate,DCDataCenter::instance()->sCycleBeginDate,8)>=0))
								nFlag=1;
						}
						if(nFlag==0)
						{
							DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::GetOfrDetailInst","ofr_inst_id[%ld] filter by inst other info.effdate[%s] expdate[%s] ofrid[%ld] ofrstasid[%d] moddate[%s]",lnOfrInstId,szEffDate,szExpDate,lnOfrId,nOfrStasId,szModDate);
							continue;
						}
					}					
					//过滤ofr_inst_id  DATA_PRD_FILETER_OFRINSTID
					if(filterOfrInstId(lnOfrInstId,nLatnId))
					{
						DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::GetOfrDetailInst","ofr_inst_id[%ld] filter by ofr_inst_id.",lnOfrInstId);
						continue;
					}

					tmpOfrInst.lnOfrInstId = lnOfrInstId;
					tmpOfrInst.lnOfrId = lnOfrId;
					tmpOfrInst.lnSelectGroupID = tmpOfrInst.lnOfrInstId;
					tmpOfrInst.nFeeType = 7;
					tmpOfrInst.nEventTypeId    = 950;
					tmpOfrInst.szEffDate = szEffDate;
					tmpOfrInst.szExpDate = szExpDate;
					tmpOfrInst.szEventEffDate = szEffDate;
					tmpOfrInst.szEventExpDate = szExpDate;
					tmpOfrInst.nDynInstFlag = 0;//=1是普选优惠实例
					tmpOfrInst.nCalcPriority = getPriority(tmpOfrInst.lnOfrId,nLatnId); 
					tmpOfrInst.lnPrdInstId = lnServId;
					tmpOfrInst.b7Cancel = b7Cancel;					

					if(OfferType == 11 && strcmp(RoleType, "A1") == 0)
					{
						m_vOfrInstId.push_back(lnOfrInstId);
					}
					else if(OfferType == 11 && strcmp(RoleType, "A1") != 0)
					{
						m_vtmpOfr.push_back(lnOfrInstId);
					}
					m_setOfrInstId.insert(lnOfrInstId);	
					m_mapOfrInstTmp.insert(make_pair(lnOfrInstId,tmpOfrInst));	
					nRecNum++;
				    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::GetOfrDetailInst","Insert lnOfrInstId:[%ld],lmOfrId:[%ld],szEffDate[%s],szExpDate[%s]",lnOfrInstId,lnOfrId,szEffDate,szExpDate);
				}
				pQuery->Close();
			}
			else
			{
				if(find(m_vtmpOfr.begin(), m_vtmpOfr.end(), lnOfrInstId) != m_vtmpOfr.end() && strcmp(RoleType, "A1") == 0)
				{
					m_vtmpOfr.erase(find(m_vtmpOfr.begin(), m_vtmpOfr.end(), lnOfrInstId)); 
					m_vOfrInstId.push_back(lnOfrInstId);
				}
			}
			if(strcmp(RoleType, "A1") == 0)
			{
				m_setA1OfrInstId.insert(lnOfrInstId);	
			}
			
        }
    }
    catch(UDBException& e)
    {
    	string strSQL;
        pSQL->GetSqlString(strSQL);
       	DCBIZLOG(DCLOG_LEVEL_ERROR,-e.GetErrorCode(),"DCComboAdaptPrdInst::GetOfrDetailInst","execption[%s] sql[%s]",e.ToString(), strSQL.c_str());
		return -1;
	}
	pSQL->Close();
        	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::GetOfrDetailInst end,OfrRoleId[%ld],OfrInstRefType[%s],get %d record",RoleId, RoleType,nRecNum);

	return 0;
}


//获取销售品实例的可选包并保存销售品实例
int DCComboAdaptPrdInst::pushOfrInstWithCombineGroup(ST_ShiftOfrInst ofrInst, int nLatnId)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::pushOfrInstWithCombineGroup begin,lnOfrInstId[%ld],nLatnId[%d],b7Cancel[%d]",ofrInst.lnOfrInstId,nLatnId,ofrInst.b7Cancel);

	long lnGroupId=0;		
	char szEffDate[16]={0},szExpDate[16]={0};
	vector<long> vecOfrInstGroup;
	string strsql;
	try
	{
		char *sCycleBeginTime = DCDataCenter::instance()->sCycleBeginTime;
		char *sCycleEndTime = DCDataCenter::instance()->sCycleEndTime;
		
		UDBSQL*pQuery = m_pDbm->GetSQL("QueryTbPrdOfrStraInst");
		if(pQuery == NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCComboAdaptPrdInst::pushOfrInstWithCombineGroup","not find sql[QueryTbPrdOfrStraInst]");
			return -1;
		}
		pQuery->UnBindParam();
		pQuery->BindParam(1,ofrInst.lnOfrInstId);
		pQuery->BindParam(2,nLatnId);

		pQuery->GetSqlString(strsql);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::pushOfrInstWithCombineGroup","Do SQL: begin to do [QueryTbPrdOfrStraInst],sql :[%s] ",strsql.c_str());

	    pQuery->Execute();
	    while(pQuery->Next())
	    {	
	        pQuery->GetValue(2,szEffDate); 
	        pQuery->GetValue(3,szExpDate); 
			if(strcmp(szExpDate,"") == 0)
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::pushOfrInstWithCombineGroup","OfrStraInst Filter by ExpDate is null.");
				continue;
			}
			//过滤失效记录
			if ((strcmp(szEffDate,"") != 0) && (strcmp(szExpDate,"") != 0))
			{			
			    if (!ofrInst.b7Cancel)
			    {
					if (0 == strncmp(szEffDate,szExpDate,8))
					{
						DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::pushOfrInstWithCombineGroup","OfrStraInst Filter by EffDate[%s]=ExpDate[%s].",szEffDate,szExpDate);
						continue;
					}
				}			
				
				if (strncmp(sCycleBeginTime,szExpDate,8) > 0 || strncmp(sCycleEndTime,szEffDate,8) < 0)
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::pushOfrInstWithCombineGroup","OfrStraInst Filter by EffDate[%s] or ExpDate[%s] out of this month[%d].",szEffDate,szExpDate,m_nBillingCycleId);
					continue;
				}
			}
	        pQuery->GetValue(1,lnGroupId); 
			if (lnGroupId != 0 && lnGroupId != -1)   //日账=0或月账=-1 表示必选包
			{				
				vecOfrInstGroup.push_back(lnGroupId);
			}
	    }
		pQuery->Close();
		
		ofrInst.vGroupId = vecOfrInstGroup;
		
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::pushOfrInstWithCombineGroup","Ofr Ofr Inst record [OfrInstId:%ld OfrId:%ld GroupIdSize:%ld][PriPrdInstId=%ld].",
				ofrInst.lnOfrInstId,ofrInst.lnOfrId, ofrInst.vGroupId.size(),ofrInst.lnPrdInstId);
		//m_stAcctBodyReq.VOfrInst.push_back(ofrInst);
		/*ST_ShiftOfrInst tmp;
		tmp.lnOfrId = ofrInst.lnOfrId;
		tmp.lnOfrInstId = ofrInst.lnOfrInstId;
		tmp.lnSelectGroupID = ofrInst.lnSelectGroupID;
		tmp.nDynInstFlag = ofrInst.nDynInstFlag;
		tmp.nEventTypeId = ofrInst.nEventTypeId;
		tmp.nFeeType = ofrInst.nFeeType;
		tmp.szEffDate = ofrInst.szEffDate;
		tmp.szEventEffDate = ofrInst.szEventEffDate;
		tmp.szEventExpDate = ofrInst.szEventExpDate;
		tmp.szExpDate = ofrInst.szExpDate;
		tmp.vGroupId = ofrInst.vGroupId;
		tmp.nCalcPriority = ofrInst.nCalcPriority;
		tmp.lnPrdInstId = lnServId;*/
		m_multimapOfrInstPri.insert(make_pair(ofrInst,1));
	} 
	catch(std::exception& e)
	{		
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCComboAdaptPrdInst::pushOfrInstWithCombineGroup","error info: [%s] sql[%s]",e.what(),strsql.c_str());
		return -1;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::pushOfrInstWithCombineGroup end,lnOfrInstId[%ld],m_multimapOfrInstPri.size[%d]",ofrInst.lnOfrInstId,m_multimapOfrInstPri.size());
	return 0;
}



int DCComboAdaptPrdInst::QuerySysParam(std::string strParmGroup,std::string strParmKey,std::string& strParaValue,bool& bFind)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","QuerySysParam begin,strParmGroup[%s],strParmKey[%s]",strParmGroup.c_str(),strParmKey.c_str());
	bFind = false;
	try
	{
		char szSqlname[64] = {0};
		sprintf(szSqlname, "QuerySysParam");

		UDBSQL *pQuery = NULL;
		pQuery = m_pDbm->GetSQL(szSqlname);
		if (NULL == pQuery)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,0,"","get query[%s] failed.",szSqlname);
			return -1;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","Get sql [%s] OK",szSqlname);
		pQuery->UnBindParam();
		pQuery->BindParam(1, strParmGroup);
		pQuery->BindParam(2, strParmKey);

		string strSql;
		pQuery->GetSqlString(strSql);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "sql[%s] is[%s]", szSqlname,strSql.c_str());

		pQuery->Execute();
		while (pQuery->Next())
		{
			pQuery->GetValue(1, strParaValue);			
			if (0==strParaValue.length())
			{
				continue;
			}
			bFind = true;
		}
		pQuery->Close();
		if (!bFind)
		{			
		    DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","can not find record in table SM_SYSTEM_PARAMETER,strParmGroup[%s],strParmKey[%s]",strParmGroup.c_str(),strParmKey.c_str());
		}
	}
	catch(UDBException &e)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-e.GetErrorCode(),"","error info[%s]",e.ToString());
		return -1;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","QuerySysParam end,strParmGroup[%s],strParmKey[%s],strParaValue[%s],bFind[%d]",strParmGroup.c_str(),strParmKey.c_str(),strParaValue.c_str(),bFind);
	return 0;
}


//组装消息头信息
int DCComboAdaptPrdInst::SetHeadInfo(int nLatnId,int nBillingCycleId,long lnAcctId,int nAcctType,T_UserInfo tUserInfo)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::SetHeadInfo begin,lnAcctId[%ld],nAcctType[%d],nUserMode[%d],nObversionMode[%d],nBillingCycleId[%d],nTriggerCycle[%d]",lnAcctId, nAcctType,tUserInfo.nUserMode,tUserInfo.nObversionMode,nBillingCycleId,tUserInfo.nTriggerCycle);
    int nRet = 0;
	int nIsBigAcct=0,nMsgSrc=2;
	switch(nAcctType)
	{
	    case SPECIAL_ACCT_COMM:
	    {
            nIsBigAcct = 0;
		    break;
		}
	    case SPECIAL_ACCT_BIG:
	    {
            nIsBigAcct = 1;
		    break;
		}		   
	    case SPECIAL_ACCT_INST:
		{
            nIsBigAcct = 2;
		    nMsgSrc = 6;
		    break;
		}		    
	    case SPECIAL_ACCT_MOST:
	    {
            nIsBigAcct = 3;
		    break;
		}		
	    default:
	    {
            nIsBigAcct = 1;
		    break;
		}		    
	}

	long lnPrdInstId=-99;
	char szStatusCd[20]={0};
	int nPaymentMode=0;
	char szCurTime[20]={0};
	PublicLib::GetTime(szCurTime, YYYYMMDDHHMMSS);
	string strsql;
	
	m_stFmtHead.latnid = nLatnId;
	m_stFmtHead.opertype = "acct";
	//正常定时流程为rent，试算为trial
	if(tUserInfo.nUserMode == USER_TRIAL)
	{
		m_stFmtHead.msgtype = "trial";
	}
	else
	{
		m_stFmtHead.msgtype = "rent";
	}	

	//m_stAcctHead.BatchNo = getpid();//用于账务优惠分组计数，避免同一账户多进程发送时计数混乱
	m_stAcctHead.BatchNo = pthread_self();
	m_stAcctHead.latn_id = nLatnId;
	char szBillingCycleId[8]={0};
	sprintf(szBillingCycleId,"%d",nBillingCycleId);
	m_stAcctHead.billing_cycle = szBillingCycleId;

	char szTriggerCycle[9]={0};
	sprintf(szTriggerCycle,"%d",tUserInfo.nTriggerCycle);
	m_stAcctHead.trigger_cycle = szTriggerCycle;
	
	m_stAcctHead.messagesource = nMsgSrc;
	m_stAcctHead.acct_id = lnAcctId;
	m_stAcctHead.user_mode = tUserInfo.nUserMode;
	m_stAcctHead.obversion_mode = tUserInfo.nObversionMode;

    try
	{
		UDBSQL* pQuery = m_pDbm->GetSQL("info_acct");
		if(pQuery ==NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCComboAdaptPrdInst::SetHeadInfo","not find sql info_acct");
			return -1;
		}
		pQuery->UnBindParam();
		pQuery->BindParam(1,lnAcctId);//ACCT_ID
		pQuery->BindParam(2,nLatnId);
		pQuery->GetSqlString(strsql);
		pQuery->Execute();
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCComboAdaptPrdInst::SetHeadInfo","SQL[info_acct]:[%s]",strsql.c_str());  
		if(pQuery->Next())
		{
			pQuery->GetValue(1,lnPrdInstId);//ET_PRD_INST_ID
		}
		pQuery->Close();
		
		UDBSQL* pQueryPrd = m_pDbm->GetSQL("data_serv");
		if(pQueryPrd ==NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCComboAdaptPrdInst::SetHeadInfo","not find sql data_serv");
			return -1;
		}
		pQueryPrd->UnBindParam();
		pQueryPrd->BindParam(1,lnPrdInstId);
		pQueryPrd->BindParam(2,nLatnId);
		pQueryPrd->GetSqlString(strsql);
		pQueryPrd->Execute();
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCComboAdaptPrdInst::SetHeadInfo","lnPrdInstId[%ld],SQL[data_serv]:[%s]",lnPrdInstId,strsql.c_str());  
		if(pQueryPrd->Next())
		{
			pQueryPrd->GetValue(11,szStatusCd);//status_cd
			pQueryPrd->GetValue(17,nPaymentMode);//PAYMENT_MODE_CD 7
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCComboAdaptPrdInst::SetHeadInfo","not find record by data_serv");
		}
		pQueryPrd->Close();		
		
	}
	catch(UDBException &e)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR,-e.GetErrorCode(),"DCComboAdaptPrdInst::SetHeadInfo","error info[%s] sql[%s]",e.ToString(),strsql.c_str());
        return -e.GetErrorCode();
    }	
	if(nAcctType==SPECIAL_ACCT_INST || lnAcctId==-99)
	{		
		m_stAcctHead.status_cd = "100000";		
	}
	else
	{		
		m_stAcctHead.status_cd = szStatusCd;		
	}
	string strChargetatusCd;
	nRet = QueryChargeStatusCd(m_stAcctHead.status_cd,strChargetatusCd);
	if (nRet < 0)
	{				
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","Curr PrdInstId[%ld] AcctId[%ld] QueryChargeStatusCd filed",lnPrdInstId,lnAcctId);
		return -1;
	}
	m_stAcctHead.status_cd = strChargetatusCd;
	m_stAcctHead.big_acct_flag = nIsBigAcct;//=1大账户
	m_stAcctHead.start_time = szCurTime;
	
    if(2100 == nPaymentMode)
    {
        m_stAcctHead.if_prepay = 2;   //(0:后付费,1:实时预付费,2:定时预付费)
		m_stAcctHead.creditCtlFlag = 3; //(1:后付费用户,2:预后融合用户,3:预付费用户)
		m_stAcctHead.platform = 1;  //预付费
	}
	else if(1200 == nPaymentMode)
	{
		m_stAcctHead.if_prepay = 0;
		m_stAcctHead.creditCtlFlag = 1;
		m_stAcctHead.platform = 0;  //后付费
	}
	else
	{
		m_stAcctHead.if_prepay = -1;  //融合用户这个字段无用
		m_stAcctHead.creditCtlFlag = 2;
	}
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::SetHeadInfo end,lnAcctId[%ld],nAcctType[%d],if_prepay[%d],big_acct_flag[%d],nPaymentMode[%d],creditCtlFlag[%d]",lnAcctId, nAcctType,m_stAcctHead.if_prepay,m_stAcctHead.big_acct_flag,nPaymentMode,m_stAcctHead.creditCtlFlag);
	return 0;
}


//获取账户下所有销售品实例信息，包含E1套餐实例ID获取，销售品实例信息查询，销售品实例明细信息查询
int DCComboAdaptPrdInst::getAllAcctOfrfInstance(long lnAcctId,int nLatnId,long lnLastServId)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCComboAdaptPrdInst::getAllAcctOfrfInstance","AcctId/CrossModGroupId[%ld] LatnId[%d] to find InstInfo,m_setOfrInstId.size is[%d]",lnAcctId,nLatnId,m_setOfrInstId.size());
	int nRet = 0;
	//int iBeforSearch = m_stAcctBodyReq.VOfrInst.size();

	long lnOfrInstId=0;

	/*改为第一个用户提取，用于提高排序//获得账户级套餐实例
	nRet = GetOfrDetailInst(lnAcctId,"E1",nLatnId,lnLastServId);
	if ( nRet < 0 )
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"","Find detail info failed,lnAcctId[%ld]+[E1].",lnAcctId);
		return -1;
	}*/
		
	set<long>::iterator iterOfrinst = m_setOfrInstId.begin();
	for(;iterOfrinst!=m_setOfrInstId.end();iterOfrinst++)
	{
		lnOfrInstId = *iterOfrinst;
		/*nRet = GetOfrInstanceInfo(lnOfrInstId,nLatnId);
		if ( nRet < 0 )
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"","GetOfrInstanceInfo failed,lnOfrInstId[%ld].",lnOfrInstId);
			return -1;
		}*/
		//获取销售品实例的可选包，并将销售品实例保存
		pushOfrInstWithCombineGroup( m_mapOfrInstTmp[lnOfrInstId], nLatnId );

		if(m_nGetDetail != 0)
		{
			//获取套餐明细
			nRet = GetOfrInstDetailInfo(lnOfrInstId, nLatnId);			
			if ( nRet < 0 )
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"DCComboAdaptPrdInst::getAllAcctOfrfInstance","GetOfrInstDetailInfo Find detail info failed,lnOfrInstId[%ld].",lnOfrInstId);
				return -1;
			}
		}
		
    }
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","m_vOfrDetailDyn size is[%d],m_multimapOfrInstPri.size is[%d]",m_vOfrDetailDyn.size(),m_multimapOfrInstPri.size());

	//存入普选优惠资料	
	for(int i=0;i<m_vOfrDetailDyn.size();i++)
	{
		//m_stAcctBodyReq.VDetailInst.push_back(m_vOfrDetailDyn[i]);
		m_mmapOfrDetail.insert(make_pair(m_vOfrDetailDyn[i].lnOfrInstId,m_vOfrDetailDyn[i]));
	}
	
	// 对优惠实例排序
	//int iTmpCounts = m_stAcctBodyReq.VOfrInst.size();
	//if ( iTmpCounts != iBeforSearch )
	//{
	//	sort(m_stAcctBodyReq.VOfrInst.begin() + iBeforSearch, m_stAcctBodyReq.VOfrInst.end(),SORTOFRINST);
	//}
	map<ST_ShiftOfrInst,int>::iterator iter = m_multimapOfrInstPri.begin();
	ocs::OfrInst tmp;
	for(;iter!=m_multimapOfrInstPri.end();iter++)
	{
		tmp.lnOfrInstId = iter->first.lnOfrInstId;
		tmp.lnOfrId = iter->first.lnOfrId;
		tmp.lnSelectGroupID = iter->first.lnSelectGroupID;
		tmp.nCalcPriority = iter->first.nCalcPriority;
		tmp.nDynInstFlag = iter->first.nDynInstFlag;
		tmp.nEventTypeId = iter->first.nEventTypeId;
		tmp.nFeeType = iter->first.nFeeType;
		tmp.szEffDate = iter->first.szEffDate;
		tmp.szEventEffDate = iter->first.szEventEffDate;
		tmp.szEventExpDate = iter->first.szEventExpDate;
		tmp.szExpDate = iter->first.szExpDate;
		tmp.vGroupId = iter->first.vGroupId;
		//m_stAcctBodyReq.VOfrInst.push_back(tmp);
		m_vecOfrInstOutput.push_back(tmp);
	}

	DCBIZLOG(DCLOG_LEVEL_INFO,0,"DCComboAdaptPrdInst::getAllAcctOfrfInstance","AcctId[%ld] LatnId[%d] to find InstInfo VOfrInst.size()=%d VDetailInst.size()=%d",lnAcctId,nLatnId,m_vecOfrInstOutput.size(),m_mmapOfrDetail.size());
	DCDATLOG("RD00003:%d!%ld!%d!%d",nLatnId,lnAcctId,m_vecOfrInstOutput.size(),m_mmapOfrDetail.size());
	return 0;
}


//获取销售品实例信息，包含获取可选包ID，实例明细信息
int DCComboAdaptPrdInst::GetOfrInstanceInfo(long lnOfrInstId,int nLatnId,long& lnOfrId)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","DCComboAdaptPrdInst::GetOfrInstanceInfo begin,OfrInstId[%ld] LatnId[%d]",lnOfrInstId,nLatnId);

	//一个实例只取一次
	if(m_setOfrInstId.find(lnOfrInstId)!=m_setOfrInstId.end())
	{
		return 0;
	}
	//m_setOfrInstId.insert(lnOfrInstId);
		

	ST_ShiftOfrInst tmpDcOfrInst;
	char szRealEffDate[20] = {0};
	char szRealExpDate[20] = {0};
	char szEffDate[20] = {0};
	char szExpDate[20] = {0};
	char szModDate[16]={0};
	int nOfrStasId=0;
	bool bGetFail = true;
	string strsql;
	int nRet=0;
	int nFlag=0;
	
	 //费用归属天 
	char szTime[24] = {0};
	PublicLib::GetTime(szTime,YYYYMM);
	int nDay = 0;
	//如果系统账期和处理账期相同，则实例天为归属天，否则为32天
	if (atoi(szTime) == DCDataCenter::instance()->iBillingCycleId )
	{
		nDay = DCDataCenter::instance()->iEndDay;
	}
	else
	{
		nDay = 32;
	}
	char *sCycleBeginTime = DCDataCenter::instance()->sCycleBeginTime;
	char *sCycleEndTime = DCDataCenter::instance()->sCycleEndTime;

	try{
		
		UDBSQL*pQuery = m_pDbm->GetSQL("data_prd_ofr_inst");
		if(pQuery == NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCComboAdaptPrdInst::GetOfrInstanceInfo","not find sql[data_prd_ofr_inst]");
			return -1;
		}
		bGetFail = true;
	
		pQuery->UnBindParam();
		pQuery->BindParam(1,lnOfrInstId);
		pQuery->BindParam(2,nLatnId);
		pQuery->GetSqlString(strsql);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::GetOfrInstanceInfo","Do SQL: begin to do [data_prd_ofr_inst],sql :[%s] ",strsql.c_str());

	    pQuery->Execute();
	    while(pQuery->Next())
	    {	
			//tmpDcOfrInst.lnPrdInstId     = tPrdInst.lnProdInstId;
			tmpDcOfrInst.lnOfrInstId     = lnOfrInstId;
			pQuery->GetValue(1,lnOfrId );
			tmpDcOfrInst.lnOfrId = lnOfrId;			

			//过滤ofr_inst_id  DATA_PRD_FILETER_OFRINSTID
			if(filterOfrInstId(tmpDcOfrInst.lnOfrInstId,nLatnId))
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::GetOfrInstanceInfo","ofr ofr_inst_id [%ld] filter.",tmpDcOfrInst.lnOfrInstId);
				continue;
			}
		
			//pQuery->GetValue(2,tmpDcOfrInst.lnCustId );
			//tmpDcOfrInst.lnAcctId = lnAcctId;
			tmpDcOfrInst.lnSelectGroupID = tmpDcOfrInst.lnOfrInstId;
			tmpDcOfrInst.nFeeType = 7;
			tmpDcOfrInst.nEventTypeId    = 950;
			pQuery->GetValue(3,szEffDate );
			pQuery->GetValue(4,szExpDate );
			tmpDcOfrInst.szEffDate = szEffDate;
			tmpDcOfrInst.szExpDate = szEffDate;
			tmpDcOfrInst.szEventEffDate = szEffDate;
			tmpDcOfrInst.szEventExpDate = szExpDate;
			tmpDcOfrInst.nCalcPriority = getPriority(tmpDcOfrInst.lnOfrId,nLatnId); 
			
			//tmpDcOfrInst.nDay = nDay;
			//tmpDcOfrInst.nBelongDay = DCDataCenter::instance()->iEndDay;
			//tmpDcOfrInst.nBelongBillingCycleId = m_nBillingCycleId;

			//过滤失效记录
			pQuery->GetValue(5,szModDate );
			nOfrStasId = atoi(szModDate);
			pQuery->GetValue(6,szModDate );

			string strDefaultValue = "";
			nRet = QueryOfrExtAttr(lnOfrId,301,strDefaultValue);  //daily rent ofr
			if (nRet<0)
			{					
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "QueryOfrExtAttr failed,lnOfrId[%ld],attr_id[301]", lnOfrId);
				return -1;
			}	
			bool b7Cancel = false; //7 day unsubscribe without reason ofrinst
			if (m_cfgpara.SevenCancelSwitch)
			{
				std::string strParaValue;
				bool bFind = false;		
				long ln7CancelAttrId = 0;				
				std::string strParmGroup = "ACCT.CONFIG";
				std::string strParmKey = "7_CANCEL_OFRINST";				
				nRet = QuerySysParam(strParmGroup,strParmKey,strParaValue,bFind); 
				if (nRet<0)
				{					
					DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "QuerySysParam failed,lnOfrInstId[%ld]", lnOfrInstId);
					return -1;
				}
				if (bFind)
				{
					ln7CancelAttrId = atol(strParaValue.c_str());
					nRet = Query7CancelOfrInstAttr(lnOfrInstId,nLatnId,ln7CancelAttrId,b7Cancel);  
					if (nRet<0)
					{					
						DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "QueryOfrInstAttr failed,lnOfrInstId[%ld],attr_id[%ld]", lnOfrInstId,ln7CancelAttrId);
						return -1;
					}
				}
			}			
			

			//过滤失效记录
			//where ((eff_date <= '[CYCLE_END_DATE]' and exp_date >= '[CYCLE_END_DATE]') or (eff_date <= '[CYCLE_END_DATE]' and exp_date >= '[CYCLE_BEGIN_DATE]'  and  ofr_id>990000 and ofr_id <1000000   AND  eff_date<=exp_date))
			//and ((ofr_inst_stas_id = 1001) or (ofr_inst_stas_id in (1101,1201) and mod_date >= '[CYCLE_BEGIN_DATE]'))
			if ((strcmp(szEffDate,"") != 0) && (strcmp(szExpDate,"") != 0))
			{
				if (strncmp(sCycleBeginTime,szExpDate,8) > 0 || strncmp(sCycleEndTime,szEffDate,8) < 0)
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::GetOfrInstanceInfo","ofr_inst_id[%ld] filter by inst date.effdate[%s] expdate[%s]",lnOfrInstId,szEffDate,szExpDate);
					continue;
				}
				nFlag=0;
				if ((strncmp(szEffDate,sCycleEndTime,8) <= 0 && strncmp(szExpDate,sCycleEndTime,8) >= 0) ||
					(strncmp(szEffDate,sCycleEndTime,8) <= 0 && strncmp(szExpDate,sCycleBeginTime,8) >= 0 && ("1" == strDefaultValue || b7Cancel) && strncmp(szEffDate,szExpDate,8)<=0))
				{
					if (nOfrStasId==1000 || ((nOfrStasId==1100 || nOfrStasId==1097) && strncmp(szModDate,sCycleBeginTime,8)>=0))
						nFlag=1;
				}
				if(nFlag==0)
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::GetOfrInstanceInfo","ofr_inst_id[%ld] filter by inst other info.effdate[%s] expdate[%s] ofrid[%ld] ofrstasid[%d] moddate[%s]",lnOfrInstId,szEffDate,szExpDate,lnOfrId,nOfrStasId,szModDate);
					continue;
				}
			}
	     
            //如果有多条记录，则对记录的生失效日期进行延长，多条记录变成一条记录
			if (0x00 == szRealEffDate[0])
			{
				strcpy(szRealEffDate, szEffDate);
				strcpy(szRealExpDate, szExpDate);
			}
			else
			{
				if (0 == strncmp(szExpDate, szRealEffDate, 8))
				{
					strcpy(szRealEffDate, szEffDate);
				}
				else if (0 == strncmp(szRealExpDate, szEffDate, 8))
				{
					strcpy(szRealExpDate, szExpDate);
				}
			}
			bGetFail = false;
	    }
		pQuery->Close();
		
		if (bGetFail)
		{
			DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCComboAdaptPrdInst::GetOfrInstanceInfo","Get User OfrfInstance by OfrInstId failed,OfrInstId [%ld].",lnOfrInstId);
			DCDATLOG("RD00006:%ld",lnOfrInstId);
			return 1;
		}
		else
		{
			tmpDcOfrInst.szEffDate = szRealEffDate;
			tmpDcOfrInst.szExpDate = szRealExpDate;
			tmpDcOfrInst.nDynInstFlag = 0;//=1是普选优惠实例

			//获取销售品实例的可选包，并将销售品实例保存
			//本函数目前仅用于新装套餐获取明细判断账户//pushOfrInstWithCombineGroup( tmpDcOfrInst, nLatnId );

			//if(m_nGetDetail != 0)//因需判断跨账户，所以必须取明细
			//{
				//获取套餐明细
				nRet = GetOfrInstDetailInfo(lnOfrInstId, nLatnId);			
				if ( nRet < 0 )
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"DCComboAdaptPrdInst::GetOfrInstanceInfo","GetOfrInstDetailInfo Find detail info failed,lnOfrInstId[%ld].",lnOfrInstId);
					return -1;
				}
			//}
			
		}
	} 
	catch(std::exception& e)
	{		
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCComboAdaptPrdInst::GetOfrInstanceInfo","error info: [%s] sql[%s]",e.what(),strsql.c_str());
		return -1;
	}
	
	m_setOfrInstId.insert(lnOfrInstId);
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","DCComboAdaptPrdInst::GetOfrInstanceInfo end,OfrInstId[%ld] LatnId[%d]",lnOfrInstId,nLatnId);
	return 0;
}


//获取销售品实例明细信息，并保存
int DCComboAdaptPrdInst::GetOfrInstDetailInfo(long lnOfrInstId,int nLatnId)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::GetOfrInstDetailInfo begin,OfrInstId[%ld]", lnOfrInstId);

	string strsql;
    char szSqlName[64]={0};
	sprintf(szSqlName,"data_ofr_inst_detail_info");
    UDBSQL   * pSQL = m_pDbm->GetSQL(szSqlName);
    if(!pSQL)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCComboAdaptPrdInst::GetOfrInstDetailInfo", "Not find  SQL [%s] ",szSqlName);
        return -1;
    }
    
	char *pszCycleBeginTime = DCDataCenter::instance()->sCycleBeginTime;
	char *pszCycleEndTime = DCDataCenter::instance()->sCycleEndTime;

    char szValue[128] = {0};
	char szEffDate[24] = {0};
	char szExpDate[24] = {0};
	int nRecNum=0;
	ocs::DetailInst stDetailInst;
	set<PAIRDETAIL> setDetail;
	setDetail.clear();
    try
    {
		pSQL->UnBindParam();
		pSQL->BindParam(1,lnOfrInstId);
		pSQL->BindParam(2,nLatnId);
		pSQL->GetSqlString(strsql);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::GetOfrInstDetailInfo","Do SQL: begin to do [data_ofr_inst_detail_info],sql :[%s] ",strsql.c_str());
        pSQL->Execute();
        while(pSQL->Next())
        {
    		/*lnOfrInstId = atol(pSQL->GetValue(2));
			//同一ofr_inst_id只取一次明细
			if (m_setOfrInstId.find(lnOfrInstId) != m_setOfrInstId.end())
			{
				continue;
			}*/
			 
			stDetailInst.lnOfrInstId = lnOfrInstId;
    		
    		memset(szValue,0,sizeof(szValue) );
    		pSQL->GetValue(3, szValue);
    		stDetailInst.lnOfrDetailId  = atol(szValue);

    		memset(szValue,0,sizeof(szValue) );
    		pSQL->GetValue(6, szValue);
    		stDetailInst.lnOfrDetailInstId=atol(szValue);
            
    		memset(szValue,0,sizeof(szValue) );
    		pSQL->GetValue(5, szValue);
			stDetailInst.lnRefId = atol(szValue);
			
    		memset(szValue,0,sizeof(szValue) );
    		pSQL->GetValue(4, szValue);
			stDetailInst.szOfrInstRefType = szValue;

            pSQL->GetValue(8, szEffDate);
            pSQL->GetValue(9, szExpDate);
			//过滤失效记录
			if ((strcmp(szEffDate,"") != 0) && (strcmp(szExpDate,"") != 0))
			{
				if (0 == strncmp(szEffDate,szExpDate,8))
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::GetOfrInstDetailInfo","detail Filter by EffDate[%]=ExpDate[%s].",szEffDate,szExpDate);
					continue;
				}
				if (strncmp(pszCycleBeginTime,szExpDate,8) > 0 || strncmp(pszCycleEndTime,szEffDate,8) < 0)
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::GetOfrInstDetailInfo","detail Filter by EffDate[%s] or ExpDate[%s] out of this month[%d].",szEffDate,szExpDate,DCDataCenter::instance()->iBillingCycleId);
					continue;
				}
			}
            if(strncmp(szExpDate+8,"000000",6)==0)
            {szExpDate[8]='2';szExpDate[9]='3';szExpDate[10]='5';szExpDate[11]='9';szExpDate[12]='5';szExpDate[13]='9';szExpDate[14]='\0';}

			PAIRDETAIL pairDetail(stDetailInst.lnOfrDetailId,stDetailInst.lnRefId);
			set<PAIRDETAIL>::iterator itSetDetail = setDetail.find(pairDetail);
			if (setDetail.end() == itSetDetail)
			{
				setDetail.insert(pairDetail);

				nRecNum++;
				
				if(m_nGetDetail != 0)//发送明细
				{
					m_mmapOfrDetail.insert(make_pair(stDetailInst.lnOfrInstId,stDetailInst));
				}
				m_multimapOfrInstTmp.insert(make_pair(lnOfrInstId,stDetailInst));
				
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::GetOfrInstDetailInfo","getOfrDetailInst,Ofr Detail Inst record [OfrInstId:%ld OfrDetailId:%ld OfrDetailInstId:%ld RefId:%ld RefType:%s].",
					stDetailInst.lnOfrInstId, stDetailInst.lnOfrDetailId, stDetailInst.lnOfrDetailInstId, stDetailInst.lnRefId, (stDetailInst.szOfrInstRefType).c_str());
			}
        }
		pSQL->Close();
    }
    catch(UDBException& e)
    {
    	string strSQL;
        pSQL->GetSqlString(strSQL);
       	DCBIZLOG(DCLOG_LEVEL_ERROR,-e.GetErrorCode(),"DCComboAdaptPrdInst::GetOfrInstDetailInfo","execption[%s] sql[%s]",e.ToString(), strSQL.c_str());
		return -1;
	}
        	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::GetOfrInstDetailInfo end,OfrInstId[%ld],get %d record",lnOfrInstId,nRecNum);

	return 0;
}


//获取普选优惠套餐实例信息与明细信息
int DCComboAdaptPrdInst::GetDynOfrInst(long lnServId, int nLatnId )
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::GetDynOfrInst begin [PrdInstId=%ld]",lnServId);
	string strsql;
	try
	{
		char szbuf[30]={0};
		char szEffDate[16]={0};
		char szExpDate[16]={0};
		char szCatOfferType[4]={0};

		char *sCycleBeginTime = DCDataCenter::instance()->sCycleBeginTime;
		char *sCycleEndTime = DCDataCenter::instance()->sCycleEndTime;
		
		UDBSQL*pQuery = m_pDbm->GetSQL("QueryDynOfrInst");
		if(pQuery == NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCComboAdaptPrdInst::GetDynOfrInst","not find sql[QueryDynOfrInst]");
			return -1;
		}
		pQuery->UnBindParam();
		pQuery->BindParam(1,lnServId);
		//pQuery->BindParam(2,1);//cat_offer_type = '1' 时object_id=prd_inst_id（账务优惠）
		pQuery->BindParam(2,nLatnId);

		pQuery->GetSqlString(strsql);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::GetDynOfrInst","Do SQL: begin to do [QueryDynOfrInst],sql :[%s] ",strsql.c_str());

	    pQuery->Execute();
	    while(pQuery->Next())
	    {	
	    	pQuery->GetValue(10,szCatOfferType);
			if(strncmp(szCatOfferType,"1",1)!=0)
				continue;
		
	    	memset(szEffDate,0x00,sizeof(szEffDate));
	    	memset(szEffDate,0x00,sizeof(szExpDate));
	        pQuery->GetValue(6,szEffDate);	
	        pQuery->GetValue(7,szExpDate);
			//过滤失效记录
			if ((strcmp(szEffDate,"") != 0) && (strcmp(szExpDate,"") != 0))
			{
				if (0 == strncmp(szEffDate,szExpDate,8))
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::GetDynOfrInst","DynOfrInst Filter by EffDate[%]=ExpDate[%s].",szEffDate,szExpDate);
					continue;
				}
				if (strncmp(sCycleBeginTime,szExpDate,8) > 0 || strncmp(sCycleEndTime,szEffDate,8) < 0)
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::GetDynOfrInst","DynOfrInst Filter by EffDate[%s] or ExpDate[%s] out of this month[%d].",szEffDate,szExpDate,m_nBillingCycleId);
					continue;
				}
			}
			
	    	ocs::OfrInst tmpinst;
	    	ocs::DetailInst tmpdetail;
			
	        pQuery->GetValue(8,szbuf);//OFR_INST_ID
	        tmpinst.lnOfrInstId = atol(szbuf);
			tmpdetail.lnOfrInstId  = tmpinst.lnOfrInstId;
			
	        pQuery->GetValue(3,szbuf);//CALC_PRIORITY
			tmpinst.nCalcPriority = atoi(szbuf);

	        pQuery->GetValue(4,szbuf);//OFR_ID
	        tmpinst.lnOfrId = atol(szbuf);
			//tmpinst.nCalcPriority = getPriority(tmpinst.lnOfrId,nLatnId); 
	        pQuery->GetValue(5,szbuf);//OFR_DETAIL_ID
	        tmpdetail.lnOfrDetailId = atol(szbuf);

			tmpinst.lnSelectGroupID = tmpinst.lnOfrInstId;
			tmpinst.nEventTypeId = 950;
			tmpinst.nFeeType = 7;
			tmpinst.szEffDate = szEffDate;
			tmpinst.szExpDate = szExpDate;
			tmpinst.szEventEffDate = szEffDate;
			tmpinst.szEventExpDate = szExpDate;
			tmpinst.nDynInstFlag = 1;//=1是普选优惠实例
			
			tmpdetail.lnRefId = lnServId;
			tmpdetail.szOfrInstRefType = "B1";
	        
	    	//pQuery->GetValue(1,szbuf);//PRICING_PLAN_ID =-1
	        //pQuery->GetValue(2,szbuf);//OFR_TYPE_ID =4
	        //pQuery->GetValue(9,szbuf);//PRICING_COMBINE_GROUP_ID =0

			//m_vOfrInstDyn.push_back(tmpinst);
			ST_ShiftOfrInst tmp;
			tmp.lnOfrInstId = tmpinst.lnOfrInstId;
			tmp.lnOfrId = tmpinst.lnOfrId;
			tmp.lnSelectGroupID = tmpinst.lnSelectGroupID;
			tmp.nCalcPriority = tmpinst.nCalcPriority;
			tmp.nDynInstFlag = tmpinst.nDynInstFlag;
			tmp.nEventTypeId = tmpinst.nEventTypeId;
			tmp.nFeeType = tmpinst.nFeeType;
			tmp.szEffDate = tmpinst.szEffDate;
			tmp.szEventEffDate = tmpinst.szEventEffDate;
			tmp.szEventExpDate = tmpinst.szEventExpDate;
			tmp.szExpDate = tmpinst.szExpDate;
			tmp.vGroupId = tmpinst.vGroupId;
			tmp.lnPrdInstId = lnServId;
			
			m_multimapOfrInstPri.insert(make_pair(tmp,1));
			m_vOfrDetailDyn.push_back(tmpdetail);
			
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::GetDynOfrInst","Dyn Ofr Inst record [OfrInstId:%ld OfrId:%ld OfrDetailId:%ld CalcPriority:%d].",
					tmpinst.lnOfrInstId,tmpinst.lnOfrId, tmpdetail.lnOfrDetailId,tmpinst.nCalcPriority);
			
	    }
		pQuery->Close();
	} 
	catch(std::exception& e)
	{		
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCComboAdaptPrdInst::GetDynOfrInst","QueryDynOfrInst failed: [%s] sql[%s]",e.what(),strsql.c_str());
		return -1;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::GetDynOfrInst end");
	return 0;
}
int DCComboAdaptPrdInst::Query7CancelOfrInstAttr(long lnOfferInstId, int nLatnId, long lnAttrId, bool& bFind)
{	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","Query7CancelOfrInstAttr begin [lnOfferInstId=%ld,nLatnId=%d,lnAttrId=%ld]",lnOfferInstId,nLatnId,lnAttrId);
	string strsql;	
	bFind = false;
	UDBSQL*pQuery=NULL;		
	try
	{
		char szEffDate[16]={0};
		char szExpDate[16]={0};
		long lnQueryAttrId = 0;
		char szSqlname[64] = {0};
		sprintf(szSqlname, "QueryOfrInstAttr7Cancel");		
		char *sCycleEndDate = DCDataCenter::instance()->sCycleEndDate;			
		char *sNextCycleBeginDate = DCDataCenter::instance()->sNextCycleBeginDate;	
		pQuery = m_pDbm->GetSQL(szSqlname);
		if (NULL == pQuery)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,0,"","get query[%s] failed.",szSqlname);
			return -1;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","Get sql [%s] OK",szSqlname);
		pQuery->UnBindParam();
		pQuery->BindParam(1,lnOfferInstId);
		pQuery->BindParam(2,nLatnId);

		pQuery->GetSqlString(strsql);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","Do SQL: begin to do [%s],sql :[%s] ",szSqlname,strsql.c_str());

	    pQuery->Execute();
	    while(pQuery->Next())
	    {	
	    	pQuery->GetValue(2, lnQueryAttrId); //PROD_OFFER_ATTR_ID
	    	if (lnQueryAttrId != lnAttrId)
	    	{
				continue;
			}		
	    	memset(szEffDate,0x00,sizeof(szEffDate));
	    	memset(szEffDate,0x00,sizeof(szExpDate));
	        pQuery->GetValue(3,szEffDate);	
	        pQuery->GetValue(4,szExpDate);
			//过滤失效记录
			if (szEffDate[0] == '\0' || szExpDate[0] == '\0' || strncmp(szEffDate, sNextCycleBeginDate, 8) >= 0 || strncmp(szExpDate, sCycleEndDate, 6) < 0)
			{
				DCBIZLOG(DCLOG_LEVEL_WARN, 0, "", "Check Time out of date effdate[%s] NextCycleBeginDate[%s] expdate[%s] CycleEndDate[%s]", szEffDate, sNextCycleBeginDate, szExpDate, sCycleEndDate);
				continue;
			}			
			bFind = true; 	
			break;
			
	    }
		pQuery->Close();
	} 
	catch (UDBException &e)
	{
		string strSQL;
		pQuery->GetSqlString(strSQL);
		DCBIZLOG(DCLOG_LEVEL_ERROR, -e.GetErrorCode(), "", "SQL [%s],execption[%s]", strSQL.c_str(), e.ToString());
		return -1;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","Query7CancelOfrInstAttr end [lnOfferInstId=%ld,nLatnId=%d,lnAttrId=%ld],bFind[%d]",lnOfferInstId,nLatnId,lnAttrId,bFind);
	return 0;
}



//判断是否为特殊分流账户，包含特殊分流账户保存,nUpdateType=0全量更新流程，=1新装订购，=2新装退订,isNewOfrDealFlew为true时表示新装流程,false时为跨账户流程
int DCComboAdaptPrdInst::CheckSpecialCrossAcct(int nLatnId, long lnOfrInstId, bool isNewOfrDealFlew ,long &lnSpecialId,set<long> &setOfrInstAcct)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::CheckSpecialCrossAcct begin,lnOfrInstId[%ld],isNewOfrDealFlew[%d]",lnOfrInstId,isNewOfrDealFlew);
	string strsql;	
	int nRet = 0;
    set<long> tmpPrdInstId;//存储明细下所有用户再查主产品实例表
	tmpPrdInstId.clear();
	m_multimapAcctUser.clear();
	char *pszCycleBeginTime = DCDataCenter::instance()->sCycleBeginTime;
	char *pszCycleEndTime = DCDataCenter::instance()->sCycleEndTime;
	try
	{
		long lnPrdInstId=0,lnTmp=0,lnOtherAcctId=-99;
		char szBuf[32]={0};
		int nTmp2=0,nTmpLatnId=0;
		long lnOfrId=0,lnCustId = 0,lnProdId=0;
		char szStatusCd[32]={0},szInstallDate[16]={0},szBillDate[16]={0},szCreateDate[16]={0},szPaymentCd[16]={0},szOfrId[16]={0},szAccNum[50]={0};
		string szUserTypeId,szEffDate,szExpDate,szAreaCode;
		
		bool isTrue=false;
		STSpecialAcctKey sttmpInfo;

		setOfrInstAcct.clear();
		multimap<long,ocs::DetailInst>::iterator iterbng = m_multimapOfrInstTmp.lower_bound(lnOfrInstId);
		multimap<long,ocs::DetailInst>::iterator iterend = m_multimapOfrInstTmp.upper_bound(lnOfrInstId);
		for(;iterbng!=iterend;iterbng++)
		{
			if(iterbng->second.szOfrInstRefType == "A1" || iterbng->second.szOfrInstRefType == "J1")
			{
				lnPrdInstId = iterbng->second.lnRefId;               
				tmpPrdInstId.insert(lnPrdInstId);
			}
			else if(iterbng->second.szOfrInstRefType == "E1")
			{
				lnTmp = iterbng->second.lnRefId;
                lnOtherAcctId = lnTmp;
                if (setOfrInstAcct.find(lnOtherAcctId) == setOfrInstAcct.end())
                {
                    setOfrInstAcct.insert(lnOtherAcctId);
                }
				UDBSQL* pQuery = m_pDbm->GetSQL("info_acct");
				if(pQuery ==NULL)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCComboAdaptPrdInst::CheckSpecialCrossAcct","not find sql info_acct");
					return -1;
				}
				pQuery->UnBindParam();
				pQuery->BindParam(1,lnTmp);//ACCT_ID
				pQuery->BindParam(2,nLatnId);
				pQuery->GetSqlString(strsql);
				pQuery->Execute();
				DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCComboAdaptPrdInst::CheckSpecialCrossAcct","SQL[info_acct]:[%s]",strsql.c_str());  
				if(pQuery->Next())
				{
					pQuery->GetValue(1,lnPrdInstId);//ET_PRD_INST_ID                    
					tmpPrdInstId.insert(lnPrdInstId);
				}
				pQuery->Close();
			}
			else if(iterbng->second.szOfrInstRefType == "F1" || iterbng->second.szOfrInstRefType == "G1")
			{
				lnTmp = iterbng->second.lnRefId;
				UDBSQL* pQuery = m_pDbm->GetSQL("data_group");
				if(pQuery ==NULL)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","not find sql data_group");
					return -1;
				}
				pQuery->UnBindParam();
				pQuery->BindParam(1,lnTmp);//GROUP_USER_GROUP_ID
				pQuery->BindParam(2,nLatnId);
				pQuery->GetSqlString(strsql);
				pQuery->Execute();
				DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCComboAdaptPrdInst::CheckSpecialCrossAcct","GROUP_USER_GROUP_ID[%ld],SQL[data_group]:[%s]",lnTmp,strsql.c_str());  
				if(pQuery->Next())
				{
					pQuery->GetValue(3,nTmp2);//GROUP_USER_TYPE_ID
					
					UDBSQL* pQuery2 = m_pDbm->GetSQL("data_prd_grp_user_type");
					if(pQuery2 ==NULL)
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCComboAdaptPrdInst::CheckSpecialCrossAcct","not find sql data_prd_grp_user_type");
						return -1;
					}
					pQuery2->UnBindParam();
					pQuery2->BindParam(1,nTmp2);//GROUP_USER_TYPE_ID
					//pQuery2->BindParam(2,nLatnId);
					pQuery2->GetSqlString(strsql);
					pQuery2->Execute();
					DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCComboAdaptPrdInst::CheckSpecialCrossAcct","SQL[data_prd_grp_user_type]:[%s] GROUP_USER_TYPE_ID[%d]",strsql.c_str(),nTmp2);  
					while(pQuery2->Next())
					{
						pQuery2->GetValue(3,nTmpLatnId);
						if(nTmpLatnId!=888 && nTmpLatnId!=-1 && nLatnId!=nTmpLatnId)
						{
							DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","nTmpLatnId[%d] is invalid,nLatnId[%d]",nTmpLatnId,nLatnId); 
							continue;
						}
						
						memset(szBuf,0x00,sizeof(szBuf));
						pQuery2->GetValue(2,szBuf);//GROUP_USER_TYPE_CODE
						if (0 == strcmp(szBuf,"G98"))
						{
							UDBSQL* pQuery3 = m_pDbm->GetSQL("data_group_user");
							if(pQuery3 ==NULL)
							{
								DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCComboAdaptPrdInst::CheckSpecialCrossAcct","not find sql data_group_user");
								return -1;
							}
							pQuery3->UnBindParam();
							pQuery3->BindParam(1,lnTmp);//GROUP_USER_GROUP_ID
							pQuery3->BindParam(2,nLatnId);
							pQuery3->GetSqlString(strsql);
							pQuery3->Execute();
							DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCComboAdaptPrdInst::CheckSpecialCrossAcct","GROUP_USER_GROUP_ID[%ld],SQL[data_group_user]:[%s]",lnTmp,strsql.c_str());  
							if(pQuery3->Next())
							{
								pQuery3->GetValue(1,lnPrdInstId);//PRD_INST_ID
							}
							pQuery3->Close();                            
							tmpPrdInstId.insert(lnPrdInstId);
						}
						else if (0 == strcmp(szBuf,"G99"))
						{
							pQuery->GetValue(2,lnPrdInstId);//ET_PRD_INST_ID                            
							tmpPrdInstId.insert(lnPrdInstId);
						}
						break;
					}
					pQuery2->Close();
				}
				pQuery->Close();
			}
			else if(iterbng->second.szOfrInstRefType == "O1" || iterbng->second.szOfrInstRefType == "O2")//提取类型只为A1
			{
				char szEffDate[24] = {0};
				char szExpDate[24] = {0};
				strsql = "";
				string OfrInstRefType;//获取明细的类型
				long lnObjId = iterbng->second.lnRefId;//O1的obj_id为它所作用的套餐的offer_inst_id
				char sqlname[64] = {0};
				sprintf(sqlname, "data_ofr_inst_detail_info");
				//用offer_inst_id再去查一次offer_prod_inst_rel表
				UDBSQL *pQuery = m_pDbm->GetSQL(sqlname);
				if(NULL == pQuery)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCComboAdaptPrdInst::CheckSpecialCrossAcct","Not Find this sqlname[%s]", sqlname);
					return -1;
				}
				pQuery->UnBindParam();
				pQuery->BindParam(1, lnObjId);
				pQuery->BindParam(2, nLatnId);
				pQuery->GetSqlString(strsql);
				pQuery->Execute();
				DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCComboAdaptPrdInst::CheckSpecialCrossAcct","SQL[data_ofr_inst_detail_info]:[%s]",strsql.c_str());  

				while(pQuery->Next())
				{
            		pQuery->GetValue(8, szEffDate);
           	 		pQuery->GetValue(9, szExpDate);				
    				pQuery->GetValue(4, OfrInstRefType);
					
					if ((strcmp(szEffDate,"") != 0) && (strcmp(szExpDate,"") != 0))
					{
						if (0 == strncmp(szEffDate,szExpDate,8))
						{
							DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::CheckSpecialCrossAcct","detail Filter by EffDate[%]=ExpDate[%s].",szEffDate,szExpDate);
							continue;
						}
						if (strncmp(pszCycleBeginTime,szExpDate,8) > 0 || strncmp(pszCycleEndTime,szEffDate,8) < 0)
						{
							DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::CheckSpecialCrossAcct","detail Filter by EffDate[%s] or ExpDate[%s] out of this month[%d].",szEffDate,szExpDate,DCDataCenter::instance()->iBillingCycleId);
							continue;
						}
					}
					if(strncmp(szExpDate+8,"000000",6)==0)
					{szExpDate[8]='2';szExpDate[9]='3';szExpDate[10]='5';szExpDate[11]='9';szExpDate[12]='5';szExpDate[13]='9';szExpDate[14]='\0';}

					if(OfrInstRefType == "A1")
					{
						pQuery->GetValue(5, lnPrdInstId);
						tmpPrdInstId.insert(lnPrdInstId);
					}
					
				}
				pQuery->Close();
			}
        }
		//data_serv 得到acctid
		UDBSQL* pQueryPrd = m_pDbm->GetSQL("data_serv");
		if(pQueryPrd ==NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCComboAdaptPrdInst::CheckSpecialCrossAcct","not find sql data_serv");
			return -1;
		}
        
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "DCComboAdaptPrdInst::CheckSpecialCrossAcct", "ProdInstId size is %d", tmpPrdInstId.size());
		ocs::StRentMainInfo tmpPrdInfo;
        set<long>::iterator itrsetbeg = tmpPrdInstId.begin();
		for(; itrsetbeg != tmpPrdInstId.end(); itrsetbeg++)
        {
			pQueryPrd->UnBindParam();
            pQueryPrd->BindParam(1, *itrsetbeg);
			pQueryPrd->BindParam(2,nLatnId);
			pQueryPrd->GetSqlString(strsql);
			pQueryPrd->Execute();
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCComboAdaptPrdInst::CheckSpecialCrossAcct","SQL[data_serv]:[%s]",strsql.c_str());  
			while(pQueryPrd->Next())
			{
				pQueryPrd->GetValue(3,szAccNum);
				pQueryPrd->GetValue(5,szUserTypeId);
				pQueryPrd->GetValue(6,lnOfrId);//OFFER_ID@26
				pQueryPrd->GetValue(7,szEffDate);
				pQueryPrd->GetValue(8,szExpDate);
				pQueryPrd->GetValue(10,lnCustId);
				pQueryPrd->GetValue(11,szStatusCd);//STATUS_CD@8
				pQueryPrd->GetValue(12,szCreateDate);//INSTALL_DATE@27
				pQueryPrd->GetValue(14,szInstallDate);//INSTALL_DATE@27
				pQueryPrd->GetValue(16,szBillDate);//BILL_DATE@28
				pQueryPrd->GetValue(17,szPaymentCd);//PAYMENT_MODE_CD
				pQueryPrd->GetValue(18,lnProdId);
				pQueryPrd->GetValue(19,szAreaCode);		

				string strChargetatusCd;
				nRet = QueryChargeStatusCd(szStatusCd,strChargetatusCd);
				if (nRet < 0)
				{				
					DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","Curr PrdInstId[%ld] QueryChargeStatusCd filed",*itrsetbeg);
					return -1;
				}
				strncpy(szStatusCd,strChargetatusCd.c_str(),sizeof(szStatusCd)-1);
				szStatusCd[sizeof(szStatusCd)-1] = '\0';
				
				 /*
				 ((PRD_INST_STAS_ID not in (1105,1106,130000,150000,110000,1203,1204,1301)) 
							OR (   (PRD_INST_STAS_ID =1101 or PRD_INST_STAS_ID=1204 or PRD_INST_STAS_ID=1203 or PRD_INST_STAS_ID=1301) and bill_date >= '[CYCLE_BEGIN_DATE]')	 ) 
				 and  install_date <= '[CYCLE_END_DATE]' on  idx_tb_prd_prd_inst_[LATN_ID](prd_inst_id=?)
				*/
				if(szInstallDate[0]!='\0')
				{
					szInstallDate[8]='\0';	
					if(strncmp(szInstallDate,DCDataCenter::instance()->sCycleEndDate,8)>0 )
					{					
					    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","szInstallDate[%s] is bigger than sCycleEndDate[%s]",szInstallDate,DCDataCenter::instance()->sCycleEndDate); 
						continue;	
					}
				}
				if(szBillDate[0]=='\0')
				{
					strcpy(szBillDate,"20990101235959");			
				}
				if( !InCompare(szStatusCd,"130000,150000,110000") || ( strcmp(szStatusCd,"110000")==0 && strncmp(szBillDate,DCDataCenter::instance()->sCycleBeginDate,8)>=0)   )
				{
					isTrue=true;
				}
				if(!isTrue)
				{				
				    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","szStatusCd[%s] is invalid,szBillDate[%s],sCycleBeginDate[%s]",szStatusCd,szBillDate,DCDataCenter::instance()->sCycleBeginDate); 
					continue;
				}
		
				pQueryPrd->GetValue(1,lnOtherAcctId);//ET_ACCT_ID

				if (setOfrInstAcct.find(lnOtherAcctId) == setOfrInstAcct.end())
				{
					setOfrInstAcct.insert(lnOtherAcctId);
                    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "DCComboAdaptPrdInst::CheckSpecialCrossAcct", "Add to setOfrInstAcct (%d)", setOfrInstAcct.size());
				}	
				tmpPrdInfo.lnProdInstId = *itrsetbeg;
				tmpPrdInfo.lnCustId = lnCustId;
				tmpPrdInfo.lnOfferId = lnOfrId;
				tmpPrdInfo.lnProdId = lnProdId;
				tmpPrdInfo.szStatusCd = szStatusCd;
				tmpPrdInfo.szCreateDate = szCreateDate;
				tmpPrdInfo.szInstallDate = szInstallDate;
				tmpPrdInfo.szFirstFinishDate = szBillDate;				
				tmpPrdInfo.szEffDate = szEffDate;
				tmpPrdInfo.szExpDate = szExpDate;
				tmpPrdInfo.szAreaCode = szAreaCode;				
				tmpPrdInfo.szAccNum = szAccNum;
				tmpPrdInfo.szUserTypeId = szUserTypeId;
                tmpPrdInfo.nBelongDay = atoi(szPaymentCd);//暂时用于保存用户预后属性，ComboAdapt后会更新为正确值
				m_multimapAcctUser.insert(make_pair(lnOtherAcctId,tmpPrdInfo));				
				
				break;//主产品实例表只取一个有效的acctid
			}
			pQueryPrd->Close();
		}
		
        int nAcctType = -2;
        bool bIn = false;
		//保存跨账户
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "DCComboAdaptPrdInst::CheckSpecialCrossAcct", "Check setOfrInstAcct (%d)", setOfrInstAcct.size());
		if(setOfrInstAcct.size()>1)
		{
            bIn = true;
			//新装流程如果全部存在就不用保存中间表了
			if(isNewOfrDealFlew)
			{
				if(CheckNewCrossOfrInst(setOfrInstAcct ,lnOfrInstId ,lnSpecialId)==false)//没有新装套餐
				{				
				    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","CheckNewCrossOfrInst false,nAcctType is[%d]",SPECIAL_ACCT_INST); 
					return SPECIAL_ACCT_INST;
				}
			}
			if( SaveMiddleCrossAcct(setOfrInstAcct ,lnOfrInstId ,nLatnId)<0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCComboAdaptPrdInst::CheckSpecialCrossAcct","SaveMiddleCrossAcct failed, OfrInstId[%ld].",lnOfrInstId);
				//return -1;
			}
			
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","nAcctType is[%d]",SPECIAL_ACCT_WAIT); 
            nAcctType = SPECIAL_ACCT_WAIT;
		}
		else if(setOfrInstAcct.size()==1)
		{
			map<long,STSpecialAcctInfo>::iterator iterfind = m_mapSpecialAcctType.find(lnOtherAcctId);
			if(iterfind!=m_mapSpecialAcctType.end())
			{
				if(iterfind->second.nGroupId == 2)
				{
					lnSpecialId = iterfind->second.lnModGroupId;
					nAcctType = SPECIAL_ACCT_INST;
				}
				else if(iterfind->second.nGroupId == 3)
				{
					lnSpecialId = lnOtherAcctId;
					nAcctType = SPECIAL_ACCT_MOST;
				}
				else
				{
					lnSpecialId = lnOtherAcctId;
					nAcctType = SPECIAL_ACCT_BIG;
				}
			}
			else
			{
				lnSpecialId = lnOtherAcctId;
				nAcctType = SPECIAL_ACCT_COMM;
			}			
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","nAcctType is[%d]",nAcctType); 
        }

        if (setOfrInstAcct.size() > 0)
        {
            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "DCComboAdaptPrdInst::CheckSpecialCrossAcct", "ofrinstid[%ld] perfect mod group info", lnOfrInstId);
            sttmpInfo.lnAcctId = *(setOfrInstAcct.begin());
            sttmpInfo.lnOfrInstId = lnOfrInstId; // 同一个套餐，Mod_group_id一样
            sttmpInfo.nGroupId = 2;
            map<STSpecialAcctKey, long>::iterator itermap = m_mapSpecialAcctId.find(sttmpInfo);
            if (itermap != m_mapSpecialAcctId.end())
            { //删除无效数据避免分组太大
                DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "DCComboAdaptPrdInst::CheckSpecialCrossAcct", "find ofrinstid[%ld]", lnOfrInstId);
                long lnModGroupId = itermap->second;
                vector<STSpecialAcctKey> stDelList; // 需要删除的信息
                // 按mod_group_id来判断，是否需要拆分组；按账户与套餐进行分组，同一个账户在同一个组，相同套餐也在同一个组
                vector<STCrossKey> stModList;
                for (map<STSpecialAcctKey, long>::iterator itMap = m_mapSpecialAcctId.begin(); itMap != m_mapSpecialAcctId.end(); ++itMap)
                {
                    if (itMap->first.nGroupId != 2 || itMap->second != lnModGroupId)
                    {
                        continue;
                    }
                    const STSpecialAcctKey &stInfo = itMap->first;
                    // 如果该套餐只有一个账户了，则需要删除该组下该套餐所有账户
                    // 如果该套餐不只一个账户，则需要删除该组下该套餐多余的账户
                    if (stInfo.lnOfrInstId == lnOfrInstId && (!bIn || (bIn && setOfrInstAcct.find(stInfo.lnAcctId) == setOfrInstAcct.end())))
                    {
                        sttmpInfo.lnAcctId = stInfo.lnAcctId;
                        sttmpInfo.lnOfrInstId = lnOfrInstId;
                        sttmpInfo.nGroupId = 2;
                        stDelList.push_back(sttmpInfo);
                        continue;
                    }
                    // 还要考虑交叉合并的问题，如
                    // 账户A订购套餐1，账户B订购套餐2，账户C订购套餐1和2，要合并为一个组
                    bool bFind = false;
                    int first = -1, pos = 0; // 首次在vector中出现的位置
                    for (vector<STCrossKey>::iterator it = stModList.begin(); it != stModList.end();)
                    {
                        if (it->stOfrInstList.find(stInfo.lnOfrInstId) != it->stOfrInstList.end() ||
                            it->stAcctIdList.find(stInfo.lnAcctId) != it->stAcctIdList.end())
                        {
                            bFind = true;
                            if (first == -1) // 第一次找到才插入，后面找到直接合并
                            {
                                it->stOfrInstList.insert(stInfo.lnOfrInstId);
                                it->stAcctIdList.insert(stInfo.lnAcctId);
                                it->stCrossAcctList.push_back(stInfo);
                                first = pos;
                                ++it;
                            }
                            else
                            {
                                stModList[first] += *it;
                                it = stModList.erase(it);
                            }
                        }
                        else
                        {
                            ++it;
                            ++pos;
                        }
                    }
                    if (!bFind)
                    {
                        STCrossKey stKey;
                        stKey.stOfrInstList.insert(stInfo.lnOfrInstId);
                        stKey.stAcctIdList.insert(stInfo.lnAcctId);
                        stKey.stCrossAcctList.push_back(stInfo);
                        stModList.push_back(stKey);
                    }
                }
                DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "DCComboAdaptPrdInst::CheckSpecialCrossAcct", "delete mod group size [%u]", stDelList.size());
                for (int i = 0; i < stDelList.size(); ++i)
                {
                    if (DeleteInvalidCrossAcct(stDelList[i], nLatnId) == 0)
                        m_mapSpecialAcctId.erase(stDelList[i]);
                }
                DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "DCComboAdaptPrdInst::CheckSpecialCrossAcct", "Split mod group size [%u]", stModList.size());
                // 如果一个组中只有一个账户，需要删除；有多个组时，需要生成新的mod_group_id
                bool bNewModGroupId = false; // 是否切换新的mod_group_id
                for (int i = 0; i < stModList.size(); ++i)
                {
                    const STCrossKey &stInfo = stModList[i];
                    if (stInfo.stAcctIdList.size() < 2) // 只有一个账户情况下，就不是跨账户了，需要删除
                    {
                        for (vector<STSpecialAcctKey>::const_iterator it = stInfo.stCrossAcctList.begin();
                                it != stInfo.stCrossAcctList.end(); ++it)
                        {
                            DeleteInvalidCrossAcct(*it, nLatnId);
                            m_mapSpecialAcctId.erase(*it);
                        }
                        continue;
                    }
                    if (!bNewModGroupId)
                    {
                        bNewModGroupId = true;
                        continue;
                    }
                    string sbuf;
                    try
                    {
                        long lnNewModGroupId = 0; //获取新的mod_group_id
                        char szSqlname[64];
                        sprintf(szSqlname, "GetSpecialAcctSeq|%d", nLatnId);
                        UDBSQL *pGetSeq = m_pDbm->GetSQL(szSqlname);
                        if (pGetSeq == NULL)
                        {
                            DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "GetSQL [%s] failed", szSqlname);
                            return -1;
                        }
                        pGetSeq->UnBindParam();
                        pGetSeq->GetSqlString(sbuf);
                        pGetSeq->Execute();
                        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "DCComboAdaptPrdInst::CheckSpecialCrossAcct", "Do SQL:Begin to do GetSpecialAcctSeq,sql :[%s]", sbuf.c_str());
                        if (pGetSeq->Next())
                        {
                            pGetSeq->GetValue(1, lnNewModGroupId);
                        }
                        else
                        {
                            DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "DCComboAdaptPrdInst::CheckSpecialCrossAcct", "Do SQL:[%s] get new mod_group_id failed!", sbuf.c_str());
                            pGetSeq->Close();
                            return -1;
                        }
                        pGetSeq->Close();
                        // 更新new_mod_group_id
                        sprintf(szSqlname, "UpdateCrossAcctModGroupId|%d", nLatnId);
                        UDBSQL *pUptMod = m_pDbm->GetSQL(szSqlname);
                        if (pUptMod == NULL)
                        {
                            DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "GetSQL [%s] failed", szSqlname);
                            return -1;
                        }
                        for (vector<STSpecialAcctKey>::const_iterator it = stInfo.stCrossAcctList.begin();
                                it != stInfo.stCrossAcctList.end(); ++it)
                        {
                            pUptMod->UnBindParam();
                            pUptMod->GetSqlString(sbuf);
                            pUptMod->BindParam(1, lnNewModGroupId);
                            pUptMod->BindParam(2, lnModGroupId);
                            pUptMod->BindParam(3, 2);
                            pUptMod->BindParam(4, it->lnAcctId);
                            pUptMod->BindParam(5, it->lnOfrInstId);
                            pUptMod->Execute();
							pUptMod->Connection()->Commit();
                            m_mapSpecialAcctId[*it] = lnNewModGroupId; // 更新缓存
                            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "DCComboAdaptPrdInst::CheckSpecialCrossAcct", "Update new mod_group_id[%ld], ori mod_group_id[%ld], acct_id[%ld], ofrinstid[%ld]",
                                        lnNewModGroupId, lnModGroupId, it->lnAcctId, it->lnOfrInstId);
                        }
                        pUptMod->Close();
                    }
                    catch (UDBException &e)
                    {
                        DCBIZLOG(DCLOG_LEVEL_ERROR, -e.GetErrorCode(), "DCComboAdaptPrdInst::CheckSpecialCrossAcct", "sql[%s] execption[%s]", sbuf.c_str(), e.ToString());
                        return -1;
                    }
                } // end for stModList
            }
            else
            {
                DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "DCComboAdaptPrdInst::CheckSpecialCrossAcct", "not find ofrinstid[%ld]", lnOfrInstId);
            }
            DCBIZLOG(DCLOG_LEVEL_INFO, 0, "", "DCComboAdaptPrdInst::CheckSpecialCrossAcct end! OfrInstId[%ld] return %d", lnOfrInstId, nAcctType);
            return nAcctType;
        }
    
    }
    catch (UDBException &e)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, -e.GetErrorCode(), "DCComboAdaptPrdInst::CheckSpecialCrossAcct", "execption[%s] sql[%s]", e.ToString(), strsql.c_str());
        return -1;
    }
    catch (...)
    {
        return -1;
    }
	DCBIZLOG(DCLOG_LEVEL_INFO, 0,"","OfrInstId[%ld] is no valid attribution account",lnOfrInstId);	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::CheckSpecialCrossAcct end,lnOfrInstId[%ld],isNewOfrDealFlew[%d]",lnOfrInstId,isNewOfrDealFlew);
	return -2;//setOfrInstAcct的size为0
}

//加载TB_BIL_CROSS_ACCOUNT_[@]表中查出的大账户、超大账户、跨账户记录
int DCComboAdaptPrdInst::LoadSpecialAcct(int nLatnId)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::LoadSpecialAcct begin,m_nProType[%d]",m_nProType);

	if(PROC_TYPE_IMMED == m_nProType)
	{
		return 0;
	}

	string sbuf;
	try{
		
		char szSqlname[64]={0};
		int nQueryGroupId=0;
		long lnAcctId=0,lnOfrInstId=0,lnModGroupId=0;
		STSpecialAcctKey stKey;
		STSpecialAcctInfo stInfo;

		m_mapSpecialAcctId.clear();
        m_mapSpecialAcctType.clear();
		m_mapModGroupAcct.clear();

		sprintf(szSqlname,"LoadSpecialAcct|%d",nLatnId);
	    UDBSQL* pQuery = m_pDbm->GetSQL(szSqlname);			
		if(pQuery==NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "DCComboAdaptPrdInst::LoadSpecialAcct","GetSQL [%s] failed",szSqlname);
			return -1;
		} 
	    pQuery->UnBindParam();
		pQuery->GetSqlString(sbuf);
		pQuery->Execute();
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::LoadSpecialAcct","Do SQL:Begin to do LoadSpecialAcct,sql :[%s]",sbuf.c_str());	
	    while(pQuery->Next())
	    {
	    	pQuery->GetValue(1,lnAcctId);
	    	pQuery->GetValue(2,nQueryGroupId);
	    	pQuery->GetValue(3,lnOfrInstId);
	    	pQuery->GetValue(4,lnModGroupId);
			
			stKey.lnAcctId = lnAcctId;
			stKey.nGroupId = nQueryGroupId;
			stKey.lnOfrInstId = lnOfrInstId;
			
			stInfo.lnAcctId = lnAcctId;
			stInfo.nGroupId = nQueryGroupId;
			stInfo.lnOfrInstId = lnOfrInstId;
			stInfo.lnModGroupId = lnModGroupId;
			
            DCBIZLOG(DCLOG_LEVEL_TRACE, 0, "", "DCComboAdaptPrdInst::LoadSpecialAcct,Get SpecialAcct, AcctId:[%ld],GroupId:[%d],OfrInstId:[%ld],ModGroupId:[%ld]. ", lnAcctId, nQueryGroupId, lnOfrInstId, lnModGroupId);
			m_mapSpecialAcctId.insert(make_pair(stKey,lnModGroupId));
			m_mapSpecialAcctType.insert(make_pair(lnAcctId,stInfo));//因为跨账户时acctid可能有多条，这里仅能保存一条，专用于获取账户分流信息
			if (GROUP_CROSS == nQueryGroupId)
			{
				std::map<long,set<long> >::iterator itermap = m_mapModGroupAcct.find(lnModGroupId);
			    if(itermap!=m_mapModGroupAcct.end())
			    {
					itermap->second.insert(lnAcctId);	
				}
				else
				{
					set<long> setAcctId;
					setAcctId.insert(lnAcctId);
					m_mapModGroupAcct.insert(make_pair(lnModGroupId,setAcctId));
				}
			}	
	    }
		pQuery->Close();
	} 
	catch(UDBException& e)
    {
       	DCBIZLOG(DCLOG_LEVEL_ERROR,-e.GetErrorCode(),"DCComboAdaptPrdInst::LoadSpecialAcct","sql[%s] execption[%s]",sbuf.c_str(),e.ToString());
		return -1;
	}
    catch (...)
    {
        return -1;
    }
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::LoadSpecialAcct end,m_mapSpecialAcctId.size[%d],m_mapSpecialAcctType.size[%d]",m_mapSpecialAcctId.size(),m_mapSpecialAcctType.size());
	return 0;
}

//判断vi_pszLeft是否在vi_pszRight中,是返回true,否则返回false
bool DCComboAdaptPrdInst::InCompare(const char* vi_pszLeft, const char* vi_pszRight)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::InCompare begin,vi_pszLeft[%s],vi_pszRight[%s]",vi_pszLeft,vi_pszRight);

    char  m_szTmpLeft[1024]={0};
    char  m_szTmpRight[1024]={0};
	if(strlen(vi_pszLeft)<=0)
	{
		if(strlen(vi_pszRight)<=0)
		{
			return true;
		}
		return false;
	}

	//首先判断分隔符类型
	char sp = ',';
	int c = 0;
	while('\0' != *(vi_pszRight+c) )
	{
		if(','==*(vi_pszRight+c))
		{
			sp = ',';
			break;
		}	
		else if('|'==*(vi_pszRight+c))
		{
			sp = '|';
			break;
		}
		else if(';'==*(vi_pszRight+c))
		{
			sp = ';';
			break;
		}
		else if('#'==*(vi_pszRight+c))
		{
			sp = '#';
			break;
		}
		else if('@'==*(vi_pszRight+c))
		{
			sp = '@';
			break;
		}		
		++c;
	}

    //构造 ",A," 格式
    if (vi_pszLeft[0] != sp)
    {
        sprintf(m_szTmpLeft,"%c%s",sp,vi_pszLeft);
    }
    else
    {
        strcpy(m_szTmpLeft,vi_pszLeft);
    }

    int nLen = strlen(m_szTmpLeft);
    if (m_szTmpLeft[nLen-1] != sp)
    {
        m_szTmpLeft[nLen] = sp;
        m_szTmpLeft[nLen+1] = '\0';
    }

    //构造 ",B1,B2,B3," 格式
    if (vi_pszRight[0] != sp)
    {
        sprintf(m_szTmpRight,"%c%s",sp,vi_pszRight);
    }
    else
    {
        strcpy(m_szTmpRight,vi_pszRight);
    }

    nLen = strlen(m_szTmpRight);
    if (m_szTmpRight[nLen-1] != sp)
    {
        m_szTmpRight[nLen] = sp;
        m_szTmpRight[nLen+1] = '\0';
    }

    //如果A在B中，判断
    char* pstr = strstr(m_szTmpRight, m_szTmpLeft);
    if (NULL != pstr)
    {
        return true; 
    }
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::InCompare end,vi_pszLeft[%s],vi_pszRight[%s]",vi_pszLeft,vi_pszRight);

    return false;
}



//判断是否为特殊分流账户(大账户或者超大账户)，判断并保存大账户
int DCComboAdaptPrdInst::CheckSpecialBigAcct(long lnAcctId, int nLatnId, int nCurAcctPrdCount,long &lnSpecialId)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::CheckSpecialBigAcct begin ");
	char szBuf[32]={0};
	string strAcctList;
	string strsql;
	try
	{
		//先判断是否有跨账户或大套餐
		STSpecialAcctKey sttmpInfo;
		sttmpInfo.lnAcctId = lnAcctId;
		sttmpInfo.lnOfrInstId = -99;
		sttmpInfo.nGroupId = 3;
		map<STSpecialAcctKey,long>::iterator itermap = m_mapSpecialAcctId.find(sttmpInfo);
		if(itermap!=m_mapSpecialAcctId.end())
		{
            if (nCurAcctPrdCount >= m_nMostBigAcctThreshold)
            {
                lnSpecialId = lnAcctId; //超大账户返回AcctId
				return SPECIAL_ACCT_MOST;
			}
        }
		
		sttmpInfo.lnAcctId = lnAcctId;
		sttmpInfo.lnOfrInstId = -99;
		sttmpInfo.nGroupId = 1;
		itermap = m_mapSpecialAcctId.find(sttmpInfo);
		if(itermap!=m_mapSpecialAcctId.end())
		{
            //可能表里已经有了，但这次是超大账户过来
            if (nCurAcctPrdCount >= m_nBigAcctThreshold && nCurAcctPrdCount < m_nMostBigAcctThreshold)
            {
				lnSpecialId = lnAcctId;//大账户返回AcctId
				return SPECIAL_ACCT_BIG;
			}
        }
		
		//是否为大账户，本账户的用户数与配置数比较
		lnSpecialId = lnAcctId;
		if(nCurAcctPrdCount >= m_nMostBigAcctThreshold)
		{
			//写入分流表
			if( SaveSpecialBigAcct(lnAcctId ,3,nLatnId)<0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCComboAdaptPrdInst::CheckSpecialBigAcct","SaveSpecialAcct failed,AcctId[%ld].",lnAcctId);
				return -1;
			}
			lnSpecialId = lnAcctId;//大账户返回AcctId
			return SPECIAL_ACCT_MOST;
		}
		else if(nCurAcctPrdCount >= m_nBigAcctThreshold)
		{
			//写入分流表
			if( SaveSpecialBigAcct(lnAcctId ,1,nLatnId)<0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCComboAdaptPrdInst::CheckSpecialBigAcct","SaveSpecialAcct failed,AcctId[%ld].",lnAcctId);
				return -1;
			}
			lnSpecialId = lnAcctId;//大账户返回AcctId
			return SPECIAL_ACCT_BIG;
		}
	} 
	catch(UDBException& e)
    {
       	DCBIZLOG(DCLOG_LEVEL_ERROR,-e.GetErrorCode(),"DCComboAdaptPrdInst::CheckSpecialBigAcct","execption[%s] sql[%s]",e.ToString(),strsql.c_str());
		return -1;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::CheckSpecialBigAcct end ");
	return SPECIAL_ACCT_COMM;
}

//根据账户获取用户，用于新装提取，如果setFilterPrd非空就只查找setFilterPrd中的用户套餐，否则全找
bool DCComboAdaptPrdInst::LoadUserByAcctList(set<long> setAcctList,int nLatnId,long lnSpecialAcctId,int nAcctType,set<long> setFilterPrd)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::LoadUserByAcctList begin,lnSpecialAcctId[%ld],nAcctType[%d],setAcctList.size[%d]",lnSpecialAcctId,nAcctType,setAcctList.size());

	long lnAcctId=0;
	char buf[32];
	char szEffDate[16] = {0};	
	char szExpDate[16] = {0};	
	long lnLastPrdInstId=-99,lnCurPrdInstId=-99,lnCounts=0;
	ocs::StRentMainInfo tmpPrdInfo;
    multimap<long,ocs::StRentMainInfo> oneAcctUser;//如果缓存没有账户的资料需要查询下数据库，因为query840和loadcrossacct的取模不一样
	int nRet = 0;
	try
	{
		set<long>::iterator iteracct = setAcctList.begin();
		for(;iteracct!=setAcctList.end();iteracct++)
		{
			lnAcctId = *iteracct;
			bool bIsImmed = false;
			nRet = JudgeImmedAcct(lnAcctId,nLatnId,bIsImmed);
			if (nRet < 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","JudgeImmedAcct failed,lnAcctId[%ld],nLatnId[%d]",lnAcctId,nLatnId);
				return -1;
			}
			if (bIsImmed)
			{				
			    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","lnAcctId[%ld] is immed acct,not need to do",lnAcctId);
				continue;
			}			
			
			if(!setFilterPrd.empty())//过户和费用缓存触发流程发送messagesource=1的消息不需要查询E1套餐
			{
				m_isGetAcctOfrInst = true;
			}
			else
			{
				m_isGetAcctOfrInst = false;
			}			
			
			multimap<long,ocs::StRentMainInfo>::iterator iterprdbeg = m_multimapAcctUser.lower_bound(lnAcctId);
			multimap<long,ocs::StRentMainInfo>::iterator iterprdend = m_multimapAcctUser.upper_bound(lnAcctId);
            if(iterprdbeg==iterprdend)
            {
                oneAcctUser.clear();
                if(loadOneAcctUser(nLatnId,lnAcctId,oneAcctUser)<0)
                {
                    DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","loadOneAcctUser filed AcctId[%ld]",lnAcctId);
					releaseUserMap(true); //清空数据				
					return false;
                }
                iterprdbeg = oneAcctUser.lower_bound(lnAcctId);
			    iterprdend = oneAcctUser.upper_bound(lnAcctId);
            }
            for(;iterprdbeg!=iterprdend;iterprdbeg++)
			{
				tmpPrdInfo = iterprdbeg->second;
				//切换一个用户清空一次m_vecOfrInstTmp，因为已经存入m_stAcctBodyReq.VOfrInst
				releaseUserMap(false);		
				lnCounts++;
				if(!setFilterPrd.empty())//为过户和费用缓存触发流程做用户过滤
				{
					if(setFilterPrd.find(tmpPrdInfo.lnProdInstId)==setFilterPrd.end())
					{
						continue;
					}
				}
                if(m_nProType == PROC_TYPE_PRE_NORMAL && tmpPrdInfo.nBelongDay != 2100)//在这里nBelongDay保存的是用户预后属性，ComboAdapt后会更新为正确值
                {
                    continue;
                }
                else if(m_nProType == PROC_TYPE_POST_NORAML && tmpPrdInfo.nBelongDay == 2100)//在这里nBelongDay保存的是用户预后属性，ComboAdapt后会更新为正确值
                {
                    continue;
                }
				lnCurPrdInstId = tmpPrdInfo.lnProdInstId;
				tmpPrdInfo.lnOfrInstId = -2;
				tmpPrdInfo.lnGroupUserGroupId = tmpPrdInfo.lnProdInstId;

				if(ComboAdapt(tmpPrdInfo, nLatnId)<0)
	            {
				    DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCComboAdaptPrdInst::LoadUserByAcctList","Curr PrdInstId[%ld] AcctId[%ld] ComboAdapt filed",lnCurPrdInstId,lnAcctId);
					releaseUserMap(true); //清空数据				
					return false;
	            }				
				lnLastPrdInstId = lnCurPrdInstId;	
				
			}
			
		}

		if(m_nContralGetOfrInst==1)//获取套餐实例开关
		{
			if( getAllAcctOfrfInstance(lnSpecialAcctId ,nLatnId,lnLastPrdInstId)<0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCComboAdaptPrdInst::LoadUserByAcctList","getAllAcctOfrfInstance failed,AcctId/CrossModGroupId[%ld].",lnSpecialAcctId);
				return -1;
			}
		}
	
	} 
	catch(std::exception& e)
	{		
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCComboAdaptPrdInst::LoadUserByAcctList","LoadUserByAcctList failed: [%s] sql[%s]",e.what());
	}
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::LoadUserByAcctList end,lnSpecialAcctId[%ld],nAcctType[%d],setAcctList.size[%d]",lnSpecialAcctId,nAcctType,setAcctList.size());
	return true;
}

int DCComboAdaptPrdInst::JudgeImmedAcct(long lnAcctId,int nLatnId,bool& bIsImmed)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","JudgeImmedAcct begin,lnAcctId[%ld],nLatnId[%d],m_nProType[%d]",lnAcctId,nLatnId,m_nProType);

	if(PROC_TYPE_IMMED == m_nProType)
	{
		return 0;
	}
	int nBillingCycleId = DCDataCenter::instance()->iBillingCycleId;

    string strSql;
	int nCount = -1,nRet = 0;
	char szSqlname[64] = {0};	
	sprintf(szSqlname, "JudgeImmedAcct|%d",nLatnId);
	UDBSQL *pQuery = NULL;
	pQuery = m_pDbm->GetSQL(szSqlname);	
	if(NULL == pQuery)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","not find sql[%s]",szSqlname);
		return FAIL_NOT_FIND_SQLNAME;
	}
	
	while(nCount < NUMBER_OF_CYCLES)
	{
		nRet = 0;
		try
		{
			pQuery->UnBindParam();
			pQuery->BindParam(1,lnAcctId);
			pQuery->BindParam(2,to_string(nBillingCycleId));
			pQuery->GetSqlString(strSql);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","Do SQL: begin to do [%s],sql :[%s] ",szSqlname,strSql.c_str());	

			pQuery->Execute();
			while( pQuery->Next() )
			{
				bIsImmed = true;
				break;
			}
		}
		catch (UDBException &e)
		{
			std::string sql;
			pQuery->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "sql[%s]",sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "[%s],execption[%s]", szSqlname,e.ToString());
			m_pDbm->CheckReset();
			nCount++;
			nRet = -1;
			continue;
		}
		break;
	}
	
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","JudgeImmedAcct end,lnAcctId[%ld],nLatnId[%d],bIsImmed[%d],nRet[%d]",lnAcctId,nLatnId,bIsImmed,nRet);
	return nRet;
}

int DCComboAdaptPrdInst::UpdateDealState(const long lnTransferId, const int nLatnId)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","UpdateDealState begin,lnTransferId[%d]",lnTransferId);

	// <sql name="UpdateDealStateTransfer" bind="2" sub="553,554,555,556,558,562,563,564,566">		
	// 	UPDATE PROD_INST_TRANSFER_[@] 
	// 	SET ACCT_DEAL_STATE=11
	// 	WHERE PRD_TRANSFER_ID=?
	// 	AND ACCT_DEAL_STATE=21
	// </sql>

	int nAffect = 0;
    char sqlname[50] = {0};
    string sbuf;

	try
	{
		sprintf(sqlname, "UpdateDealStateTransfer|%d", nLatnId);
		UDBSQL* pUpdate = m_pDbm->GetSQL(sqlname);
		if(pUpdate==NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "UpdateDealState","GetSQL [%s] failed",sqlname);
			return -1;
		} 
		pUpdate->UnBindParam();
		pUpdate->BindParam(1,lnTransferId);

		pUpdate->GetSqlString(sbuf);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"UpdateDealState","Do SQL:Begin to do %s,sql :[%s]",sqlname,sbuf.c_str());
		
		pUpdate->Execute();
		nAffect = pUpdate->GetRowCount();
		pUpdate->Connection()->Commit();
		pUpdate->Close();
	}
	catch (UDBException& e)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"UpdateDealState","InitCycEvtPro throw out abnormity! sql[%s] %s",sbuf.c_str(),e.ToString());
		return -1;
	}

	if(0 == nAffect)
	{
		//未能取到对应任务的正确的表数据
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"UpdateDealState","GetRowCount[%d]Get task error!", nAffect);
		return -1;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","UpdateDealState end");
	return 0;
}

int DCComboAdaptPrdInst::LoadTransferPrdAcctDcfServ(int nLatnId,std::vector<string> &vecSQLFields,vector<long>& vecInvaildTransfer)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","LoadTransferPrdAcctDcfServ begin");
	string sbuf;
	int nRet = 0;
	char szSqlname[64]={0};
	long lnAcctId=0,lnPrdInstId=0,lnTransferId=0,lnTemp=0;
	bool isTrueAcct=false;
	long lnOfrId=0,lnCustId = 0,lnProdId=0;
	char szStatusCd[32]={0},szInstallDate[16]={0},szBillDate[16]={0},szCreateDate[16]={0},szPaymentCd[16]={0},szOfrId[16]={0},szAccNum[50]={0};
	string szUserTypeId,szEffDate,szExpDate,szAreaCode;
	bool isTrue=false;	
	ocs::StRentMainInfo tmpPrdInfo;
	try
	{
		m_multimapAcctUser.clear();
		m_multimapTransferAcct.clear();
		vecInvaildTransfer.clear();

		//ACCT_ID,PRD_INST_ID,PRD_TRANSFER_ID
		if(3 == vecSQLFields.size())
		{
			lnAcctId = atol(vecSQLFields[0].c_str());
			lnPrdInstId = atol(vecSQLFields[1].c_str());
			lnTransferId = atol(vecSQLFields[2].c_str());

			//任务对应表数据预占
			nRet = UpdateDealState(lnTransferId, nLatnId);
			if(nRet < 0)
			{
				//任务处理失败
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "", "UpdateDealState failed,exit");
				return -1;
			}
		}
		else
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "task_params size[%d] error!", vecSQLFields.size());
			return -1;
		}

		isTrueAcct = false;
		//校验数据是否正确
		UDBSQL* pQueryPrd = m_pDbm->GetSQL("data_serv");
		if(pQueryPrd ==NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"LoadTransferPrdAcctDcfServ","not find sql data_serv");
			return -1;
		}
		pQueryPrd->UnBindParam();
		pQueryPrd->BindParam(1,lnPrdInstId);
		pQueryPrd->BindParam(2,nLatnId);
		pQueryPrd->GetSqlString(sbuf);
		pQueryPrd->Execute();
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"LoadTransferPrdAcctDcfServ","lnPrdInstId[%ld],SQL[data_serv]:[%s]",lnPrdInstId,sbuf.c_str());  
		while(pQueryPrd->Next())
		{
			pQueryPrd->GetValue(3,szAccNum);
			pQueryPrd->GetValue(5,szUserTypeId);
			pQueryPrd->GetValue(6,lnOfrId);//OFFER_ID@26
			pQueryPrd->GetValue(7,szEffDate);
			pQueryPrd->GetValue(8,szExpDate);
			pQueryPrd->GetValue(10,lnCustId);
			pQueryPrd->GetValue(11,szStatusCd);//STATUS_CD@8
			pQueryPrd->GetValue(12,szCreateDate);//INSTALL_DATE@27
			pQueryPrd->GetValue(14,szInstallDate);//INSTALL_DATE@27
			pQueryPrd->GetValue(16,szBillDate);//BILL_DATE@28
			pQueryPrd->GetValue(17,szPaymentCd);//PAYMENT_MODE_CD
			pQueryPrd->GetValue(18,lnProdId);
			pQueryPrd->GetValue(19,szAreaCode);

			string strChargetatusCd;
			nRet = QueryChargeStatusCd(szStatusCd,strChargetatusCd);
			if (nRet < 0)
			{				
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","Curr PrdInstId[%ld] QueryChargeStatusCd filed",lnPrdInstId);
				return -1;
			}
			strncpy(szStatusCd,strChargetatusCd.c_str(),sizeof(szStatusCd)-1);
			szStatusCd[sizeof(szStatusCd)-1] = '\0';
			
			if(szInstallDate[0]!='\0')
			{
				szInstallDate[8]='\0';	
				if(strncmp(szInstallDate,DCDataCenter::instance()->sCycleEndDate,8)>0 )
					continue;			
			}
			if(szBillDate[0]=='\0')
			{
				strcpy(szBillDate,"20990101235959");			
			}
			if( !InCompare(szStatusCd,"130000,150000,110000") || ( strcmp(szStatusCd,"110000")==0 && strncmp(szBillDate,DCDataCenter::instance()->sCycleBeginDate,8)>=0)   )
			{
				isTrue=true;
			}
			if(!isTrue)
				continue;
			
			pQueryPrd->GetValue(1,lnTemp);//ET_ACCT_ID
			if(lnTemp == lnAcctId)
			{
				isTrueAcct = true;
				break;
			}
		}
		pQueryPrd->Close();
		
		if(isTrueAcct)
		{
			STTransferAcctPrd stTmp;
			stTmp.lnTransferId = lnTransferId;
			stTmp.lnPrdInstId = lnPrdInstId;
			stTmp.lnNewAcctId = lnAcctId;
			m_multimapTransferAcct.insert(make_pair(lnAcctId,stTmp));

			tmpPrdInfo.lnProdInstId = lnPrdInstId;
			tmpPrdInfo.lnCustId = lnCustId;
			tmpPrdInfo.lnOfferId = lnOfrId;
			tmpPrdInfo.lnProdId = lnProdId;
			tmpPrdInfo.szStatusCd = szStatusCd;
			tmpPrdInfo.szCreateDate = szCreateDate;
			tmpPrdInfo.szInstallDate = szInstallDate;
			tmpPrdInfo.szFirstFinishDate = szBillDate;				
			tmpPrdInfo.szEffDate = szEffDate;
			tmpPrdInfo.szExpDate = szExpDate;
			tmpPrdInfo.szAreaCode = szAreaCode;				
			tmpPrdInfo.szAccNum = szAccNum;
			tmpPrdInfo.szUserTypeId = szUserTypeId;
			tmpPrdInfo.nBelongDay = atoi(szPaymentCd);//暂时用于保存用户预后属性，ComboAdapt后会更新为正确值
			m_multimapAcctUser.insert(make_pair(lnAcctId,tmpPrdInfo)); 			
		}
		else
		{
			vecInvaildTransfer.push_back(lnTransferId);
		}
	} 
	catch(UDBException& e)
    {
       	DCBIZLOG(DCLOG_LEVEL_ERROR,-e.GetErrorCode(),"LoadTransferPrdAcctDcfServ","sql[%s] execption[%s]",sbuf.c_str(),e.ToString());
		return -1;
	}
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","LoadTransferPrdAcctDcfServ end,m_multimapTransferAcct.size[%d],vecInvaildTransfer.size[%d]",m_multimapTransferAcct.size(),vecInvaildTransfer.size());

	return 0;
}

int DCComboAdaptPrdInst::LoadTransferPrdAcct(int nLatnId,vector<long>& vecInvaildTransfer)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::LoadTransferPrdAcct begin");
	string sbuf;	
	int nRet = 0;
	char szSqlname[64]={0};
	long lnAcctId=0,lnPrdInstId=0,lnTransferId=0,lnTemp=0;
	bool isTrueAcct=false;
	long lnOfrId=0,lnCustId = 0,lnProdId=0;
	char szStatusCd[32]={0},szInstallDate[16]={0},szBillDate[16]={0},szCreateDate[16]={0},szPaymentCd[16]={0},szOfrId[16]={0},szAccNum[50]={0};
	string szUserTypeId,szEffDate,szExpDate,szAreaCode;
	bool isTrue=false;	
	ocs::StRentMainInfo tmpPrdInfo;
	try
	{
		m_multimapAcctUser.clear();
		m_multimapTransferAcct.clear();
		vecInvaildTransfer.clear();

		sprintf(szSqlname,"LoadTransferPrdAcct|%d",nLatnId);
	    UDBSQL* pQuery = m_pDbm->GetSQL(szSqlname);			
		if(pQuery==NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "DCComboAdaptPrdInst::LoadTransferPrdAcct","GetSQL [%s] failed",szSqlname);
			return -1;
		} 
	    pQuery->UnBindParam();
		pQuery->GetSqlString(sbuf);
		pQuery->Execute();
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::LoadTransferPrdAcct","Do SQL:Begin to do LoadTransferPrdAcct,sql :[%s]",sbuf.c_str());	
	    while(pQuery->Next())
	    {
	    	pQuery->GetValue(1,lnAcctId);
	    	pQuery->GetValue(2,lnPrdInstId);
	    	pQuery->GetValue(3,lnTransferId);

			isTrueAcct = false;
			//校验数据是否正确
			UDBSQL* pQueryPrd = m_pDbm->GetSQL("data_serv");
			if(pQueryPrd ==NULL)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCComboAdaptPrdInst::LoadTransferPrdAcct","not find sql data_serv");
				return -1;
			}
			pQueryPrd->UnBindParam();
			pQueryPrd->BindParam(1,lnPrdInstId);
			pQueryPrd->BindParam(2,nLatnId);
			pQueryPrd->GetSqlString(sbuf);
			pQueryPrd->Execute();
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCComboAdaptPrdInst::LoadTransferPrdAcct","lnPrdInstId[%ld],SQL[data_serv]:[%s]",lnPrdInstId,sbuf.c_str());  
			while(pQueryPrd->Next())
			{
				pQueryPrd->GetValue(3,szAccNum);
				pQueryPrd->GetValue(5,szUserTypeId);
				pQueryPrd->GetValue(6,lnOfrId);//OFFER_ID@26
				pQueryPrd->GetValue(7,szEffDate);
				pQueryPrd->GetValue(8,szExpDate);
				pQueryPrd->GetValue(10,lnCustId);
				pQueryPrd->GetValue(11,szStatusCd);//STATUS_CD@8
				pQueryPrd->GetValue(12,szCreateDate);//INSTALL_DATE@27
				pQueryPrd->GetValue(14,szInstallDate);//INSTALL_DATE@27
				pQueryPrd->GetValue(16,szBillDate);//BILL_DATE@28
				pQueryPrd->GetValue(17,szPaymentCd);//PAYMENT_MODE_CD
				pQueryPrd->GetValue(18,lnProdId);
				pQueryPrd->GetValue(19,szAreaCode);

				string strChargetatusCd;
				nRet = QueryChargeStatusCd(szStatusCd,strChargetatusCd);
				if (nRet < 0)
				{				
					DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","Curr PrdInstId[%ld] QueryChargeStatusCd filed",lnPrdInstId);
					return -1;
				}
				strncpy(szStatusCd,strChargetatusCd.c_str(),sizeof(szStatusCd)-1);
				szStatusCd[sizeof(szStatusCd)-1] = '\0';
				
				if(szInstallDate[0]!='\0')
				{
					szInstallDate[8]='\0';	
					if(strncmp(szInstallDate,DCDataCenter::instance()->sCycleEndDate,8)>0 )
						continue;			
				}
				if(szBillDate[0]=='\0')
				{
					strcpy(szBillDate,"20990101235959");			
				}
				if( !InCompare(szStatusCd,"130000,150000,110000") || ( strcmp(szStatusCd,"110000")==0 && strncmp(szBillDate,DCDataCenter::instance()->sCycleBeginDate,8)>=0)   )
				{
					isTrue=true;
				}
				if(!isTrue)
					continue;
				
				pQueryPrd->GetValue(1,lnTemp);//ET_ACCT_ID
				if(lnTemp == lnAcctId)
				{
					isTrueAcct = true;
					break;
				}
	    	}
			pQueryPrd->Close();
			
			if(isTrueAcct)
			{
				STTransferAcctPrd stTmp;
				stTmp.lnTransferId = lnTransferId;
				stTmp.lnPrdInstId = lnPrdInstId;
				stTmp.lnNewAcctId = lnAcctId;
				m_multimapTransferAcct.insert(make_pair(lnAcctId,stTmp));

				tmpPrdInfo.lnProdInstId = lnPrdInstId;
				tmpPrdInfo.lnCustId = lnCustId;
				tmpPrdInfo.lnOfferId = lnOfrId;
				tmpPrdInfo.lnProdId = lnProdId;
				tmpPrdInfo.szStatusCd = szStatusCd;
				tmpPrdInfo.szCreateDate = szCreateDate;
				tmpPrdInfo.szInstallDate = szInstallDate;
				tmpPrdInfo.szFirstFinishDate = szBillDate;				
				tmpPrdInfo.szEffDate = szEffDate;
				tmpPrdInfo.szExpDate = szExpDate;
				tmpPrdInfo.szAreaCode = szAreaCode;				
				tmpPrdInfo.szAccNum = szAccNum;
				tmpPrdInfo.szUserTypeId = szUserTypeId;
				tmpPrdInfo.nBelongDay = atoi(szPaymentCd);//暂时用于保存用户预后属性，ComboAdapt后会更新为正确值
				m_multimapAcctUser.insert(make_pair(lnAcctId,tmpPrdInfo)); 			
			}
			else
			{
				vecInvaildTransfer.push_back(lnTransferId);
			}
	    }
		pQuery->Close();
	} 
	catch(UDBException& e)
    {
       	DCBIZLOG(DCLOG_LEVEL_ERROR,-e.GetErrorCode(),"DCComboAdaptPrdInst::LoadTransferPrdAcct","sql[%s] execption[%s]",sbuf.c_str(),e.ToString());
		return -1;
	}
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::LoadTransferPrdAcct end,m_multimapTransferAcct.size[%d],vecInvaildTransfer.size[%d]",m_multimapTransferAcct.size(),vecInvaildTransfer.size());
	return 0;
}

int DCComboAdaptPrdInst::DeleteInvalidCrossAcct(const STSpecialAcctKey& stInfo,int nLatnId)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::DeleteInvalidCrossAcct begin,lnOfrInstId[%ld],lnAcctId[%ld]",stInfo.lnOfrInstId,stInfo.lnAcctId);

	string sbuf;
	char szSqlname[64]={0};
	try
	{
		sprintf(szSqlname,"DeleteSpecialAcct|%d",nLatnId);
	    UDBSQL* pExec = m_pDbm->GetSQL(szSqlname);			
		if(pExec==NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "","GetSQL [%s] failed",szSqlname);
			return -1;
		} 
	    pExec->UnBindParam();
	    pExec->BindParam(1,2);//=1大账户 =2跨账户 =3特大账户 
	    pExec->BindParam(2,stInfo.lnOfrInstId);
	    pExec->BindParam(3,stInfo.lnAcctId);//and groupid=1
		pExec->GetSqlString(sbuf);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::DeleteInvalidCrossAcct","Do SQL:Begin to do DeleteSpecialAcct,sql :[%s]",sbuf.c_str());	
	    
	    pExec->Execute();
		pExec->Connection()->Commit();
		pExec->Close();

	}
	catch(UDBException& e)
    {
       	DCBIZLOG(DCLOG_LEVEL_ERROR,-e.GetErrorCode(),"DCComboAdaptPrdInst::DeleteInvalidCrossAcct","sql[%s] execption[%s]",sbuf.c_str(),e.ToString());
		return -1;
	}
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::DeleteInvalidCrossAcct end");
	return 0;
}

//检查新装套餐是否是跨账户套餐,是返回true,否则返回false
bool DCComboAdaptPrdInst::CheckNewCrossOfrInst(const set<long>& setOfrInstAcct,const long& lnOfrInstId, long &lnSpecialId)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::CheckNewCrossOfrInst begin,lnOfrInstId[%ld]",lnOfrInstId);

	long lnAcctId=-99;
	int nOldCrossOfr=0;
	STSpecialAcctKey sttmpInfo;
	map<STSpecialAcctKey,long>::iterator itermap;
	
	set<long>::iterator iteracct = setOfrInstAcct.begin();
	for(;iteracct!=setOfrInstAcct.end();iteracct++)
	{
		lnAcctId = *iteracct;
		
		sttmpInfo.lnAcctId = lnAcctId;
		sttmpInfo.lnOfrInstId = lnOfrInstId;
		sttmpInfo.nGroupId = 2;  //跨账户
		itermap = m_mapSpecialAcctId.find(sttmpInfo);
		if(itermap!=m_mapSpecialAcctId.end()) 
		{
			nOldCrossOfr+=1;			
		}
	}
	if(nOldCrossOfr == setOfrInstAcct.size())  //根据新装ofrInstId查出的acct_id都在TB_BIL_CROSS_ACCOUNT_[LatnId]表中有记录，且类型都为跨账户
	{
		lnSpecialId = itermap->second;//跨账户返回ModGroupId
		return false;
	}
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::CheckNewCrossOfrInst end");
	return true;	

}

int DCComboAdaptPrdInst::WaitNewCrossFinish(const set<long>& setOfrInstAcct,const long& lnOfrInstId,const int& nLatnId, long &lnSpecialId)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::WaitNewCrossFinish begin");

	string sbuf;
	int nOldCrossOfr=0;
	char szSqlname[64]={0},szBuf[18]={0};
	int nQueryGroupId=0;
	long lnQueryOfrInstId=0;
	long lnModGroupId=-99,lnQueryAcctId=-99;
	string strAcctList = "";
	set<long>::iterator iteracct = setOfrInstAcct.begin();
	for(;iteracct!=setOfrInstAcct.end();iteracct++)
	{
		memset(szBuf,0x00,sizeof(szBuf));
		sprintf(szBuf,",%ld,",*iteracct);
		strAcctList += szBuf;
	}

	try
	{
		for(int icycle=0;icycle<3;icycle++)
		{
			sleep(3);
			nOldCrossOfr=0;
			
			sprintf(szSqlname,"QuerySpecialAcct|%d",nLatnId);
	        UDBSQL* pQuery = m_pDbm->GetSQL(szSqlname);			
			if(pQuery==NULL)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "DCComboAdaptPrdInst::WaitNewCrossFinish","GetSQL [%s] failed",szSqlname);
				return -1;
			} 
	        pQuery->UnBindParam();
	        pQuery->BindParam(1,strAcctList.c_str());    
	        pQuery->BindParam(2,lnOfrInstId);
			pQuery->GetSqlString(sbuf);
			pQuery->Execute();
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::WaitNewCrossFinish","strAcctList[%s] lnOfrInstId[%ld],Do SQL:Begin to do QuerySpecialAcct,sql :[%s]",strAcctList.c_str(),lnOfrInstId,sbuf.c_str());	
	        while(pQuery->Next())
	        {
	        	pQuery->GetValue(1,nQueryGroupId);
	        	pQuery->GetValue(2,lnQueryOfrInstId);
				pQuery->GetValue(3,lnModGroupId);
				pQuery->GetValue(4,lnQueryAcctId);

				if(nQueryGroupId==2 && setOfrInstAcct.find(lnQueryAcctId)!=setOfrInstAcct.end() && lnQueryOfrInstId==lnOfrInstId)
				{
					nOldCrossOfr+=1;			
				}
			}
			pQuery->Close();
			if(nOldCrossOfr == setOfrInstAcct.size())
			{
				lnSpecialId = lnModGroupId;
				return 1;
			}
		}
	} 
	catch(UDBException& e)
    {
       	DCBIZLOG(DCLOG_LEVEL_ERROR,-e.GetErrorCode(),"DCComboAdaptPrdInst::WaitNewCrossFinish","sql[%s] execption[%s]",sbuf.c_str(),e.ToString());
		return -1;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::WaitNewCrossFinish end ");
	return 0;	

}

int DCComboAdaptPrdInst::SaveMiddleCrossAcct(set<long> setAcctList, long lnOfrInstId, int nLatnId)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::SaveMiddleCrossAcct begin,lnOfrInstId[%ld]",lnOfrInstId);
	string sbuf;
	try
	{
		char szSqlname[64]={0};
		long lnAcctId=-99;

		set<long>::iterator iteracct = setAcctList.begin();
		for(;iteracct!=setAcctList.end();iteracct++)
		{
			lnAcctId = *iteracct;
			
			sprintf(szSqlname,"InsertCrossMiddle|%d",nLatnId);
	        UDBSQL* pExec = m_pDbm->GetSQL(szSqlname);			
			if(pExec==NULL)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "","GetSQL [%s] failed",szSqlname);
				return -1;
			} 
	        pExec->UnBindParam();
	        pExec->BindParam(1,lnAcctId);
	        pExec->BindParam(2,lnOfrInstId); 
			pExec->GetSqlString(sbuf);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::SaveMiddleCrossAcct","lnAcctId[%ld] lnOfrInstId[%ld],Do SQL:Begin to do InsertCrossMiddle,sql :[%s]",lnAcctId,lnOfrInstId,sbuf.c_str());	
	        
	        pExec->Execute();
			pExec->Connection()->Commit();
			pExec->Close();
			
		}
	} 
	catch(UDBException& e)
    {
       	DCBIZLOG(DCLOG_LEVEL_ERROR,-e.GetErrorCode(),"DCComboAdaptPrdInst::SaveMiddleCrossAcct","sql[%s] execption[%s]",sbuf.c_str(),e.ToString());
		return -1;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::SaveMiddleCrossAcct end ");
	return 0;

}


//保存特殊分流账户--大账户和特大账户
int DCComboAdaptPrdInst::SaveSpecialBigAcct(long lnAcctId, int nGroupId, int nLatnId)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::SaveSpecialBigAcct begin,lnAcctId[%ld],nGroupId[%d]",lnAcctId,nGroupId);
	string sbuf;
	try
	{
		char szSqlname[64]={0},szBuf[18]={0};
		int nQueryGroupId=0;
		long lnQueryOfrInstId=0;
		long lnModGroupId=-99,lnQueryAcctId=-99,lnOfrInstId=-99;
		
		sprintf(szBuf,",%ld,",lnAcctId);

		sprintf(szSqlname,"QuerySpecialAcct|%d",nLatnId);
        UDBSQL* pQuery = m_pDbm->GetSQL(szSqlname);			
		if(pQuery==NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "DCComboAdaptPrdInst::SaveSpecialBigAcct","GetSQL [%s] failed",szSqlname);
			return -1;
		} 
        pQuery->UnBindParam();
        pQuery->BindParam(1,szBuf);     
        pQuery->BindParam(2,lnOfrInstId);
		pQuery->GetSqlString(sbuf);
		pQuery->Execute();
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::SaveSpecialBigAcct","acct_id list[%s],lnOfrInstId[%ld],Do SQL:Begin to do QuerySpecialAcct,sql :[%s]",szBuf,lnOfrInstId,sbuf.c_str());	
        while(pQuery->Next())
        {
        	pQuery->GetValue(1,nQueryGroupId);
        	pQuery->GetValue(2,lnQueryOfrInstId);
			pQuery->GetValue(3,lnModGroupId);
			pQuery->GetValue(4,lnQueryAcctId);
			
        	if(nGroupId==1  && lnQueryAcctId==lnAcctId)
        	{
        		pQuery->Close();
				return 0;//大账户结果不能覆盖任何结果
        	}
			if(nGroupId==3  && lnQueryAcctId==lnAcctId && nQueryGroupId==1)
        	{//特大账户更新掉大账户
        	
				STSpecialAcctKey sttmpInfo;
				sttmpInfo.lnAcctId = lnAcctId;
				sttmpInfo.lnOfrInstId = lnOfrInstId;
				sttmpInfo.nGroupId = 1;
				map<STSpecialAcctKey,long>::iterator itermap = m_mapSpecialAcctId.find(sttmpInfo);
				if(itermap!=m_mapSpecialAcctId.end())
				{
					lnModGroupId = itermap->second;
					m_mapSpecialAcctId.erase(itermap);
				}
				sttmpInfo.nGroupId = 3;
				m_mapSpecialAcctId.insert(make_pair(sttmpInfo,lnModGroupId));
				
				sprintf(szSqlname,"UpdateSpecialAcct|%d",nLatnId);
		        UDBSQL* pExec = m_pDbm->GetSQL(szSqlname);			
				if(pExec==NULL)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "","GetSQL [%s] failed",szSqlname);
        			pQuery->Close();
					return -1;
				} 
		        pExec->UnBindParam();
		        pExec->BindParam(1,3);//=1大账户 =2跨账户 =3特大账户         
		        pExec->BindParam(2,lnModGroupId);   
		        pExec->BindParam(3,lnOfrInstId);
		        pExec->BindParam(4,lnAcctId);
				pExec->GetSqlString(sbuf);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::SaveSpecialAcct","lnModGroupId[%ld] lnOfrInstId[%ld] lnAcctId[%ld],Do SQL:Begin to do DeleteSpecialAcct,sql :[%s]",lnModGroupId,lnOfrInstId,lnAcctId,sbuf.c_str());	
		        
		        pExec->Execute();
				pExec->Connection()->Commit();
				pExec->Close();	
				
        		pQuery->Close();
				return 0;
        	}
			
        }
		pQuery->Close();
		
		//新纪录
		{
			sprintf(szSqlname,"GetSpecialAcctSeq|%d",nLatnId);
	        UDBSQL* pGetSeq = m_pDbm->GetSQL(szSqlname);			
			if(pGetSeq==NULL)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "","GetSQL [%s] failed",szSqlname);
				return -1;
			} 
	        pGetSeq->UnBindParam();
			pGetSeq->GetSqlString(sbuf);
			pGetSeq->Execute();
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::SaveSpecialAcct","Do SQL:Begin to do GetSpecialAcctSeq,sql :[%s]",sbuf.c_str());	
	        if(pGetSeq->Next())
	        {
				pGetSeq->GetValue(1,lnModGroupId);
	        }
			pGetSeq->Close();
		}
		
		sprintf(szSqlname,"InsertSpecialAcct|%d",nLatnId);
        UDBSQL* pExec = m_pDbm->GetSQL(szSqlname);			
		if(pExec==NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "","GetSQL [%s] failed",szSqlname);
			return -1;
		} 
        pExec->UnBindParam();
        pExec->BindParam(1,lnAcctId);
        pExec->BindParam(2,nGroupId);//=1大账户 =2跨账户 =3特大账户     
        pExec->BindParam(3,lnOfrInstId);
        pExec->BindParam(4,lnModGroupId);
		pExec->GetSqlString(sbuf);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::SaveSpecialAcct","lnAcctId[%ld] nGroupId[%d],lnOfrInstId[%ld],lnModGroupId[%ld],Do SQL:Begin to do InsertSpecialAcct,sql :[%s]",lnAcctId,nGroupId,lnOfrInstId,lnModGroupId,sbuf.c_str());	
        
        pExec->Execute();
		pExec->Connection()->Commit();
		pExec->Close();
		
		STSpecialAcctKey stInfo;
		stInfo.lnAcctId = lnQueryAcctId;
		stInfo.nGroupId = nGroupId;
		stInfo.lnOfrInstId = -99;
		m_mapSpecialAcctId.insert(make_pair(stInfo,lnModGroupId));
		
		DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCComboAdaptPrdInst::SaveSpecialAcct","Add a special acct record: AcctId[%ld] GroupId[%d] OfrInstId[%ld] ModGroupId[%ld]",lnQueryAcctId,nGroupId,lnOfrInstId,lnModGroupId);		        
		DCDATLOG("RD00004:%ld!%d!%ld!%ld",lnQueryAcctId,nGroupId,lnOfrInstId,lnModGroupId);

	} 
	catch(UDBException& e)
    {
       	DCBIZLOG(DCLOG_LEVEL_ERROR,-e.GetErrorCode(),"DCComboAdaptPrdInst::SaveSpecialAcct","sql[%s] execption[%s]",sbuf.c_str(),e.ToString());
		return -1;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::SaveSpecialBigAcct end,lnAcctId[%ld],nGroupId[%d]",lnAcctId,nGroupId);
	return 0;

}


//保存特殊分流账户,根据中间表依次删除TB_BIL_CROSS_ACCOUNT_表中的大账户记录,更新MOD_GROUP_ID为最小值,--跨账户更新进程调用
int DCComboAdaptPrdInst::SaveSpecialCrossAcct(const long lnAcctId,const long lnOfrInstId, int nLatnId, bool bChange)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::SaveSpecialCrossAcct begin ");
	string sbuf;
	bool bInsertFlag = true;//插入跨账户记录
	m_mapNewModGroup.clear();
	try
	{
		char szSqlname[64]={0},szBuf[20]={0};
		int nQueryGroupId=0;
		long lnQueryOfrInstId=-99,lnQueryModGroupId=-99,lnQueryAcctId=-99, lnModGroupId=-99;
		
		sprintf(szBuf,",%ld,",lnAcctId);

		sprintf(szSqlname,"QuerySpecialAcct|%d",nLatnId);
        UDBSQL* pQuery = m_pDbm->GetSQL(szSqlname);			
		if(pQuery==NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "DCComboAdaptPrdInst::SaveSpecialCrossAcct","GetSQL [%s] failed",szSqlname);
			return -1;
		} 
        pQuery->UnBindParam();
        pQuery->BindParam(1,szBuf);     
        pQuery->BindParam(2,lnOfrInstId);
		pQuery->GetSqlString(sbuf);
		pQuery->Execute();
		
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::SaveSpecialCrossAcct","acct_id list[%s],lnOfrInstId[%ld],Do SQL:Begin to do QuerySpecialAcct,sql :[%s]",szBuf,lnOfrInstId,sbuf.c_str());	
        while(pQuery->Next())
        {
        	pQuery->GetValue(1,nQueryGroupId);
        	pQuery->GetValue(2,lnQueryOfrInstId);
			pQuery->GetValue(3,lnQueryModGroupId);
			pQuery->GetValue(4,lnQueryAcctId);

			//跨账户逻辑
			if(nQueryGroupId==2)
			{
				//查询语句有排序，保存当前第一个 跨账户 ModGroupId
				if(lnModGroupId==-99)//保留最小的跨账户分组id
				{
					lnModGroupId = lnQueryModGroupId;
				}
				//判断是否需要变更ModGroupId ,即多个跨账户
				if(lnQueryModGroupId != lnModGroupId)
				{
					if(m_mapNewModGroup.end() == m_mapNewModGroup.find(lnQueryModGroupId))
					{
						m_mapNewModGroup.insert(make_pair(lnQueryModGroupId,lnModGroupId));
					}
				}
				
				//有相同记录,则不用插入新纪录
	        	if(lnQueryAcctId==lnAcctId && lnQueryOfrInstId==lnOfrInstId)
	        	{
	        		bInsertFlag = false;
	        	}
			}
			
			//大账户变更为跨账户,先删除大账户记录，后续流程新增跨账户记录 
			//由于大账户在TB_BIL_CROSS_ACCOUNT_ 表中 只有一条ofr_inst_id -99的记录
			if((nQueryGroupId==1 || nQueryGroupId==3) && lnQueryAcctId==lnAcctId)
			{
				sprintf(szSqlname,"DeleteSpecialAcct|%d",nLatnId);
		        UDBSQL* pExec = m_pDbm->GetSQL(szSqlname);			
				if(pExec==NULL)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "","GetSQL [%s] failed",szSqlname);
					pQuery->Close();
					return -1;
				} 
		        pExec->UnBindParam();
		        pExec->BindParam(1,nQueryGroupId);//=1大账户 =2跨账户 =3特大账户       
		        pExec->BindParam(2,lnQueryOfrInstId);
		        pExec->BindParam(3,lnQueryAcctId);
				pExec->GetSqlString(sbuf);
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::SaveSpecialCrossAcct","Do SQL:Begin to do DeleteSpecialAcct,sql :[%s]",sbuf.c_str());	
		        
		        pExec->Execute();
				pExec->Close();
			}

        }
		pQuery->Close();

		//更改ModGroupId
		if(bChange && (m_mapNewModGroup.size()>0) )
		{
			sprintf(szSqlname,"UpdateCrossAcctMerge|%d",nLatnId);
	        UDBSQL* pUpdate = m_pDbm->GetSQL(szSqlname);			
			if(pUpdate==NULL)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "","GetSQL [%s] failed",szSqlname);
				return -1;
			}
			std::map<long,long>::iterator itm;
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::SaveSpecialCrossAcct","Do SQL:Begin to do UpdateCrossAcctMerge,sqlname :[%s]",szSqlname);
			for(itm=m_mapNewModGroup.begin(); itm!=m_mapNewModGroup.end(); itm++)
			{
				pUpdate->UnBindParam();
		        pUpdate->BindParam(1,itm->second);
		        pUpdate->BindParam(2,itm->first);//=1大账户 =2跨账户 =3特大账户
		        pUpdate->BindParam(3,2);//=1大账户 =2跨账户 =3特大账户
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::SaveSpecialCrossAcct","UpdateCrossAcctMerge,lodModGroup[%ld] newModGroup[%ld]", itm->first, itm->second);
		        DCDATLOG("RD00005:%d!%ld!%ld",2,itm->first,itm->second);
		        pUpdate->Execute();
			}
			pUpdate->Close();
		}
		//需要插入新纪录
		if(bInsertFlag)
		{
			if(lnModGroupId==-99)//没有关联的acctid或ofrinstid
			{
				sprintf(szSqlname,"GetSpecialAcctSeq|%d",nLatnId);
		        UDBSQL* pGetSeq = m_pDbm->GetSQL(szSqlname);			
				if(pGetSeq==NULL)
				{
					DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "","GetSQL [%s] failed",szSqlname);
					return -1;
				} 
		        pGetSeq->UnBindParam();
				pGetSeq->GetSqlString(sbuf);
				pGetSeq->Execute();
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::SaveSpecialCrossAcct","Do SQL:Begin to do GetSpecialAcctSeq,sql :[%s]",sbuf.c_str());	
		        if(pGetSeq->Next())
		        {
					pGetSeq->GetValue(1,lnModGroupId);
		        }
				pGetSeq->Close();
			}

			sprintf(szSqlname,"InsertSpecialAcct|%d",nLatnId);
	        UDBSQL* pInsert = m_pDbm->GetSQL(szSqlname);			
			if(pInsert==NULL)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "","GetSQL [%s] failed",szSqlname);
				return -1;
			} 
	        pInsert->UnBindParam();
	        pInsert->BindParam(1,lnAcctId);
	        pInsert->BindParam(2,2);//=1大账户 =2跨账户 =3特大账户     
	        pInsert->BindParam(3,lnOfrInstId);
	        pInsert->BindParam(4,lnModGroupId);
			pInsert->GetSqlString(sbuf);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::SaveSpecialCrossAcct","Do SQL:Begin to do InsertSpecialAcct,sql :[%s]",sbuf.c_str());	
	        
	        pInsert->Execute();
			pInsert->Close();
			
			DCBIZLOG(DCLOG_LEVEL_INFO, 0,"DCComboAdaptPrdInst::SaveSpecialCrossAcct","Add a special acct record: AcctId[%ld] GroupId[%d] OfrInstId[%ld] ModGroupId[%ld]",lnQueryAcctId,2,lnOfrInstId,lnModGroupId);		        
			DCDATLOG("RD00004:%ld!%d!%ld!%ld",lnQueryAcctId,2,lnOfrInstId,lnModGroupId);
		}

		//删除中间表数据
		sprintf(szSqlname,"DeleteOneCrossMiddle|%d",nLatnId);
        UDBSQL* pDelete = m_pDbm->GetSQL(szSqlname);			
		if(pDelete==NULL)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "","GetSQL [%s] failed",szSqlname);
			return -1;
		} 
        pDelete->UnBindParam();
        pDelete->BindParam(1,lnAcctId);   
        pDelete->BindParam(2,lnOfrInstId);
		pDelete->GetSqlString(sbuf);
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCComboAdaptPrdInst::SaveSpecialCrossAcct","lnAcctId[%ld] lnOfrInstId[%ld],Do SQL:Begin to do DeleteOneCrossMiddle,sql :[%s]",lnAcctId,lnOfrInstId,sbuf.c_str());	
        
        pDelete->Execute();
				
		pDelete->Connection()->Commit();//配置sql时需保证cross与middle表在同一个库中
		pDelete->Close();
	} 
	catch(UDBException& e)
    {
       	DCBIZLOG(DCLOG_LEVEL_ERROR,-e.GetErrorCode(),"DCComboAdaptPrdInst::SaveSpecialCrossAcct","sql[%s] execption[%s]",sbuf.c_str(),e.ToString());
		return -1;
	}
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::SaveSpecialCrossAcct end ");
	return 0;

}

bool DCComboAdaptPrdInst::FilterStatusCd(StPrdInstInfo& tmpPrdInfo,bool isTrial,bool isDCA)
{
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::FilterStatusCd begin,lnProdInstId[%ld],lnOfferId[%ld],szStatusCd[%s],szUserTypeId[%s],szBasicState[%s],szBillDate[%s]",tmpPrdInfo.lnProdInstId,tmpPrdInfo.lnOfferId,tmpPrdInfo.szStatusCd.c_str(),tmpPrdInfo.szUserTypeId.c_str(),tmpPrdInfo.szBasicState.c_str(),tmpPrdInfo.szFirstFinishDate.c_str());
	
	bool bFilterFlag = false;
	bool bFlag1 = false;
	bool bFlag2 = false;
	if(PROC_TYPE_PRE_NORMAL == m_nProType && isDCA)
	{
		//预付费通过dca查询用户资料的
		if (PublicLib::InCompare(tmpPrdInfo.szStatusCd.c_str(), "120064,120068"))
		{
			return true;
		}		
		char szCompTime[16] = {0};
		char szCurTime[16] = {0};
		PublicLib::GetTime(szCurTime, YYYYMMDDHHMMSS);
		szCurTime[6] = '\0';
		sprintf(szCompTime,"%s01",szCurTime);
		if ((tmpPrdInfo.szBasicState == string("F0L")) && (tmpPrdInfo.szStatusCd == string("120000")) && 
			(PublicLib::long_to_string(tmpPrdInfo.lnOfferId) == string("481")) && strncmp(tmpPrdInfo.szBillDate.c_str(),szCompTime,8) < 0
			)
		{
			bFlag1 = true;
		}
		if ((tmpPrdInfo.szBasicState == string("F0B")) || ((tmpPrdInfo.szBasicState == string("F0E"))  && !PublicLib::InCompare(PublicLib::long_to_string(tmpPrdInfo.lnOfferId).c_str(), "427,429,481,5413,581,1481")) && (tmpPrdInfo.szStatusCd != string("120000")))
		{
			bFlag2 = true;
		}
	    if (!bFlag1 && !bFlag2)
	    {
			bFilterFlag = true;
		}
	}
    else if(PROC_TYPE_PRE_NORMAL == m_nProType || isTrial )
    {
        ;//预付费直接用CRM的status_cd在sql语句中判断
    }
	else
	{
		if ((!PublicLib::InCompare(tmpPrdInfo.szStatusCd.c_str(), "110000,120000,120063,120069,150000,130000,140000")) ||
			 ((PublicLib::InCompare(tmpPrdInfo.szStatusCd.c_str(), "110000,120000,120063,120069")) && (strncmp(tmpPrdInfo.szFirstFinishDate.c_str(),DCDataCenter::instance()->sCycleBeginDate,8) >= 0)) ||
			 ((PublicLib::InCompare(tmpPrdInfo.szStatusCd.c_str(), "120000,120063")) && (strncmp(tmpPrdInfo.szFirstFinishDate.c_str(),DCDataCenter::instance()->sCycleBeginDate,8) < 0) && PublicLib::InCompare(PublicLib::long_to_string(tmpPrdInfo.lnOfferId).c_str(), "427,429,430,481,361,362")))
		{
			bFlag1 = true;
		}
		if (!bFlag1)//不满足条件的过滤
	    {
			bFilterFlag = true;
		}
	}
		    
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCComboAdaptPrdInst::FilterStatusCd end,bFlag1[%d],bFlag2[%d],bFilterFlag[%d]",bFlag1,bFlag2,bFilterFlag);
	return bFilterFlag;
}

int DCComboAdaptPrdInst::loadOneAcctUser(int nLatnId,long lnAcctId,multimap<long,ocs::StRentMainInfo> &oneAcctUser)
{    
    char sqlname[64] = {0};
    string strSQL = "";
    long lnPrdInstId = 0;   
    set<long> setPrdInstId;
    
    sprintf(sqlname, "query_acct_prod_sum");                    
    UDBSQL *pQueryPrd = m_pDbm->GetSQL(sqlname);
    if(NULL == pQueryPrd)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Not Find this sqlname[%s]", sqlname);
        return NO_FIND_SQL;
    }
    
    try
    {
        pQueryPrd->UnBindParam();
        pQueryPrd->BindParam(1, lnAcctId);
        pQueryPrd->BindParam(2, nLatnId);
        pQueryPrd->GetSqlString(strSQL);
        pQueryPrd->Execute();
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Accord to sqlname[%s], AcctId[%ld] to Get User ", sqlname, lnAcctId);
        while (pQueryPrd->Next())
        {
            pQueryPrd->GetValue(1, lnPrdInstId);
            setPrdInstId.insert(lnPrdInstId);
        }
    }
    catch(UDBException &e)
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, -e.GetErrorCode(), "", "MsgEntry::GetPrdByCurAcct,execption[%s]\n sql[%s]\n", e.ToString(), strSQL.c_str());
        return SQL_EXCEPTION;                       
    }
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "AcctId[%ld] get prdInstId size[%d]",lnAcctId,setPrdInstId.size());

    int PayMentMod = 0;
    int nRet = 0;
    ocs::StRentMainInfo tPrdInst;
    sprintf(sqlname, "data_serv");
	UDBSQL *pQuery = m_pDbm->GetSQL(sqlname);
	if (pQuery == NULL)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Get sql [data_serv]error");
		return NO_FIND_SQL;
	}

    for(set<long>::iterator itr=setPrdInstId.begin();itr!=setPrdInstId.end();++itr)
    {
        lnPrdInstId = *itr;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Accord to data_serv  PrdInstId:[%ld] LatnId:[%d]", lnPrdInstId, nLatnId);
		try
		{
			pQuery->UnBindParam();
			pQuery->BindParam(1, lnPrdInstId);
			pQuery->BindParam(2, nLatnId);
			pQuery->Execute();
			while (pQuery->Next())
			{
				StPrdInstInfo tmpPrdInfo;
				tmpPrdInfo.lnProdInstId = lnPrdInstId;				
                pQuery->GetValue(1,tmpPrdInfo.lnAcctId);
                pQuery->GetValue(3,tmpPrdInfo.szAccNum);
                pQuery->GetValue(5,tmpPrdInfo.szUserTypeId);
                pQuery->GetValue(6,tmpPrdInfo.lnOfferId);
                pQuery->GetValue(7,tmpPrdInfo.szEffDate);
                pQuery->GetValue(8,tmpPrdInfo.szExpDate);
                pQuery->GetValue(10,tmpPrdInfo.lnCustId);
                pQuery->GetValue(11,tmpPrdInfo.szStatusCd);
                pQuery->GetValue(12,tmpPrdInfo.szCreateDate);
                pQuery->GetValue(13,tmpPrdInfo.szFirstFinishDate);
                pQuery->GetValue(14,tmpPrdInfo.szInstallDate);
                pQuery->GetValue(17,PayMentMod);
                pQuery->GetValue(18,tmpPrdInfo.lnProdId);
                pQuery->GetValue(19,tmpPrdInfo.szAreaCode);                
                pQuery->GetValue(20, tmpPrdInfo.szBasicState);  //BASIC_STATE
                
                string strChargetatusCd;
				nRet = QueryChargeStatusCd(tmpPrdInfo.szStatusCd,strChargetatusCd);
				if (nRet < 0)
				{				
					DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","Curr PrdInstId[%ld] QueryChargeStatusCd filed",lnPrdInstId);
					return -1;
				}
				tmpPrdInfo.szStatusCd = strChargetatusCd;
				if (FilterStatusCd(tmpPrdInfo,false,true))
    			{				
    				DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","Curr PrdInstId[%ld] AcctId[%ld] FilterStatusCd,szStatusCd[%s]",tmpPrdInfo.lnProdInstId,tmpPrdInfo.lnAcctId,tmpPrdInfo.szStatusCd.c_str());
    				continue;
    			}

                tPrdInst.lnAcctId = tmpPrdInfo.lnAcctId;
    			tPrdInst.lnProdInstId = tmpPrdInfo.lnProdInstId;    			
    			tPrdInst.szAccNum = tmpPrdInfo.szAccNum;
    			tPrdInst.szUserTypeId = tmpPrdInfo.szUserTypeId;
    			tPrdInst.lnOfferId = tmpPrdInfo.lnOfferId;             
    			tPrdInst.lnCustId = tmpPrdInfo.lnCustId;
    			tPrdInst.szStatusCd = tmpPrdInfo.szStatusCd;
    			tPrdInst.szCreateDate = tmpPrdInfo.szCreateDate;
    			tPrdInst.szFirstFinishDate = tmpPrdInfo.szFirstFinishDate;
    			tPrdInst.szInstallDate = tmpPrdInfo.szInstallDate;
    			tPrdInst.lnProdId = tmpPrdInfo.lnProdId;	
    			tPrdInst.szAreaCode = tmpPrdInfo.szAreaCode;
    			tPrdInst.szEffDate = tmpPrdInfo.szEffDate;
    			tPrdInst.szExpDate = tmpPrdInfo.szExpDate;	
    			tPrdInst.szEventEffDate = tmpPrdInfo.szEffDate;
    			tPrdInst.szEventExpDate = tmpPrdInfo.szExpDate;
    			tPrdInst.lnOfrInstId = -2;
    			tPrdInst.lnGroupUserGroupId = tPrdInst.lnProdInstId;
                tPrdInst.nBelongDay = PayMentMod==2100?2100:1200;//暂时用于保存用户预后属性，ComboAdapt后会更新为正确值
    				
				oneAcctUser.insert(make_pair(lnAcctId,tPrdInst));
			}

			pQuery->Close();
		}
		catch (UDBException &e)
		{
			pQuery->GetSqlString(strSQL);
			DCBIZLOG(DCLOG_LEVEL_ERROR, -e.GetErrorCode(), "", "Get Serv Info, execption[%s] sql[%s]", e.ToString(), strSQL.c_str());
			return false;
		}
    }
    return 0;
}



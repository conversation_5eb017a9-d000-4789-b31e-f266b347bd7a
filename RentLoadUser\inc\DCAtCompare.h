/***********************************************************************
* Module:  DCAtCompare.h
* Author:  gaos
* Modified: 2010年5月26日 14:44:18
* Purpose: Declaration of the class DCAtCompare
* Comment: 融合帐务条件比较
***********************************************************************/

#if !defined(_DCAtCompare_h)
#define _DCAtCompare_h

#include <map>
//#include "common/DCAcctDataStruct.h"
#include "DataDef.h"
#include "publiclib.h"

class DCAtCompare
{
public:
	/* 判断单个条件项 */
	bool Compare(STATCondItem& vi_stCondItem);
	/* 判断分组条件项集。组间或；组内与 */
	bool Compare(multimap<int,STATCondItem> & vi_mmapCondItems);
    /* 分组条件判断，用数组的形式实现，提高效率*/
    bool Compare(const int vi_iCount, const int *vi_iGroup,  STATCondItem *vi_stCondition);

	bool Compare(STATCondItem* vi_stCondItem);

protected:
private:
	//判断字符串是否是数字
	bool IsNumber(const char * vi_sIn);
	//判断字符是否是字母
	bool IsAlpha(char vi_chIn);
	//数值型左右值比较
	//bool NumberCompare(long vi_lLeft, string &vi_strOperator, long vi_lRight);
    //为了提高效率，string改为char*
    bool NumberCompare(long vi_lLeft, const char* vi_pszOperator, long vi_lRight);
	//In比较
	//bool InCompare(string &vi_strLeft, string &vi_strRight);
    //In比较，用新的算法来判断
    bool InCompare(const char* vi_pszLeft, const char* vi_pszRight);

private:
    //为了提高效率，设置临时变量
    char* m_pLeft;
    char* m_pOperator;
    char* m_pRight;
    char  m_szTmpLeft[1024];
    char  m_szTmpRight[1024];

	int m_nOperatorId;
};

#endif
// Language:    C++
//
// OS Platform: UNIX
//
// Authors: <AUTHORS>
//
// History:
// Copyright (C) 2001 by <PERSON><PERSON><PERSON>, Linkage. All Rights Reserved.
//
// Comments:
//


#ifndef TRIM_H_INCLUDED
#define TRIM_H_INCLUDED

#include "config-all.h"
#include <string>

USING_NAMESPACE(std)


// define white space
//const string WHITE_SPACE (" \t\n\r");
#define WHITE_SPACE string(" \t\n\r")

enum TRIM_TYPE {LTRIM, RTRIM, LRTRIM};


void trim(string& str, const TRIM_TYPE trimtype, const string& trimchar);
void trim(string& str, const TRIM_TYPE trimtype = LRTRIM);
void trim(char *dest, const char *src, TRIM_TYPE trimtype = LRTRIM);


#endif // TRIM_H_INCLUDED


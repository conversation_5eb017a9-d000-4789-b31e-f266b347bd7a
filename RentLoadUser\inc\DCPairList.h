/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCRentLoadUser.h
*Indentifier：
*
*Description：
*		月租用户数据加载类
*Version：
*		V1.0
*Author:
*		
*Finished：
*		
*History:
********************************************/

#ifndef _DCPAIR_LIST_H_
#define _DCPAIR_LIST_H_

#include <stdio.h>
#include <string>
#include <errno.h>
#include <pthread.h>
#include <map>
#include <vector>
#include <set>
#include <sys/time.h>

//#include "ace/OS.h"
//#include "ace/Signal.h"
//#include "ace/Condition_T.h"

#include "DCLogMacro.h"


using namespace std;

#define DIFFTIME(Tb, Ta) ((((Tb)>>24) - ((Ta)>>24))*1000000 + ((Tb)&0xFFFFFF) - ((Ta)&0xFFFFFF))

struct SMsgPair
{
	std::string 	uuid;
	std::string		servName;
	std::string 	sendMsg;
	struct timeval 	begin;//发送CCR时间
	int 			nAcctType;
	int				nReSend;
	long 			lnSpecialId;
	int				nLogBillingCycleId;
	int 			nLatnId;
	int 			nPrdInstNum;
	int 			nSubInstNum;
	int 			nOfrInstNum;
	int 			nGroupNum;
	long 			lnPidId;
	int 			nMsgType;
	int				nMessagesource;
	int 			nBillingFlag;
	long			nBatchNo;
	int             nCallBackRetCode;
	std::vector<long> 	vecTransferId;	

	SMsgPair()
	{
		uuid = "";
		servName = "";
		sendMsg = "";
		nAcctType = 11;//普通账户
		nReSend = 0;
		lnSpecialId = 0;
		nLogBillingCycleId = 20180101;
		nLatnId = 551;
		nPrdInstNum = 0;
		nSubInstNum = 0;
		nOfrInstNum = 0;
		nGroupNum = 1;
		lnPidId = 0;
		nMsgType = 0;
		nMessagesource = 0;
		nBillingFlag = 0;
		nBatchNo = 0;
		nCallBackRetCode = 0;
	};

	SMsgPair& operator = (const SMsgPair & IN) 
	{
		uuid = IN.uuid;
		servName = IN.servName;
		sendMsg = IN.sendMsg;
		nAcctType = IN.nAcctType;
		nReSend = IN.nReSend;
		lnSpecialId = IN.lnSpecialId;
		nLogBillingCycleId = IN.nLogBillingCycleId;
		nLatnId = IN.nLatnId;
		nPrdInstNum = IN.nPrdInstNum;
		nSubInstNum = IN.nSubInstNum;
		nOfrInstNum = IN.nOfrInstNum;
		nGroupNum = IN.nGroupNum;
		lnPidId = IN.lnPidId;
		nMsgType = IN.nMsgType;
		nMessagesource = IN.nMessagesource;
		nBillingFlag = IN.nBillingFlag;
		nBatchNo = IN.nBatchNo;
		begin = IN.begin;
		nCallBackRetCode = IN.nCallBackRetCode;
		vecTransferId = IN.vecTransferId;
		
		return *this;
	}
};

class DCPairList
{
	public:
		DCPairList();
		virtual ~DCPairList();

	public:

		/****************************************************************************************
		*@input

		*@output

		*@return
				队列大小

		*@description		获取队列大小
		******************************************************************************************/
		int size();



		/****************************************************************************************
		*@input
				pMsg:序列号值对

		*@output

		*@return
				0 : 入列成功

		*@description		入队列(已满则阻塞)
		******************************************************************************************/
		int in(const std::string uuid,const SMsgPair sMsg);



		/****************************************************************************************
		*@input
				timeout : 超时时间(ms)

		*@output
				pMsg : 序列号值对

		*@return
				0 : 出列成功

		*@description		出队列(为空阻塞)
		******************************************************************************************/
		int out(const std::string uuid,SMsgPair &sMsg,string ipErrorCode);



		/****************************************************************************************
		*@input

		*@output

		*@return

		*@description		清空超时值对
		******************************************************************************************/
		void clearTimeOut(long timeOut, std::vector<SMsgPair>& out);

		//int get(const std::string uuid,SMsgPair &sMsg);

		int GetTimeOutList(std::vector<SMsgPair> 	  &vecTimeOutList);

		bool UidQueOut(SMsgPair &msgpair);

		int SetClear();

		int InGroupUid(const std::string uuid,const std::vector<std::string> vecGroupUid);

		int update(const std::string uuid,const SMsgPair sMsg);
		
	protected:

		int lock();
		int unlock();
		int UidQueLock();
		int UidQueUnLock();

	protected:

		pthread_mutex_t 			m_mutex;//m_PairMap的锁
		std::map<std::string, SMsgPair>		m_PairMap;//已发送uid资料
		
		//std::vector<SMsgPair> 			m_vecTimeOutList;//超时待重发uid队列
		std::vector<SMsgPair>			m_uidPairMap;//整组返回uid队列，待写日志等
		std::vector<SMsgPair>			m_vecCallBack;  //记录每个回调消息的返回码
		pthread_mutex_t 			m_mutexUid;//m_uidPairMap的锁
		std::set<std::string>					m_setCallbackUid;//已返回uid，防重复返回处理
		//std::map<std::string, SMsgPair>		m_mapNoResUid;//重发再超时uid队列用于资料查找

		std::map<std::string, std::vector<std::string> >	m_mapGroupUid;//uuid分组队列，key为组中最后一个uid,value为当前组的所有uuid,未记录回调消息的返回码

		std::set<std::string>					m_setInvokeAsyncUid;//已发送的uid
		std::set<std::string> 					m_setTimeOutUid;//超时待重发关键uid
		std::set<std::string>					m_setNoResUid;//再次超时关键uid
};

#endif


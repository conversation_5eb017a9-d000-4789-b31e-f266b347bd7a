#include "DCRDEventType.h"
#include "publiclib.h"
//#include "RealDiscount/DCRDParameter.h"
//#include "common/DCAcctFunction.h"

#include <ace/Log_Msg.h>
#include <ace/Log_Msg_Backend.h>
#include <ace/Log_Record.h>
#include "DCLogMacro.h"

using namespace std;

/*******************************************************************************
*@return
*@description	    构造函数
*@frequency of call
*******************************************************************************/
DCRDEventType::DCRDEventType()
{
	m_pCondition = NULL;
}

/*******************************************************************************
*@return
*@description	    析构函数
*@frequency of call
*******************************************************************************/
DCRDEventType::~DCRDEventType()
{
	if (m_pCondition != NULL)
	{
		delete m_pCondition;
	}

}

int DCRDEventType::Init(DCDBManer *dbm)
{
	m_dbm = dbm;
	m_pCondition = new DCAtCondition();
	if (0 != m_pCondition->Init(RENT_EVENT_TYPE_COND_INSET_ID,dbm))
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","DCRDEventType::Init m_pConditon error");
		return -1;
	}

    return 0;
}


int DCRDEventType::SetCurrTime(char *vi_sCurrTime)
{
	memset(m_sCurrTime,0,sizeof(m_sCurrTime));

	strcpy(m_sCurrTime,vi_sCurrTime);
	strncpy(m_sCurrMonFirstDay,m_sCurrTime,6);
    m_sCurrMonFirstDay[6] = '\0';
	strcat(m_sCurrMonFirstDay,"01000000");

	return 1;
}

/*****************************************************************************************
*输入：     无
*输出：     无
*返回：     成功返回触发的事件类型数，失败返回小于0，无触发事件类型返回等于0
*调用：     使用此类的程序
*描述：     判断取得需要触发的事件类型列表
*****************************************************************************************/
int DCRDEventType::JudgeEventType(long lnOfrInstId,long lnPrdInstId,string strEventEffDate,string strEventExpDate)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCRDEventType::JudgeEventType begin,lnOfrInstId[%ld],lnPrdInstId[%ld],strEventEffDate[%s],strEventExpDate[%s]",lnOfrInstId,lnPrdInstId,strEventEffDate.c_str(),strEventExpDate.c_str());

	m_iCurrResult = 0;
	m_iCountResult = 0;
	int nLatnId = DCDataCenter::instance()->m_nLatnId;


	m_iInstanceType = -lnOfrInstId;
	try
	{
		UDBSQL* pQuery = m_dbm->GetSQL("rent_event_type");
		if (NULL == pQuery)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCRDEventType::JudgeEventType","not find sql[rent_event_type]");
			return -1;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","JudgeEventType, Get SQL[rent_event_type] OK!");
		//分主产品和附属产品
		if(m_iInstanceType== FEE_TYPE_RENT)
		{
			//查询主产品租费事件类型
			pQuery->UnBindParam();
			pQuery->BindParam(1,nLatnId);
			pQuery->BindParam(2,RENT_EVENT_TYPE_CONTROL_RENT);
			pQuery->Execute();

			int nRet = NextEventTypeData(lnOfrInstId,pQuery);
			if (nRet <= 0)
			{
				DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCRDEventType::JudgeEventType","GET SERV EVENT_TYPE_ID:get data end when query latn_id[%d],then query latn_id[-1],lnPrdInstId[%ld]",nLatnId,lnPrdInstId);

				//如果没有查到指定本地网的数据，就查本地网为-1的数据
				pQuery->UnBindParam();
				pQuery->BindParam(1,-1);
				pQuery->BindParam(2,RENT_EVENT_TYPE_CONTROL_RENT);
				pQuery->Execute();
				nRet = NextEventTypeData(lnOfrInstId,pQuery);
				if (nRet <= 0)
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCRDEventType::JudgeEventType","GET SERV EVENT_TYPE_ID:get no data by latn_id[%d or -1] lnPrdInstId[%ld]",nLatnId,lnPrdInstId);
				}
			}
		}
		else if(m_iInstanceType == FEE_TYPE_PRODUCT)
		{
				//查询附属产品租费事件类型
				pQuery->UnBindParam();
				pQuery->BindParam(1,nLatnId);
				pQuery->BindParam(2,RENT_EVENT_TYPE_CONTROL_PRODUCT);

				pQuery->Execute();
				
				int nRet = NextEventTypeData(lnOfrInstId,pQuery);
				if (nRet <= 0)
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCRDEventType::JudgeEventType","GET SUB SERV EVENT_TYPE_ID:get data end when query latn_id[%d] has no data,then query latn_id[-1],lnPrdInstId[%ld]",nLatnId,lnPrdInstId);

					//如果没有查到指定本地网的数据，就查本地网为-1的数据
					pQuery->UnBindParam();
					pQuery->BindParam(1,-1);
					pQuery->BindParam(2,RENT_EVENT_TYPE_CONTROL_PRODUCT);
					pQuery->Execute();
					nRet = NextEventTypeData(lnOfrInstId,pQuery);
					if (nRet <= 0)
					{
						DCBIZLOG(DCLOG_LEVEL_DEBUG,-1,"DCRDEventType::JudgeEventType","GET SUB SERV EVENT_TYPE_ID:get no data by latn_id[%d or -1] lnPrdInstId[%ld]",nLatnId,lnPrdInstId);
					}
				}
		}
		else
		{
			m_stRentEventType[m_iCountResult].nRentEventTypeId = -1;
			m_stRentEventType[m_iCountResult].nEventTypeId = 900;
			strcpy(m_stRentEventType[m_iCountResult].sEffTime,strEventEffDate.c_str());
			strcpy(m_stRentEventType[m_iCountResult].sExpTime,strEventExpDate.c_str());
			++m_iCountResult;
			return 1;
		}
		pQuery->Close();

	} 
	catch(std::exception& e)
	{		
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCRDEventType::JudgeEventType","Get query [rent_event_type] failed: %s",e.what());
		return -1;
	}

	if (m_iCountResult <= 0)
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCRDEventType::JudgeEventType","DCRDEventType::The instance is not triggered by event type");
		return 0;
	}
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCRDEventType::JudgeEventType end,lnOfrInstId[%ld],lnPrdInstId[%ld],strEventEffDate[%s],strEventExpDate[%s],m_iCountResult[%d]",lnOfrInstId,lnPrdInstId,strEventEffDate.c_str(),strEventExpDate.c_str(),m_iCountResult);
	return m_iCountResult;
}

/*****************************************************************************************
*输入：     无
*输出：     无
*返回：     返回取得的记录个数
*调用：     使用此类的程序
*描述：     取记录
*****************************************************************************************/
int DCRDEventType::NextEventTypeData(long lnOfrInstId,UDBSQL* pQuery,bool i_hisflag )
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCRDEventType::NextEventTypeData begin,lnOfrInstId[%ld]",lnOfrInstId);

	char szTmpTime[16];
	strcpy(szTmpTime, DCDataCenter::instance()->sCycleEndTime);
	bool bMainEvent = false;
	bool bHangUp = false;
	try
	{
		//遍历取数据
		while(pQuery->Next())
		{
			STRentEventTypeData stEventTypeDta;
			memset(&stEventTypeDta,0,sizeof(stEventTypeDta));
			pQuery->GetValue(1,stEventTypeDta.nRentEventTypeId);
			pQuery->GetValue(2,stEventTypeDta.nUniteConditionId);
			pQuery->GetValue(3,stEventTypeDta.iControlFlag);
			pQuery->GetValue(4,stEventTypeDta.sEffDateCode);
			pQuery->GetValue(5,stEventTypeDta.sExpDateCode);
			pQuery->GetValue(6,stEventTypeDta.nEventTypeId);
			pQuery->GetValue(7,stEventTypeDta.sRealEffDateCode);
			pQuery->GetValue(8,stEventTypeDta.sRealExpDateCode);
			pQuery->GetValue(9,stEventTypeDta.sEffDate);
			pQuery->GetValue(10,stEventTypeDta.sExpDate);

			PublicLib::Trim(stEventTypeDta.sEffDateCode);
			PublicLib::Trim(stEventTypeDta.sExpDateCode);
			PublicLib::Trim(stEventTypeDta.sRealEffDateCode);
			PublicLib::Trim(stEventTypeDta.sRealExpDateCode);
			
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCRDEventType::NextEventTypeData","Event type id [%d].",stEventTypeDta.nEventTypeId);

			//如果时间不满足要求
			if (strcmp(szTmpTime, stEventTypeDta.sEffDate)<0
				||strcmp(szTmpTime, stEventTypeDta.sExpDate)>0)
			{
			
			    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","szTmpTime[%s] is not legal,sEffDate[%s],sExpDate[%s]",szTmpTime,stEventTypeDta.sEffDate,stEventTypeDta.sExpDate);
				continue;
			}

			if (bMainEvent && ( lnOfrInstId == -FEE_TYPE_PRODUCT ) )
			{			
			    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","bMainEvent is true and is sub ofr");
				break;
			}
			else if ( bMainEvent )
			{			
			    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","bMainEvent is true");
			    continue;
			}

			if ( m_pCondition->JudgeCond(stEventTypeDta.nUniteConditionId) )
			{
				if (stEventTypeDta.iControlFlag == 1 )
				{
					m_stRentEventType[m_iCountResult].nEventTypeId = stEventTypeDta.nEventTypeId;

					GetTimeFromDateCode(stEventTypeDta.sEffDateCode,m_stRentEventType[m_iCountResult].sEffTime);

					GetTimeFromDateCode(stEventTypeDta.sExpDateCode,m_stRentEventType[m_iCountResult].sExpTime);

					m_stRentEventType[m_iCountResult].nRentEventTypeId = stEventTypeDta.nRentEventTypeId;
					++m_iCountResult;

					if (m_iCountResult >= MAX_EVENT_STRATEGY)
					{
						DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCRDEventType::NextEventTypeData","DCRDEventType::The master policy is triggered beyond the Max[%d].",MAX_EVENT_STRATEGY);
						return -1;
					}
					
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCRDEventType::NextEventTypeData","UniteConditionId [%ld] RentEventTypeId[%ld]   Meet the conditions.",stEventTypeDta.nUniteConditionId,stEventTypeDta.nRentEventTypeId);
				}
				bMainEvent = true;

				//目前只取一个主事件
				break;
			}
			else
			{
			    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCAtCondition::JudgeCond false,Condition id :%ld",stEventTypeDta.nUniteConditionId);
			}
		}
	
	} 
	catch(std::exception& e)
	{		
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCRDEventType::NextEventTypeData","Do query [rent_event_type] failed: %s",e.what());
		return -1;
	}
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCRDEventType::NextEventTypeData end,lnOfrInstId[%ld],m_iCountResult[%d]",lnOfrInstId,m_iCountResult);
	return m_iCountResult;
}

/*****************************************************************************************
*输入：     v_pRentEventType：事件类型相关信息。
*输出：     无
*返回：     还有事件返回true，无事件则返回false
*调用：     使用此类的程序
*描述：     取得触发的事件类型相关信息，和JudgeEventType并用
*****************************************************************************************/
bool DCRDEventType::GetEventType(STRentEventType **v_pRentEventType)
{
	if (m_iCurrResult >= m_iCountResult)
	{
		return false;
	}

	*v_pRentEventType = &(m_stRentEventType[m_iCurrResult++]);

	return true;
}

/*****************************************************************************************
*输入：     v_sDateCode：日期定义代码
*输入：     v_iColId：日期列ID
*输入：     v_sTime：时间指针
*输出：     v_sTime：根据日期定义代码取得的时间，格式为YYYYMMDDHH24MISS
*返回：     取得的时间，代码无效返回NULL
*调用：     JudgeEventType
*描述：     根据日期定义代码取得对应的时间。
*****************************************************************************************/
char * DCRDEventType::GetTimeFromDateCode(const char *v_sDateCode, char *v_sTime)
{
	memset(v_sTime, 0x00, sizeof(v_sTime));

	if (strcmp(v_sDateCode, "CYCLE_BEGIN_DATE") == 0 || strcmp(v_sDateCode, "CYCLE_BEGIN_TIME") == 0)          //账期开始时间
	{
		strcpy(v_sTime, DCDataCenter::instance()->sCycleBeginTime);
	}
	else if (strcmp(v_sDateCode, "CYCLE_END_DATE") == 0 || strcmp(v_sDateCode, "CYCLE_END_TIME") == 0)         //账期结束时间
	{
		strcpy(v_sTime, DCDataCenter::instance()->sCycleEndTime);
	}
	else if(strcmp(v_sDateCode,"SYSTEM_LOCAL_DATE") == 0 ||strcmp(v_sDateCode,"SYSTEM_LOCAL_TIME") == 0)
	{
		strcpy(v_sTime,m_sCurrTime);

	}
	else if(strcmp(v_sDateCode,"SYSTEM_BEGIN_DATE") == 0 ||strcmp(v_sDateCode,"SYSTEM_BEGIN_TIME") == 0)
	{
		strcpy(v_sTime,m_sCurrMonFirstDay);
	}
	else if (strcmp(v_sDateCode, "INSTALL_DATE") == 0)	  
	{
		strcpy(v_sTime, m_szInstall);
	}
	else if (strcmp(v_sDateCode, "COMPLETED_DATE") == 0)		
	{
		strcpy(v_sTime, m_szCompleted);
	}
	else if(strcmp(v_sDateCode,"SUB_COMPLETED_DATE") == 0)
	{
		strcpy(v_sTime,m_szSubCompleted);
	}

	return v_sTime;
}

int  DCRDEventType::SetTimeInfo(StInstElementValue stInfo)
{
	memset(m_szInstall,0x00,sizeof(m_szInstall));
	memset(m_szCompleted,0x00,sizeof(m_szCompleted));
	memset(m_szSubCompleted,0x00,sizeof(m_szSubCompleted));
	
	strcpy(m_szInstall,stInfo.sInstallDate);
	strcpy(m_szCompleted,stInfo.sCompletedDate);
	strcpy(m_szSubCompleted,stInfo.sSubCompleted);

	m_pCondition->SetElementValue(stInfo);
}


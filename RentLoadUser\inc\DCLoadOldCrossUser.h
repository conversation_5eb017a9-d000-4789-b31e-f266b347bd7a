/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					ocs项目组
*All rights reserved.
*
*Filename：
*		DCRentLoadUser.h
*Indentifier：
*
*Description：
*		月租用户数据加载类
*Version：
*		V1.0
*Author:
*		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_LOADOLDCROSSUSER_H__
#define __DC_LOADOLDCROSSUSER_H__
#include <list>
#include <map>
#include <vector>
#include <string>
#include "DCBasePlugin.h"
#include "DCLoadUserBase.h"
#include "publiclib.h"
#include "DCEventTime.h"
#include "DCOBJSet.h"
#include "DCDataCenter.h"
#include "DCRDEventType.h"
#include "DCComboAdaptPrdInst.h"

#include "DCKpiSender.h"



class DCLoadOldCrossUser : public DCLoadUserBase
{
	public:	
		DCLoadOldCrossUser()        
		{
		}
		
		virtual ~DCLoadOldCrossUser();
		
		virtual int InitSub();
		virtual int process();
		virtual int processDcfs();

    private:
		int initCycEvtPro(map<long,STEventPro> &MapEvtType,int nProcId,int nProcType);
		bool IsNeedDeal(DCDateTime dtLast,DCDateTime dtcur,const int nSetHour);
		int SetLastDealTime(const DCDateTime cur_time,const STEventPro stEvn,bool isStart);
		int SetLastDealTimeDcfServ(const DCDateTime cur_time, const int nProType, const int nProcId, const int nLatnId, bool isStart);
		int CheckCrossAcctOfrInst(int nLatnId);
		int UpdateSpecialCrossAcct(int nLatnId);
		int UpdateDealState(const long lnPkid, const int nLatnID);
		int InitSubLatnId();
	private:

		std::list<string> m_ltLatn;

		int m_nLatnId;
		int m_nBillingCycleId;//此账期时间到天YYYYMMDD
		
		bool m_bCrossAcctChgFlag;
	};
#endif


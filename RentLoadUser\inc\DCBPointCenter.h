#ifndef _DCBPOINTCENTER_H
#define _DCBPOINTCENTER_H

#include <unistd.h>
#include <stdlib.h>
#include <string>
#include <map>
#include <vector>
#include <list>
#include "DCLogMacro.h"
#include "DCMon.h"
#include "DataDef.h"
#include "publiclib.h"



using namespace std;

struct STBPInput
{
    int st_ndelayms;
	int st_nportocol;
	int st_nBPFlag;
	string st_strAddr;
	STBPInput()
	{
		st_ndelayms = 0;
		st_nportocol = 0;
		st_nBPFlag = 0;
		st_strAddr = "";
	}
	void clear()
	{
		st_ndelayms = 0;
		st_nportocol = 0;
		st_nBPFlag = 0;
		st_strAddr = "";
	}
};

struct STMonHead
{
	std::string system;
	std::string subsys;
	std::string module;

	STMonHead():system(""),subsys(""),module(""){}

	bool operator == (const STMonHead&value)const
	{
		if(system == value.system && subsys == value.subsys && module == value.module)
		{
			return true;
		}
		return false;
	}

	bool operator < (const STMonHead&right) const
	{
		if(system < right.system)
		{
			return true;
		}
		else if(system == right.system)
		{
			if(subsys < right.subsys)
			{
				return true;
			}
			else if(subsys == right.subsys)
			{
				if(module < right.module)
				{
					return true;
				}
			}
		}
		return false;
	}

	STMonHead & operator =(const STMonHead & value)
	{
		system = value.system;
		subsys = value.subsys;
		module = value.module;
		return *this;
	}
	
};
	
class DCBPointCenter
{
public:
    ~DCBPointCenter(void);

    static DCBPointCenter* instance()
    {
        if(m_pBPointCenter == NULL)
        {
            m_pBPointCenter = new DCBPointCenter();	
        }
        return m_pBPointCenter;
    }


public:
    int Init(STBPInput v_InParam);
    int Release();
	DCMon* GetBPHandle(const string sys, const string subsys, const string module);
	/***预设置指标组头部信息***/
	void group_all_init(DCMon* phandler, const char* group_prefix,std::list<string> v_listLatn);
	void group_init(DCMon* phandler, const char* group_prefix, const char* Latn, char* group);
	void group_init(DCMon* phandler, const char* group_prefix, const int Latn, char* group);
	void GetFullGroup(const char* group_prefix, const int Latn, char* group);
	void GetFullGroup(const char* group_prefix, const char * Latn, char* group);
	/***设置指标组头部信息***/
	void group_set(DCMon* phandler, const char* group, const char* name, const char* value);

	/***设置单组指标增量值***/
	void cycle_inc(DCMon* phandler, const char* group, const char* k, const char* ki=0L, long value=1);

	/***设置单组指标当前值***/
	void cycle_set(DCMon* phandler, const char* group, const char* k, const char* ki=0L, long value=0);

	/***设置单组指标整数状态值***/
	void state_set(DCMon* phandler, const char* group, const char* k, const char* ki=0L, long value=0);

	/***设置单组指标浮点状态值***/
	void state_set(DCMon* phandler, const char* group, const char* k, const char* ki=0L, float value=0);

	/***设置多组指标增量值***/
	void cycle_array_inc(DCMon* phandler, const char* group, int id, const char* k, const char* ki=0L, long value=1);

	/***设置多组指标当前值***/
	void cycle_array_set(DCMon* phandler, const char* group, int id, const char* k, const char* ki=0L, long value=0);

	/***设置多组指标整数状态值***/
	void state_array_set(DCMon* phandler, const char* group, int id, const char* k, const char* ki=0L, long value=0);

	/***设置多组指标浮点状态值***/
	void state_array_set(DCMon* phandler, const char* group, int id, const char* k, const char* ki=0L, float value=0);

	/***设置多组指标增量值***/
	void cycle_array_inc(DCMon* phandler, const char* group, const char* id, const char* k, const char* ki=0L, long value=1);

	/***设置多组指标当前值***/
	void cycle_array_set(DCMon* phandler, const char* group, const char* id, const char* k, const char* ki=0L, long value=0);

	/***设置多组指标整数状态值***/
	void state_array_set(DCMon* phandler, const char* group, const char* id, const char* k, const char* ki=0L, long value=0);

	/***设置多组指标浮点状态值***/
	void state_array_set(DCMon* phandler, const char* group, const char* id, const char* k, const char* ki=0L, float value=0);
   
private:
	
public:
   
private:
    DCBPointCenter(void);
	int MonInit(DCMon* phandler, const string sys, const string subsys, const string module);
	
	
private:
    static DCBPointCenter *m_pBPointCenter;	//类的自身指针
    map<STMonHead,DCMon*> m_mapBPHandler; //指标类句柄
    int m_ndelayms;
	int m_nportocol;
	string m_strAddr;
	long  m_lPid;                                 //进程实际ID
	char m_cPid[32];
	char  m_cIpAddr[50];                                 //进程所在主机
	int m_nBPFlag;										//开关 默认为关
};
#endif //_DCBPOINTCENTER_H

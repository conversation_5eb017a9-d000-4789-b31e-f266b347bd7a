#include <stdlib.h>
#include <stdio.h>
#include <utility>
#include <cassert>
#include <cstdarg>
#include <string.h>
#include <unistd.h>
#include <pthread.h>

#include "DataDef.h"
#include "DCLogMacro.h"
#include "DCOBJSet.h"
#include "DCParseXml.h"
#include "DCMCastEvtFun.h"
#include "DCKpiSender.h"
#include "DCLoadOldUser.h"
#include "DCLoadOldCrossUser.h"
#include "DCWait.h"
#include "DCConf.h"
#include "DCFLocalServer.h"
#include "json/json.h"
#include "publiclib.h"
#include "DCLoadImmedUser.h"

using namespace std;
using namespace dsv;

bool g_bRcvCmd = false; 
bool g_exit = false;
int nProcNumDcfServ = 10;
vector<DCLoadUserBase *> vecThread;

void sig_deal_func(int sig)
{
	switch(sig)
	{
		case SIGTERM:
		case SIGINT:
		case SIGQUIT:
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","get quit sig");
			g_exit = true;
	        break;
		}
	}
}

int CheckCmd(int argc, char*argv[],STInputPara &inputParam)
{
    inputParam.clear();
	// 统一任务分发启动入参处理
	if(1 == argc)
	{
	    inputParam.bDsfStart = true;
        inputParam.nProcNum = 10;
		printf("Usage:%s [ProcNum]\n",argv[0]); 
		printf("\t[ProcNum]:处理线程总数未传入，取默认值10\n");
	}
    else if(2 == argc)
    {
	    inputParam.bDsfStart = true;
		int nRet = PublicLib::IsNumber(argv[1], -1);
		if(0 == nRet)
		{
			inputParam.nProcNum = atoi(argv[1]);
		}
		else
		{
			printf("Usage:%s [ProcNum]\n",argv[0]);
			printf("\t[ProcNum]:处理线程总数，请填写数字\n");
			return -1;
		}
    }
	// 原处理逻辑 不使用分发前台
	else if(argc == 3)
	{
	    inputParam.bDsfStart = false;
		inputParam.strLatn = argv[1];
		inputParam.nProType = atoi(argv[2]);
        inputParam.nProcNum = 10;
	}
	else if(argc == 4)
	{
	    inputParam.bDsfStart = false;
		inputParam.strLatn = argv[1];
		inputParam.nProType = atoi(argv[2]);
		inputParam.nProcNum = atoi(argv[3]);
		if(inputParam.nProType==PROC_TYPE_POST_CROSS_UPDATE && inputParam.nProcNum!=1)
		{
			printf("ProcType=3为跨账户更新流程，仅需一个进程!\n");
			printf("\tLatnIdListName: 处理的本地网ID列表,是配置文件中的配置项名称 \n");
			printf("\tProcType:=1普通流程,=2跨账户判断流程,=3跨账户更新流程(仅需一个进程),=4预付费流程,=9立即出账流程\n");
			printf("\t[ProcNum]:处理线程总数\n");
			return -1;
		}
		//nProcId = atoi(argv[5]);
		//if(nProcNum<=0 || nProcId<0 || nProcId>=nProcNum)
		if(inputParam.nProcNum<=0)
		{
			printf("ProcNum set error!\n");
			printf("\tLatnIdListName: 处理的本地网ID列表,是配置文件中的配置项名称 \n");
			printf("\tProcType:=1普通流程,=2跨账户判断流程,=3跨账户更新流程(仅需一个进程),=4预付费流程,=9立即出账流程\n");
			printf("\t[ProcNum]:处理线程总数\n");
			return -1;
		}
	}
	else
	{
		printf("请输入正确命令\n");
		printf("统一任务分发前台 分发任务Usage:%s [ProcNum]\n",argv[0]); 
		printf("原处理逻辑 不使用分发前台Usage:%s LatnIdListName ProcType [ProcNum]\n",argv[0]);
		printf("\tLatnIdListName: 处理的本地网ID列表,是配置文件中的配置项名称 \n");
		printf("\tProcType:=1普通流程,=2跨账户判断流程,=3跨账户更新流程(仅需一个进程),=4预付费流程,=9立即出账流程\n");
		printf("\t[ProcNum]:处理线程总数\n");
		return -1;
	}
    return 0;
}

int InitLog(DCMCastManer *m_mcm)
{
    //配置文件初始化
	const char* rentConfig = getenv("RENT_LOAD_CONFIG");
	if(DCParseXml::Instance()->Init("RentLoad",rentConfig) < 0)
	{
	 	printf("DCParseXml init failed! Xml file from env[RENT_LOAD_CONFIG].\n");
		return -1;
	}
	
	//日志初始化	
	const char* plogAddr = DCParseXml::Instance()->GetParam("logAddr","RentLoad/log");
    const char* pPrefix  = DCParseXml::Instance()->GetParam("prefix","RentLoad/log");
    const char* pModule  = DCParseXml::Instance()->GetParam("module","RentLoad/log");
	int    loglevel      = atoi(DCParseXml::Instance()->GetParam("level","RentLoad/log"));
		
	if(!plogAddr || !pPrefix || !pModule)
	{
	    printf("Get RentLoad/log config error!\n");
	    return -1;
	}
	if(loglevel<0 || loglevel >7)
        loglevel = 7;

    printf("logAddr[%s],Prefix[%s],Module[%s],loglevel[%d]!\n",plogAddr,pPrefix,pModule,loglevel);

	int nRet = DCLOGINIT(pPrefix,pModule,loglevel,plogAddr); 
	if(nRet)
	{
	 	printf("Init log system failed!\n");
		return -1;
	}
    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Init log system successful");

    //动态日志级别修改
	m_mcm = new DCMCastManer();
	int taskid = 0;
	const char* mcast = getenv("OCS_MCAST_CMD_ADDR");
	if(mcast)
	{	
		nRet = m_mcm->init(mcast);
		if(nRet < 0)
		{	
			DCBIZLOG(DCLOG_LEVEL_ERROR,0,"main","Init  DCMCastManer failed: %s", strerror(errno));
			return nRet;
		}
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"main","Init  DCMCastManer success");

		BoltEventFun evfun(m_mcm, NULL);
		evfun.register_event(pModule, taskid, "acct_rent_load_user");
	}

    return 0;
}

int InitKpiSender(STBPInput v_InParam)
{
	char szDelayMs[12] = {0};
	char szProtocol[12] = {0};
	char szKpiFlag[64] = {0};
	char szKpiAddr[64] = {0};
	sprintf(szDelayMs,"%d",v_InParam.st_ndelayms);
	sprintf(szProtocol,"%d",v_InParam.st_nportocol);
	sprintf(szKpiFlag,"%d",v_InParam.st_nBPFlag);
	sprintf(szKpiAddr,"%s",v_InParam.st_strAddr.c_str());
	DCKpiSender::instance()->SetParam("delay",    szDelayMs);
	DCKpiSender::instance()->SetParam("protocol", szProtocol);
	DCKpiSender::instance()->SetParam("flag",     szKpiFlag);
	DCKpiSender::instance()->SetParam("addr",     szKpiAddr);

	// 设置异常处理接口（可选）
	//DCKpiSender::instance()->SetErrorCallback(my_kpi_callback_exption, (DCKpiBolt*)this);

	if (DCKpiSender::instance()->Init() != 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "DCKpiSender init fail. error_code=%d, error_info=%s", DCKpiSender::instance()->ErrorCode(), DCKpiSender::instance()->ErrorInfo());
		return -1;
	}

}

//加载OpenKpi和灰度配置
int initConfigOther(STConfigPara& cfgPara,const char* latnName)
{
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","initConfig begin");
	char latnCfgName[50]={0};
	sprintf(latnCfgName,"RentLoad/%s",latnName);

    const char* szParamValue = NULL;

	//使用监控指标和稽核指标开关 0关闭 1开启
	szParamValue = DCParseXml::Instance()->GetParam("openKpi",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("openKpi","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/openKpi fail.");
	        return -1;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/openKpi: %s",szParamValue);
	        cfgPara.nOpenKpi = atoi(szParamValue);
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/openKpi: %s",latnCfgName,szParamValue);
        cfgPara.nOpenKpi = atoi(szParamValue);
	}

	//灰度路由订阅名
	szParamValue = DCParseXml::Instance()->GetParam("Subscriber",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("Subscriber","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/Subscriber  fail.");
	        return -1;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/Subscriber: %s",szParamValue);
	        cfgPara.sSubscriber = szParamValue;
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/Subscriber: %s",latnCfgName,szParamValue);
        cfgPara.sSubscriber = szParamValue;
	}
	//灰度路由环节名
	szParamValue = DCParseXml::Instance()->GetParam("RouteProcess",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("RouteProcess","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/RouteProcess  fail.");
	        return -1;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/RouteProcess: %s",szParamValue);
	        cfgPara.sRouteProcess = szParamValue;
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/RouteProcess: %s",latnCfgName,szParamValue);
        cfgPara.sRouteProcess = szParamValue;
	}
	
	//灰度路由刷新间隔
	szParamValue = DCParseXml::Instance()->GetParam("GrayRefreshIntr",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("GrayRefreshIntr","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/GrayRefreshIntr  fail.");
	        return -1;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/GrayRefreshIntr: %s",szParamValue);
	        cfgPara.nGrayRefreshIntr = atoi(szParamValue);
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/GrayRefreshIntr: %s",latnCfgName,szParamValue);
        cfgPara.nGrayRefreshIntr = atoi(szParamValue);
	}
}

//加载配置文件配置
int initConfig(STConfigPara& cfgPara,const char* latnName, const int nOpenKpi)
{
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","initConfig begin");
	char latnCfgName[50]={0};
	sprintf(latnCfgName,"RentLoad/%s",latnName);

    const char* szParamValue = NULL;
	int nValue = 0;

	//入参中本地网列表
    szParamValue = DCParseXml::Instance()->GetParam(latnName,"RentLoad/Latn");
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/Latn/%s fail.",latnName);
        return -1;
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/Latn/%s: %s",latnName,szParamValue);
        cfgPara.latnList = szParamValue;
	}

	//数据库重连时间间隔
    szParamValue = DCParseXml::Instance()->GetParam("ReconnectInterval",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("ReconnectInterval","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/ReconnectInterval fail，set default: 5");
	        cfgPara.nReconnectInterval = 5;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/ReconnectInterval: %s",szParamValue);
	        cfgPara.nReconnectInterval = atoi(szParamValue);
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/ReconnectInterval: %s",latnCfgName,szParamValue);
        cfgPara.nReconnectInterval = atoi(szParamValue);
	}
	//数据库重连次数
    szParamValue = DCParseXml::Instance()->GetParam("ReconnectTimes",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("ReconnectTimes","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/ReconnectTimes fail，set default: 3");
	        cfgPara.nReconnectTimes = 3;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/ReconnectTimes: %s",szParamValue);
	        cfgPara.nReconnectTimes = atoi(szParamValue);
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/ReconnectTimes: %s",latnCfgName,szParamValue);
        cfgPara.nReconnectTimes = atoi(szParamValue);
	}
	//所有流程结束后休眠时间
    szParamValue = DCParseXml::Instance()->GetParam("CycleSleepTime",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("CycleSleepTime","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/CycleSleepTime fail，set default: 60");
	        cfgPara.ncycleSleepTime = 60;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/CycleSleepTime: %s",szParamValue);
	        cfgPara.ncycleSleepTime = atoi(szParamValue);
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/CycleSleepTime: %s",latnCfgName,szParamValue);
        cfgPara.ncycleSleepTime = atoi(szParamValue);
	}

	//新装用户处理完后休眠多长时间再处理复机用户
    szParamValue = DCParseXml::Instance()->GetParam("NewUserSleepTime",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("NewUserSleepTime","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/NewUserSleepTime fail，set default: 10");
	        cfgPara.nNewUserSleepTime = 10;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/NewUserSleepTime: %s",szParamValue);
	        cfgPara.nNewUserSleepTime = atoi(szParamValue);
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/NewUserSleepTime: %s",latnCfgName,szParamValue);
        cfgPara.nNewUserSleepTime = atoi(szParamValue);
	}

	//复机用户处理完后休眠多长时间再处理老用户
    szParamValue = DCParseXml::Instance()->GetParam("FrozenUserSleepTime",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("FrozenUserSleepTime","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/FrozenUserSleepTime fail，set default: 10");
	        cfgPara.nFrozenUserSleepTime = 10;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/FrozenUserSleepTime: %s",szParamValue);
	        cfgPara.nFrozenUserSleepTime = atoi(szParamValue);
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/FrozenUserSleepTime: %s",latnCfgName,szParamValue);
        cfgPara.nFrozenUserSleepTime = atoi(szParamValue);
	}	

	//普通老用户处理完后休眠多长时间再处理跨账户的老用户
    szParamValue = DCParseXml::Instance()->GetParam("OldSleepTime",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("OldSleepTime","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/OldSleepTime fail，set default: 10");
	        cfgPara.nOldUserSleepTime = 10;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/OldSleepTime: %s",szParamValue);
	        cfgPara.nOldUserSleepTime = atoi(szParamValue);
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/OldSleepTime: %s",latnCfgName,szParamValue);
        cfgPara.nOldUserSleepTime = atoi(szParamValue);
	}	

	//大账户阈值设置
    szParamValue = DCParseXml::Instance()->GetParam("BigAcctThreshold",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("BigAcctThreshold","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/BigAcctThreshold fail.");
	        return -1;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/BigAcctThreshold: %s",szParamValue);
	        cfgPara.nBigAcctThreshold = atoi(szParamValue);
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/BigAcctThreshold: %s",latnCfgName,szParamValue);
        cfgPara.nBigAcctThreshold = atoi(szParamValue);
	}
	//特大账户阈值设置
    szParamValue = DCParseXml::Instance()->GetParam("MostBigAcctThreshold",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("MostBigAcctThreshold","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/MostBigAcctThreshold fail.");
	        return -1;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/MostBigAcctThreshold: %s",szParamValue);
	        cfgPara.nMostBigAcctThreshold = atoi(szParamValue);
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/MostBigAcctThreshold: %s",latnCfgName,szParamValue);
        cfgPara.nMostBigAcctThreshold = atoi(szParamValue);
	}
	//是否获取套餐实例
    szParamValue = DCParseXml::Instance()->GetParam("ContralGetOfrInst",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("ContralGetOfrInst","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/ContralGetOfrInst fail.");
	        return -1;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/ContralGetOfrInst: %s",szParamValue);
	        cfgPara.nContralGetOfrInst = atoi(szParamValue);
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/ContralGetOfrInst: %s",latnCfgName,szParamValue);
        cfgPara.nContralGetOfrInst = atoi(szParamValue);
	}

	//7天无理由退订开关,1为开,0为关
    const char* SevenCancelSwitch = DCParseXml::Instance()->GetParam("SevenCancelSwitch", "RentLoad");
    if(SevenCancelSwitch && strlen(SevenCancelSwitch) > 0 )
	{
        cfgPara.SevenCancelSwitch = atoi(SevenCancelSwitch);
	}
	else
	{
		cfgPara.SevenCancelSwitch = 0;
	}
	
	//是否获取明细信息，=0不获取，其他获取
    szParamValue = DCParseXml::Instance()->GetParam("DetailSource",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("DetailSource","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/DetailSource fail.");
	        return -1;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/DetailSource: %s",szParamValue);
	        cfgPara.nGetDetail = atoi(szParamValue);
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/DetailSource: %s",latnCfgName,szParamValue);
        cfgPara.nGetDetail = atoi(szParamValue);
	}
	//新装流程中一条消息中发送的套餐数，因优先级一致需在同一条消息中发送，实际数可能超过配置数
    szParamValue = DCParseXml::Instance()->GetParam("NewUserInstSendBatN",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("NewUserInstSendBatN","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/NewUserInstSendBatN fail.");
	        return -1;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/NewUserInstSendBatN: %s",szParamValue);
	        cfgPara.nNewUserInstSendBatN = atoi(szParamValue);
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/NewUserInstSendBatN: %s",latnCfgName,szParamValue);
        cfgPara.nNewUserInstSendBatN = atoi(szParamValue);
	}
	//feecache记录超时时间，单位分钟
    szParamValue = DCParseXml::Instance()->GetParam("FeecacheTimeOutSec",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("FeecacheTimeOutSec","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/FeecacheTimeOutSec fail.");
	        return -1;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/FeecacheTimeOutSec: %s",szParamValue);
	        cfgPara.nFeecacheTimeOutSec = atoi(szParamValue);
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/FeecacheTimeOutSec: %s",latnCfgName,szParamValue);
        cfgPara.nFeecacheTimeOutSec = atoi(szParamValue);
	}
	//定时流程中一条消息中发送的套餐数，因优先级一致需在同一条消息中发送，实际数可能超过配置数
    szParamValue = DCParseXml::Instance()->GetParam("InstSendBatchNum",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("InstSendBatchNum","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/InstSendBatchNum fail.");
	        return -1;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/InstSendBatchNum: %s",szParamValue);
	        cfgPara.nInstSendBatNum = atoi(szParamValue);
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/InstSendBatchNum: %s",latnCfgName,szParamValue);
        cfgPara.nInstSendBatNum = atoi(szParamValue);
	}

	//拓扑图服务地址
    szParamValue = DCParseXml::Instance()->GetParam("ZKServerAddr",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("ZKServerAddr","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/ZKServerAddr fail.");
	        return -1;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/ZKServerAddr: %s",szParamValue);
	        cfgPara.strZKAddr = szParamValue;
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/ZKServerAddr: %s",latnCfgName,szParamValue);
        cfgPara.strZKAddr = szParamValue;
	}
	//拓扑图服务名
    szParamValue = DCParseXml::Instance()->GetParam("ZKServerName",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("ZKServerName","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/ZKServerName fail.");
	        return -1;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/ZKServerName: %s",szParamValue);
	        cfgPara.strZKServName = szParamValue;
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/ZKServerName: %s",latnCfgName,szParamValue);
        cfgPara.strZKServName = szParamValue;
	}
	//拓扑图服务根目录
    szParamValue = DCParseXml::Instance()->GetParam("ZKRootDir",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("ZKRootDir","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/ZKRootDir fail.");
	        return -1;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/ZKRootDir: %s",szParamValue);
	        cfgPara.strZKRootDir = szParamValue;
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/ZKRootDir: %s",latnCfgName,szParamValue);
        cfgPara.strZKRootDir = szParamValue;
	}
	//拓扑图服务登录密码
    szParamValue = DCParseXml::Instance()->GetParam("ZKServLoginPwd",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("ZKServLoginPwd","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/ZKServLoginPwd fail.");
	        return -1;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/ZKServLoginPwd: %s",szParamValue);
	        cfgPara.strZKLoginPwd = szParamValue;
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/ZKServLoginPwd: %s",latnCfgName,szParamValue);
        cfgPara.strZKLoginPwd = szParamValue;
	}
	//拓扑图服务解析密码
    szParamValue = DCParseXml::Instance()->GetParam("ZKServAesPwd",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("ZKServAesPwd","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/ZKServAesPwd fail.");
	        return -1;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/ZKServAesPwd: %s",szParamValue);
	        cfgPara.strZKAesPwd = szParamValue;
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/ZKServAesPwd: %s",latnCfgName,szParamValue);
        cfgPara.strZKAesPwd = szParamValue;
	}

	//拓扑图服务日志目录
    szParamValue = DCParseXml::Instance()->GetParam("ZKDcfLogDir",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("ZKDcfLogDir","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/ZKDcfLogDir fail.");
	        return -1;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/ZKDcfLogDir: %s",szParamValue);
	        cfgPara.strZKDcfLogDir = szParamValue;
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/ZKDcfLogDir: %s",latnCfgName,szParamValue);
        cfgPara.strZKDcfLogDir = szParamValue;
	}
	//拓扑图服务日志级别
    szParamValue = DCParseXml::Instance()->GetParam("ZKDcfLogLevel",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("ZKDcfLogLevel","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/ZKDcfLogLevel fail.");
	        return -1;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/ZKDcfLogLevel: %s",szParamValue);
	        cfgPara.nZKDcfLogLevel = atoi(szParamValue);
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/ZKDcfLogLevel: %s",latnCfgName,szParamValue);
        cfgPara.nZKDcfLogLevel = atoi(szParamValue);
	}
	//向拓扑图传递的分流字段名
    szParamValue = DCParseXml::Instance()->GetParam("ZKHashKey",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("ZKHashKey","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/ZKHashKey fail.");
	        return -1;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/ZKHashKey: %s",szParamValue);
	        cfgPara.strHashKey = szParamValue;
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/ZKHashKey: %s",latnCfgName,szParamValue);
        cfgPara.strHashKey = szParamValue;
	}
	//发送消息时是否进行流速控制
    szParamValue = DCParseXml::Instance()->GetParam("FlowControl",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("FlowControl","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/FlowControl fail.");
	        return -1;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/FlowControl: %s",szParamValue);
	        cfgPara.nFlowControl = atoi(szParamValue);
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/FlowControl: %s",latnCfgName,szParamValue);
        cfgPara.nFlowControl = atoi(szParamValue);
	}
	//发送一条消息后休眠时间，用于控制消息流速
    szParamValue = DCParseXml::Instance()->GetParam("FlowSleepUs",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("FlowSleepUs","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/FlowSleepUs fail.");
	        return -1;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/FlowSleepUs: %s",szParamValue);
	        cfgPara.nFlowSleepUs = atoi(szParamValue);
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/FlowSleepUs: %s",latnCfgName,szParamValue);
        cfgPara.nFlowSleepUs = atoi(szParamValue);
	}	
	
	//定时处理模块消息发送积压队列大小
    szParamValue = DCParseXml::Instance()->GetParam("FlowQueueSize",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("FlowQueueSize","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/FlowQueueSize fail.");
	        return -1;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/FlowQueueSize: %s",szParamValue);
	        cfgPara.nQueueSizeOldUser = atoi(szParamValue);
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/FlowQueueSize: %s",latnCfgName,szParamValue);
        cfgPara.nQueueSizeOldUser = atoi(szParamValue);
	}
	//新装处理模块消息发送积压队列大小
    szParamValue = DCParseXml::Instance()->GetParam("NewUserFlowQSize",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("NewUserFlowQSize","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/NewUserFlowQSize fail.");
	        return -1;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/NewUserFlowQSize: %s",szParamValue);
	        cfgPara.nQueueSizeNewUser = atoi(szParamValue);
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/NewUserFlowQSize: %s",latnCfgName,szParamValue);
        cfgPara.nQueueSizeNewUser = atoi(szParamValue);
	}

	//复机处理模块消息发送积压队列大小
    szParamValue = DCParseXml::Instance()->GetParam("FrozenUserFlowQSize",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("FrozenUserFlowQSize","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/FrozenUserFlowQSize fail.");
	        return -1;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/FrozenUserFlowQSize: %s",szParamValue);
	        cfgPara.nQueueSizeFrozenUser = atoi(szParamValue);
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/FrozenUserFlowQSize: %s",latnCfgName,szParamValue);
        cfgPara.nQueueSizeFrozenUser = atoi(szParamValue);
	}
	//消息发送多少时间后记为超时，超时定义配置
    szParamValue = DCParseXml::Instance()->GetParam("FlowTimeOut",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("FlowTimeOut","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/FlowTimeOut fail.");
	        return -1;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/FlowTimeOut: %s",szParamValue);
	        cfgPara.nFlowTimeOut = atoi(szParamValue);
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/FlowTimeOut: %s",latnCfgName,szParamValue);
        cfgPara.nFlowTimeOut = atoi(szParamValue);
	}


	//延时
	szParamValue = DCParseXml::Instance()->GetParam("KpiDelayMs",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("KpiDelayMs","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/KpiDelayMs fail.");
	        return -1;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/KpiDelayMs: %s",szParamValue);
	        cfgPara.nBPdelayms = atoi(szParamValue);
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/KpiDelayMs: %s",latnCfgName,szParamValue);
        cfgPara.nBPdelayms = atoi(szParamValue);
	}

	//协议
	szParamValue = DCParseXml::Instance()->GetParam("KpiPortocol",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("KpiPortocol","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/KpiPortocol fail.");
	        return -1;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/KpiPortocol: %s",szParamValue);
	        cfgPara.nBPportocol = atoi(szParamValue);
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/KpiPortocol: %s",latnCfgName,szParamValue);
        cfgPara.nBPportocol = atoi(szParamValue);
	}
	//开关
	szParamValue = DCParseXml::Instance()->GetParam("KpiFlag",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("KpiFlag","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/KpiFlag fail.");
	        return -1;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/KpiFlag: %s",szParamValue);
	        cfgPara.nBPFlag = atoi(szParamValue);
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/KpiFlag: %s",latnCfgName,szParamValue);
        cfgPara.nBPFlag = atoi(szParamValue);
	}
	//埋点地址
    szParamValue = DCParseXml::Instance()->GetParam("KpiAddr",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("KpiAddr","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/KpiAddr fail.");
	        return -1;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/KpiAddr: %s",szParamValue);
	        cfgPara.strBPAddr = szParamValue;
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/KpiAddr: %s",latnCfgName,szParamValue);
        cfgPara.strBPAddr = szParamValue;
	}
	
	//跨账户 ModGroupId 变更开关
	szParamValue = DCParseXml::Instance()->GetParam("CorssChange",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("CorssChange","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/CorssChange default.");
	        cfgPara.nCrossAcctChgFlag = 0;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/CorssChange: %s",szParamValue);
	        cfgPara.nCrossAcctChgFlag = atoi(szParamValue);
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/CorssChange: %s",latnCfgName,szParamValue);
        cfgPara.nCrossAcctChgFlag = atoi(szParamValue);
	}

	//直接发送消息的最大size，超过则走DCA传送
	szParamValue = DCParseXml::Instance()->GetParam("BigAcctMsgSize",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("BigAcctMsgSize","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/BigAcctMsgSize default 1M.");
	        cfgPara.nBigAcctMsgSize = 1024*1024;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/BigAcctMsgSize: %s",szParamValue);
	        cfgPara.nBigAcctMsgSize = atoi(szParamValue);
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/BigAcctMsgSize: %s",latnCfgName,szParamValue);
        cfgPara.nBigAcctMsgSize = atoi(szParamValue);
	}

	//走DCA传送的记录，需要分条插入，每条记录的最大size
	szParamValue = DCParseXml::Instance()->GetParam("DcaCacheMsgSize",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("DcaCacheMsgSize","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/DcaCacheMsgSize default 512K.");
	        cfgPara.nDcaCacheMsgSize = 512*1024;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/DcaCacheMsgSize: %s",szParamValue);
	        cfgPara.nDcaCacheMsgSize = atoi(szParamValue);
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/DcaCacheMsgSize: %s",latnCfgName,szParamValue);
        cfgPara.nDcaCacheMsgSize = atoi(szParamValue);
	}

	//在配置表中增加<param name="TrialDay">25</param><!---在每月的多少号（例如25号）对用户下个月的租费进行试算-->		
	//配置每月试算下个月用户租费日期
	szParamValue = DCParseXml::Instance()->GetParam("TrialDay",latnCfgName);
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
	    szParamValue = DCParseXml::Instance()->GetParam("TrialDay","RentLoad");
		if(szParamValue==NULL || strcmp(szParamValue,"")==0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/TrialDay  fail，set default: 20.");
	        cfgPara.nTrialDay = 20;//ssps_trial，这块需要注意的是，设置一个值，还是return -1;
		}
		else
		{
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/TrialDay: %s",szParamValue);
	        cfgPara.nTrialDay = atoi(szParamValue);
		}
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get %s/TrialDay: %s",latnCfgName,szParamValue);
        cfgPara.nTrialDay = atoi(szParamValue);
	}

	if (nOpenKpi)
	{
		//普通流程统计指标
		DCKpiMon* tmptrMon = NULL; 
		string latnId = cfgPara.latnList;
		std::list<string> tmpLatn;
		tmpLatn.clear();
		PublicLib::SplitStr(latnId.c_str(),'|',tmpLatn);
		tmptrMon = DCKpiSender::instance()->GetKPIHandle("BILLING", "REC", "RENDLOAD_B");
		DCKpiSender::instance()->group_all_init(tmptrMon, "TraceKpi", tmpLatn);
	}
	cfgPara.nOpenKpi = nOpenKpi;

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "","initConfig end");
	return 0;
}

int initConfigAll(map<string, STConfigPara> &m_mapLatnCfg,map<int,string> &m_mapLatnList, 
					string &strZookeeperAddr, string &strAESPasswd, int &nMaxGetTaskNum)
{
	int nRet = 0, nOpenKpi = 0;
	const char* szParamValue = NULL;
	STConfigPara STtemp;
	STConfigPara cfgPara;

    //任务分发中心
    szParamValue = DCParseXml::Instance()->GetParam("dcfServerZK/ZookeeperAddr", "RentLoad");
    if(!szParamValue || 0 == strlen(szParamValue))
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"","read dcfServerZK/ZookeeperAddr fail");
		return -1;
    }
	else
	{
		strZookeeperAddr = szParamValue;
	}

	szParamValue = DCParseXml::Instance()->GetParam("dcfServerZK/AESPasswd", "RentLoad");
    if(!szParamValue || 0 == strlen(szParamValue))
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"","read dcfServerZK/AESPasswd fail");
		return -1;
    }
    else
	{
        strAESPasswd = szParamValue;
	}

	szParamValue = DCParseXml::Instance()->GetParam("dcfServerZK/MaxGetTaskNum", "RentLoad");
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get dcfServerZK/MaxGetTaskNum fail.");
		return -1;
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get dcfServerZK/MaxGetTaskNum: %s", szParamValue);
        nMaxGetTaskNum = atoi(szParamValue);
	}

	//灰度路由订阅名
	szParamValue = DCParseXml::Instance()->GetParam("Subscriber","RentLoad");
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/Subscriber  fail.");
		return -1;
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/Subscriber: %s",szParamValue);
		cfgPara.sSubscriber = szParamValue;
	}

	//灰度路由环节名
	szParamValue = DCParseXml::Instance()->GetParam("RouteProcess","RentLoad");
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/RouteProcess  fail.");
		return -1;
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/RouteProcess: %s",szParamValue);
		cfgPara.sRouteProcess = szParamValue;
	}
	
	//灰度路由刷新间隔
	szParamValue = DCParseXml::Instance()->GetParam("GrayRefreshIntr","RentLoad");
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/GrayRefreshIntr  fail.");
		return -1;
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/GrayRefreshIntr: %s",szParamValue);
		cfgPara.nGrayRefreshIntr = atoi(szParamValue);
	}

	//灰度刷新线程(只能有一个与<Latn>无关)  --灰度单例模式刷新模式屏蔽，用多线程模式版本刷新
	/*
	nRet = DCConf::Instance()->Init(cfgPara);
	if (nRet < 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "","Init gray route failed!");
		return -1;
	}
	nRet = DCConf::Instance()->start();
	if (nRet < 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "","start gray route thread failed!");
		return -1;
	}
	*/

	//使用监控指标和稽核指标开关 0关闭 1开启
	szParamValue = DCParseXml::Instance()->GetParam("openKpi", "RentLoad");
	if(szParamValue==NULL || strcmp(szParamValue,"")==0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "initConfig","Get RentLoad/openKpi fail.");
		return -1;
	}
	else
	{
        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "initConfig","Get RentLoad/openKpi: %s", szParamValue);
        nOpenKpi = atoi(szParamValue);
	}

	// 遍历 <Latn>	
	map<string, string> mapLatnTemp;
	mapLatnTemp.clear();
	nRet = DCParseXml::Instance()->GetChild("RentLoad/Latn", mapLatnTemp);
	if(nRet < 0)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","Latn GetChild error!");
		return -1;
	}

    std::list<string> tmpLatn,tmpLatnAll;
    std::list<string>::iterator itrLs;
    tmpLatn.clear();
    tmpLatnAll.clear();
	for(map<string, string>::iterator iter = mapLatnTemp.begin(); iter != mapLatnTemp.end(); )
	{
		initConfig(STtemp, iter->first.c_str() ,nOpenKpi);
        
		//补充公共参数
		STtemp.sRouteProcess= cfgPara.sRouteProcess;
		STtemp.nGrayRefreshIntr= cfgPara.nGrayRefreshIntr;
		STtemp.sSubscriber= cfgPara.sSubscriber;
        
        cfgPara.nBPdelayms = STtemp.nBPdelayms;
        cfgPara.nBPportocol = STtemp.nBPportocol;
        cfgPara.nBPFlag = STtemp.nBPFlag;
        cfgPara.strBPAddr = STtemp.strBPAddr;
        cfgPara.strZKDcfLogDir = STtemp.strZKDcfLogDir;
        cfgPara.nZKDcfLogLevel = STtemp.nZKDcfLogLevel;
        
        tmpLatn.clear();
        PublicLib::SplitStr((iter->second).c_str(),'|',tmpLatn);
        for(itrLs = tmpLatn.begin();itrLs != tmpLatn.end();++itrLs)
        {
            tmpLatnAll.push_back(*itrLs);
            m_mapLatnList.insert(pair<int,string>(atoi((*itrLs).c_str()),iter->first));
        }

		m_mapLatnCfg.insert(pair<string, STConfigPara>(iter->first, STtemp));
		iter++;
	}

    //kpi组件初始化//kpi是单实例的，所以一个进程只支持一种配置
    if (cfgPara.nOpenKpi)
    {
        //在插件加载初始化之前 初始化DCBPointCenter
        STBPInput v_InParam;
        v_InParam.st_ndelayms = cfgPara.nBPdelayms;
        v_InParam.st_nportocol = cfgPara.nBPportocol;
        v_InParam.st_nBPFlag = cfgPara.nBPFlag;
        v_InParam.st_strAddr = cfgPara.strBPAddr;
        if (0 != InitKpiSender(v_InParam))
        {
            DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "kpisender init fail.");
            return -1;
        }
        DCKpiMon* tmptrMon = DCKpiSender::instance()->GetKPIHandle("BILLING", "REC", "RENDLOAD_B");
        DCKpiSender::instance()->group_all_init(tmptrMon, "TraceKpi", tmpLatnAll);
    }

    //服务化调用相关配置初始化
    dcf_new::DCFClientLog::init(cfgPara.strZKDcfLogDir.c_str());
    dcf_new::DCFClientLog::setLogLevel(cfgPara.nZKDcfLogLevel);
	return 0;
}

void SplitString(const std::string& str, char sep, std::vector<std::string>& vec)
{
	size_t pos = 0;
	size_t prev = 0;
	std::string sub;
	bool brun = true;
	vec.clear();
	while(brun)
	{
		pos = str.find(sep, prev);
        if(pos == std::string::npos)
		{
			sub = str.substr(prev);
			brun = false;
		}
		else
		{
			sub = str.substr(prev, pos-prev);
			prev = pos+1;
		}
		vec.push_back(sub);
	}
}

int ParseMsg(const string &strMsg, STInputPara &inputPara)
{
    Json::Value jRoot;
	Json::Reader jReader;

    //string strMsg = "{\"cycle_id\":\"\",\"latn_id\":\"\",\"uuuid\":\"1131725b-ec5d-4ae7-859f-03d7de22c53d\",\"task_params\":\"1001|2001|7625|/project/bill03/testbill/sourcefile/galen/DA3G/7625/AAA_plj_test.551.TXT|/project/bill03/testbill/sourcefile/galen/DA3G/7625/|888|7625|7625|04/|20210426102711|DA3G|0|-1|ABCEF|-1|1|0|102923\",\"task_type\":\"\",\"task_timestamp\":\"20210714185439\"}";

    if(!jReader.parse(strMsg.c_str(), jRoot))
    {
        DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "","ParseMessage failed.");
        return -1;
    }

	std::vector<string> vecTemp;
    string strSQLFields = jRoot["task_params"].asCString();
    SplitString(strSQLFields, '|', vecTemp);

    //latn_id|cycle_id|task_type|task_params
    if(vecTemp.size() >= 3)
    {
		inputPara.nLatnID = atoi(vecTemp[0].c_str());
		inputPara.nProType = atoi(vecTemp[2].c_str());
		inputPara.strLatn = vecTemp[0];
		inputPara.vecSQLFields.assign(vecTemp.begin()+3, vecTemp.end());
    }
    else
    {
        printf("task_params size %d.", vecTemp.size());
        return -1;
    }
	return 0;
}

int CheckTimeThread(STInputPara &inputPara)
{
	int statueTask = 0, statueRun = 0, i = 0;
	int nRet = THREAD_AVAILABLE;

	switch (inputPara.nProType)
	{
	case PROC_TYPE_POST_NORAML: //定时任务,后付老用户,后一半线程
	case PROC_TYPE_PRE_NORMAL: //定时任务,预付老用户,后一半线程
	case PROC_TYPE_TRIAL_USER: //定时任务,预付试算用户,后一半线程
		for(i = inputPara.nProcNum + 1; i < inputPara.nProcNum*2 + 1; i++)
		{
			statueTask = vecThread[i]->GetTaskState();
			statueRun = vecThread[i]->GetRunState();

			if(statueTask != THREAD_FREE || statueRun != THREAD_AVAILABLE)
			{
				nRet = THREAD_DISABLED;
				break; //任意线程不可用，则任务不可执行
			}
		}
		break;
	case PROC_TYPE_POST_CORSS_JUDGE: //后付费跨账户判断流程(定时任务,前一半线程)
		for(i = 0; i < inputPara.nProcNum; i++)
		{
			statueTask = vecThread[i]->GetTaskState();
			statueRun = vecThread[i]->GetRunState();

			if(statueTask != THREAD_FREE || statueRun != THREAD_AVAILABLE)
			{
				nRet = THREAD_DISABLED;
				break; //任意线程不可用，则任务不可执行
			}
		}
		break;
	default:
		break;
	}

	return nRet;
}

int DealTask(STInputPara &inputPara)
{
	int statueTask = 0, statueRun = 0, i = 0, nRet = 0;

	switch (inputPara.nProType)
	{
	case PROC_TYPE_POST_NORAML: //定时任务,后付老用户,后一半线程
	case PROC_TYPE_PRE_NORMAL: //定时任务,预付老用户,后一半线程
	case PROC_TYPE_TRIAL_USER: //定时任务,预付试算用户,后一半线程
		for(i = inputPara.nProcNum + 1; i < inputPara.nProcNum*2 + 1; i++)
		{
			statueTask = vecThread[i]->GetTaskState();
			statueRun = vecThread[i]->GetRunState();

			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"main","nProType[%d]i[%d]statueTask[%d]statueRun[%d]", 
					inputPara.nProType, i, statueTask, statueRun);

			if(THREAD_FREE == statueTask && THREAD_AVAILABLE == statueRun)
			{
				vecThread[i]->setParams(inputPara);
				vecThread[i]->SetTaskState(1);
				continue;
			}

			if(inputPara.nProcNum*2 == i)
			{
				//线程全忙
				nRet = -1;
			}
		}
		break;
	case PROC_TYPE_POST_CORSS_JUDGE: //定时任务,前一半线程
		for(i = 0; i < inputPara.nProcNum; i++)
		{
			statueTask = vecThread[i]->GetTaskState();
			statueRun = vecThread[i]->GetRunState();

			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"main","nProType[%d]i[%d]statueTask[%d]statueRun[%d]", 
					inputPara.nProType, i, statueTask, statueRun);

			if(THREAD_FREE == statueTask && THREAD_AVAILABLE == statueRun)
			{
				vecThread[i]->setParams(inputPara);
				vecThread[i]->SetTaskState(1);
				continue;
			}

			if(inputPara.nProcNum - 1 == i)
			{
				//线程全忙
				nRet = -1;
			}
		}
		break;
	case PROC_TYPE_POST_CROSS_UPDATE: //定时任务, inputPara.nProcNum号线程负责
		statueTask = vecThread[inputPara.nProcNum]->GetTaskState();
		statueRun = vecThread[inputPara.nProcNum]->GetRunState();

		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"main","nProType[%d]i[%d]statueTask[%d]statueRun[%d]", 
					inputPara.nProType, inputPara.nProcNum, statueTask, statueRun);

		if(THREAD_FREE == statueTask && THREAD_AVAILABLE == statueRun)
		{
			vecThread[inputPara.nProcNum]->setParams(inputPara);
			vecThread[inputPara.nProcNum]->SetTaskState(1);
		}
		else
		{
			//线程忙
			nRet = -1;
		}
		break;
	case PROC_TYPE_INST_CHANGE:
	case PROC_TYPE_TRANSFER_PRD:
	case PROC_TYPE_FREZEN_USER: //SQL任务, 单线程, 且只能用后一半线程
		for(i = inputPara.nProcNum + 1; i < inputPara.nProcNum*2 + 1; i++)
		{
			statueTask = vecThread[i]->GetTaskState();
			statueRun = vecThread[i]->GetRunState();

			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"main","nProType[%d]i[%d]statueTask[%d]statueRun[%d]", 
					inputPara.nProType, i, statueTask, statueRun);

			if(THREAD_FREE == statueTask && THREAD_AVAILABLE == statueRun)
			{
				vecThread[i]->setParams(inputPara);
				vecThread[i]->SetTaskState(1);
				break;
			}

			if(inputPara.nProcNum*2 == i)
			{
				//线程全忙
				nRet = -1;
			}
		}
		break;
	default:
		break;
	}

	return nRet;
}

class RegisterHandlerImp : public RegisterHandler
{
    public:
    std::string dependent(){
        return "";
    }

    bool state(long load, long hand, int &level)
    {
        double rate = hand * 1.00f / load;
        if (rate > 0.95)
        {
            level = 5;
            return true;
        }
        else if (rate > 0.8)
        {
            level = 5;
            return true;
        }
        else if (rate > 0.5)
        {
            level = 3;
        }
        else if (rate > 0.3)
        {
            level = 2;
        }
        else
        {
            level = 1;
        }
        return false;
    };
};

class TimeSerHandler : public ServiceHandler
{
public:
    int callback(const std::string &uuid, const std::string &param, std::string &response)
    {
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"callback","$$$$$$$$ recv msg{uuid=%s, len=%d} => {%s}", uuid.c_str(), param.length(), param.c_str());

		int nRet = 0;
		STInputPara inputPara;
		long long curTime = rd_clock();

		inputPara.clear();
		if(ParseMsg(param, inputPara) != 0)
		{
			//消息解析失败
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "callback", "ParseMsg error");
			return -1;
		}
		inputPara.nProcNum = nProcNumDcfServ;

		//不区分具体业务线程，定时任务独占所有资源
		g_bRcvCmd = true;

		while(true)
		{
			nRet = CheckTimeThread(inputPara);
			if(THREAD_AVAILABLE == nRet)
			{
				nRet = DealTask(inputPara);
				if(-1 == nRet)
				{
					//没有空闲线程
					DCBIZLOG(DCLOG_LEVEL_ERROR,0,"callback","DealTask error");
					sleep(1);
					continue;
				}
				break;
			}
			usleep(1000);
		}

		while(true)
		{
			nRet = CheckTimeThread(inputPara);
			if(THREAD_AVAILABLE == nRet)
			{
				//所占用线程均空闲
				g_bRcvCmd = false;
				break;
			}
			else
			{
				//等待线程处理已有任务
				usleep(1000);
			}
		}

		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"callback","g_bRcvCmd[%d], cmd over", g_bRcvCmd);
		return 0;
	}

    unsigned long long rd_clock()
    {
        struct timeval tv;
        gettimeofday(&tv, NULL);
        return ((unsigned long long)tv.tv_sec * 1000000LLU) + (unsigned long long)tv.tv_usec;
    }
};

int main(int argc, char*argv[])
{
	int nRet = 0;
	int nMaxGetTaskNum=0;
	bool isInitFail=false;
	string strAESPasswd = "";
	string strZookeeperAddr = "";
	map<string, STConfigPara> m_mapLatnCfg;//<服务分类, 配置项>
	map<int, string> m_mapLatnList;//<本地网ID, 服务分类>
    STInputPara inputParam;
    
	if(CheckCmd(argc,argv,inputParam)<0)
        return -1;
    nProcNumDcfServ = inputParam.nProcNum;

    DCMCastManer *m_mcm = NULL;
    if(InitLog(m_mcm)<0)
        return -1;

	if(inputParam.bDsfStart)
	{
		//全配置加载
		nRet = initConfigAll( m_mapLatnCfg,m_mapLatnList, strZookeeperAddr, strAESPasswd, nMaxGetTaskNum);
		if(nRet<0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "","initConfigAll fail.");
			return -1;
		}

		///////////////准备工作完成，业务开始//////////////
	    //服务对象实例化
	    RegisterHandlerImp regist;
	    DCFLocalServer server(&regist, nMaxGetTaskNum, false);

	    // 继承同步回调接口
	    TimeSerHandler *pCallback = new TimeSerHandler();
	    server.bindHandler(pCallback); //注册同步调用接口

	    //服务启动
		const char *pZookeeperAddr = strZookeeperAddr.c_str();
	    if (0 > server.start(pZookeeperAddr, strAESPasswd))
	    {
	        //printf("register service failed{zook:%s}.\n", pZookeeperAddr);
	        DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "","register service failed{zook:%s}.", pZookeeperAddr);
	        return -1;
	    }

		//线程启动
		int idx = 0;
		int iLoop = 0;
        bool bNeedCall = false;
		for(iLoop; iLoop < nProcNumDcfServ*2+1; iLoop++)
		{
			DCLoadUserBase *pThread;
			if(iLoop < nProcNumDcfServ)
			{
				// 2 后付费跨账户判断流程(前一半线程)
				idx = iLoop;
				pThread = new DCLoadOldCrossUser();
                bNeedCall = false;
			}
			else if(iLoop == nProcNumDcfServ)
			{
				// 3 后付费跨账户更新流程(独占线程)
				idx = 0;
				pThread = new DCLoadOldCrossUser();
                bNeedCall = false;
			}
			else
			{
				// 1,4,5,6,7 (后一半线程)
				idx = iLoop - nProcNumDcfServ - 1;
				pThread = new DCLoadOldUser();
                bNeedCall = true;
			}

			pThread->setStartMode(true);//统一任务分发启动方式

			//基类和子类初始化
			nRet = pThread->InitBase(m_mapLatnCfg,m_mapLatnList,idx,bNeedCall);
			if(nRet<0)
			{ 
				DCBIZLOG(DCLOG_LEVEL_ERROR,0,"main","Init  DCLoadUserBase fail");
				isInitFail = true;
				break;
			}

			pThread->start();
			vecThread.push_back(pThread);
		}

		// start to deal task
		sigset(SIGTERM, sig_deal_func);
		sigset(SIGINT, sig_deal_func);
		sigset(SIGQUIT, sig_deal_func);

		//所有线程初始化成功 才执行任务
        list<STInputPara> m_listToDo;
		if(!isInitFail)
		{
			bool bRun = true;
			int msgcount = 0;
			STInputPara inputPara;
			vector<std::string> message;

			while(bRun)
			{
				//无定时任务且本地无缓存SQL任务，取新任务
				if(!g_bRcvCmd && 0 == m_listToDo.size())
				{
					//进程没有缓存任务, 则获取新任务
					message.clear();
					msgcount = server.getTask(message, 5);

#if 0 //TODO test
						string strMsg = "{\"uuuid\": \"3234234234234\", \"latn_id\": 551, \"cycle_id\": 202107, \"task_type\":\"sum\", \"task_params\":\"551|202107|8|************|************|855\", \"task_timestamp\":\"20210630113245\"}";
						message.push_back(strMsg);
						msgcount = 1;
						// strMsg = "{\"uuuid\": \"3234234234234\", \"latn_id\": 551, \"cycle_id\": 202107, \"task_type\":\"sum\", \"task_params\":\"551|202107|6|2|55555\", \"task_timestamp\":\"20210630113245\"}";
						// message.push_back(strMsg);
						// msgcount = 2;
#endif
					if(msgcount > 0)
					{
						for(int i = 0; i < msgcount; i++)
						{
							printf("######## get msg [%d]{%s}", i, message[i].c_str());
							DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"main","g_bRcvCmd[%d] get msg {%s}", g_bRcvCmd, message[i].c_str());

							if(!g_bRcvCmd)
							{
								//无定时命令任务
								inputPara.clear();
								if(ParseMsg(message[i], inputPara) != 0)
								{
									//略过解析失败的消息
									continue;
								}

								inputPara.nProcNum = nProcNumDcfServ;
								nRet = DealTask(inputPara);
								if(-1 == nRet)
								{
									//没有空闲线程，任务缓存至 m_listToDo
									m_listToDo.push_back(inputPara);
									DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"main","m_listToDo.push_back;list size[%d]", m_listToDo.size());
								}
							}
							else
							{
								//有定时任务，SQL任务缓存至 m_listToDo
								m_listToDo.push_back(inputPara);

								DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"main","m_listToDo.push_back;list size[%d]", m_listToDo.size());
							}
						}
					}
				}

				//处理缓存消息
				list<STInputPara>::iterator itor;
				for(itor = m_listToDo.begin(); itor != m_listToDo.end(); )
				{
					DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"main","m_listToDo size[%d], DealTask", m_listToDo.size());

					if(!g_bRcvCmd)
					{
						//无定时命令任务
						nRet = DealTask(*itor);
						if(0 == nRet)
						{
							m_listToDo.erase(itor++);
						}
						else
						{
							itor++;
						}
					}
					else
					{
						//定时任务处理中，缓存的SQL任务等待5s
						sleep(5);
						//itor++;
					}
				}
				if(g_exit)
				{
					if (!server.shutdown())
					{
						DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "stop service {zook:%s}", pZookeeperAddr);
						//printf("stop service {zook:%s}.\n", pZookeeperAddr);
						//return -1;
					}
					if(0 == server.getTaskSize())
					{
						bRun = false;
						break;
					}
				}
				usleep(500);
			}
		}

		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "deal_vecThread_main");

		int nTaskOver = 0;
		while(nTaskOver < vecThread.size())
		{
			nTaskOver = 0;
			for(int idx=0;idx<vecThread.size();idx++)
			{
				if(0 == vecThread[idx]->GetTaskState())
				{
					//该线程空闲，更改为退出状态
					vecThread[idx]->SetRunState(0);
					nTaskOver++; 
					// DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "wait[%d]/[%d]", nTaskOver, vecThread.size());
				}
			}
			//pthread_cancel(NULL);
			//sleep(cfgPara.ncycleSleepTime);
		}
		
	}
	else
	{
		//保存配置参数	
		STConfigPara cfgPara;	
		nRet = initConfig(cfgPara,inputParam.strLatn.c_str(),0);
		if(nRet<0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","initConfig fail.");
		 	printf("Init config failed!\n");
			sleep(2);
			return -1;
		}		
		DCBIZLOG(DCLOG_LEVEL_NONE,0,"","Init initConfig successful");	
		nRet = initConfigOther(cfgPara,inputParam.strLatn.c_str());
		if(nRet<0)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","initConfigOther fail.");
		 	printf("Init configOther failed!\n");
			sleep(2);
			return -1;
		}		
		DCBIZLOG(DCLOG_LEVEL_NONE,0,"","Init initConfigOther successful");	

		if (cfgPara.nOpenKpi)
		{
			//在插件加载初始化之前 初始化DCBPointCenter
			STBPInput v_InParam;
			v_InParam.st_ndelayms = cfgPara.nBPdelayms;
			v_InParam.st_nportocol = cfgPara.nBPportocol;
			v_InParam.st_nBPFlag = cfgPara.nBPFlag;
			v_InParam.st_strAddr = cfgPara.strBPAddr;
			if (0 != InitKpiSender(v_InParam))
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "kpisender init fail.");
				return -1;
			}			
			DCBIZLOG(DCLOG_LEVEL_NONE,0,"","Init InitKpiSender successful");	
			//普通流程统计指标
			if(PROC_TYPE_POST_NORAML == inputParam.nProType)
			{
				DCKpiMon* tmptrMon = NULL; 
				string latnId = cfgPara.latnList;
				std::list<string> tmpLatn;
				tmpLatn.clear();
			    PublicLib::SplitStr(latnId.c_str(),'|',tmpLatn);
				tmptrMon = DCKpiSender::instance()->GetKPIHandle("BILLING", "REC", "RENDLOAD_B");
				DCKpiSender::instance()->group_all_init(tmptrMon, "TraceKpi", tmpLatn);
			}
		}
		
		//灰度单例模式刷新模式屏蔽，用多线程模式版本刷新
		/*nRet = DCConf::Instance()->Init(cfgPara);
	    if (nRet < 0)
	    {
	        DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "","Init gray route failed!");
	        return -1;
	    }		
		DCBIZLOG(DCLOG_LEVEL_NONE,0,"","Init gray route successful");	
		nRet = DCConf::Instance()->start();
	    if (nRet < 0)
	    {
	        DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "","start gray route thread failed!");
	        return -1;
	    }
		*/

		//vector<DCLoadUserBase *> vecThread;
		//bool isInitFail=false;
		for(int idx=0;idx<inputParam.nProcNum;idx++)
		{
			DCLoadUserBase *pThread;
			if(inputParam.nProType==PROC_TYPE_POST_CORSS_JUDGE || inputParam.nProType==PROC_TYPE_POST_CROSS_UPDATE)//更新跨账户表流程
			{
				pThread = new DCLoadOldCrossUser();
			}
			else if(inputParam.nProType==PROC_TYPE_IMMED) //立即出账流程
			{
				pThread = new DCLoadImmedUser();
			}
			else
			{
				pThread = new DCLoadOldUser();
			}			
			pThread->setStartMode(false);//原处理逻辑，带入参启动
			nRet = pThread->InitBase(inputParam,cfgPara,idx);
			if(nRet<0)
			{ 
				DCBIZLOG(DCLOG_LEVEL_ERROR,0,"main","Init  DCLoadUserBase fail");
				isInitFail = true;
				break ;
			}			
			DCBIZLOG(DCLOG_LEVEL_NONE,0,"","Init DCLoadUserBase successful,idx[%d]",idx);	
			pThread->start();		
			vecThread.push_back(pThread);
		}

		while(!isInitFail)
		{
			sleep(cfgPara.ncycleSleepTime);

			for(int idx=0;idx<vecThread.size();idx++)
			{
			    if(vecThread[idx]->GetRunState()==0)
			    {			
				    DCBIZLOG(DCLOG_LEVEL_ERROR,0,"main","DCLoadUserBase runState is [%d],exit\n",vecThread[idx]->GetRunState());
					isInitFail = true;//退出循环
				    break;
			    }
			}
		}

		for(int idx=0;idx<vecThread.size();idx++)
		{
			vecThread[idx]->SetRunState(0);
		}
		//pthread_cancel(NULL);
		//sleep(cfgPara.ncycleSleepTime);
		
	}
	
	for(int idx=0;idx<vecThread.size();idx++)
	{
		vecThread[idx]->join(vecThread[idx]->GetThreadNo());
		//SAFE_DELETE_PTR(vecThread[idx]); // 对线程cancel后，线程清理函数会执行delete操作，这里不用删除
	}
    SAFE_DELETE_PTR(m_mcm);
	DCBIZLOG(DCLOG_LEVEL_NONE, 0, "", "main exit");	
	_exit(0);
	return 0;
}



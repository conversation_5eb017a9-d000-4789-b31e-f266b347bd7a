/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					ocs项目组
*All rights reserved.
*
*Filename：
*		DCLoadImmedUser.h
*Indentifier：
*
*Description：
*		月租用户数据加载类
*Version：
*		V1.0
*Author:
*
*Finished：
*
*History:
********************************************/
#ifndef __DC_LOADPARTRECALCUSER_H__
#define __DC_LOADPARTRECALCUSER_H__
#include <list>
#include <map>
#include <vector>
#include <string>
#include "DCBasePlugin.h"
#include "DCLoadUserBase.h"
#include "publiclib.h"
#include "DCEventTime.h"
#include "DCOBJSet.h"
#include "DCDataCenter.h"
#include "DCRDEventType.h"
#include "DCComboAdaptPrdInst.h"
#include "DCCallZKServerMaker.h"
#include "DataDef.h"

class DCLoadImmedUser : public DCLoadUserBase
{
	public:
		DCLoadImmedUser()
		{

		};

		virtual ~DCLoadImmedUser();

		virtual int InitSub();
		virtual int process();		
		virtual int processDcfs();

    private:
		int DealBusi(int nLatnId);
		int AdjustAcctType(int nGroupId);
		int UpdateInstDealed(STImmedUser &stImmedUser, int nState);
		int LoadImmedUser(vector<STImmedUser> &vecImmedUser);


	private:

		std::list<string> m_ltLatn;

		int m_nLatnId;
		int m_nBillingCycleId;//此账期时间到天YYYYMMDD

		DCCallZKServerMaker* m_pCallServer;

		set<long> m_setPrdGroup;
		set<long> m_setCustGroup;
};
#endif


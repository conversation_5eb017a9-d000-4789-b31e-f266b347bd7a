#ifndef _PARAM_CFG_H_
#define  _PARAM_CFG_H_

#include <map>
#include <string>
#include <string.h>
using namespace std;

class DCOraDB;
class DCOraQuery;
class DCOraExecute;
class CGarbo;

//各配置项字段的值
struct stParamCfg
{
    int iItemId;            //配置项ID
    int iParentItemId;  //父配置项ID
    int iItemType;        //配置项类型,0:标题项;1:配置项
    //int iItemState;       //配置项是否可用;0:不可用,1:可用
    //char szItemName[100];   //配置项名
    char szPath[200];
    char szItemValue[500];   //配置项的值
    char szItemDefaultValue[500];  //配置项的缺省值,只有szItemValue为空时才取该字段的值

    stParamCfg()
    {
        //
        iItemId = 0;
	 iParentItemId = 0;
	 iItemType = 0;
	 memset(szPath, 0, sizeof(szPath));
	 memset(szItemValue, 0, sizeof(szItemValue));
	 memset(szItemDefaultValue, 0, sizeof(szItemDefaultValue));
		
    }
};

class DCParamCfg
{
public:
    static DCParamCfg* GetInstance()
    {
        if (m_pParamCfg == NULL)
        {
            m_pParamCfg = new DCParamCfg();
        }
	 
        if (m_pParamCfg == NULL)
        {
            //日志打印，防止内存分配失败
        }
	 return m_pParamCfg;
    }

public:
    // 读取没有子Section的配置
    bool OCS_readConfig(string& strValue,const char* szpName,const char* szpSectionPath);

    // 读取有一个子Section的配置
    bool OCS_readConfig(string& strValue,const char* szpName,const char* szpSubSectionPath,const char* szpSectionPath);


    int Init(int nReadConfigState);

    int GetReadConfigState(){ return m_nReadConfigState; }
    int AlterPathFormat(char* szpSectionPath, char* szNewSectionPath);
private:
    DCParamCfg();
    ~DCParamCfg(){};
	
    int FindParamCfgValue(string& strValue, const char* szpName);
    int ReadDataFromDb();
    int QueryDBToMap(map<string, stParamCfg>& pParamCfgNewMap);
    //int QueryDBForOldMap();
    

    
    int CheckPathFormat(char*  szPath);	
private:
    static DCParamCfg* m_pParamCfg;
  
    
    //是否读取过数据库,为1是读取了,为0是未读取
    int m_iReadDb;

    //0:用的是old map, 1:用的是new map
    int m_iWhichMapUse; 

    // 1:读取配置文件,2:读取数据库,默认为读取配置文件
    int m_nReadConfigState;
	
    //以配置项的名字做为关键字
    map<string, stParamCfg> m_ParamCfgOldMap;
    map<string, stParamCfg> m_ParamCfgNewMap;
    map<string, stParamCfg>* m_pParamCfgCurrentMap;

    friend class CGarbo;

    // 定义一个静态成员，在程序结束时，系统会调用它的析构函数	
    static CGarbo Garbo;

};

// 它的唯一工作就是在析构函数中删除DCParamCfg的实例
class CGarbo 
{
public:
   CGarbo(){};
    ~CGarbo()
    {
        if (DCParamCfg::m_pParamCfg)
        {
            delete DCParamCfg::m_pParamCfg;
        }
    }
};

#endif // _PARAM_CFG_H_

-----------------------20171010 begin------------------------------


-- Create table
create asynchronous writethrough cache group CG_CYCLE_HISTORY
from
    CYCLE_HISTORY (
            OFR_ID                 NUMBER(9) NOT NULL,
            OFR_DETAIL_INST_REF_ID NUMBER(12) NOT NULL,
            LATN_ID                NUMBER(10) NOT NULL,
            PRESENT_TIMES          NUMBER(9) NOT NULL,
            PRESENT_LAST_TIME      VARCHAR2(20 BYTE) INLINE NOT NULL,
            OFR_TYPE               NUMBER(8) NOT NULL,
            OFR_INST_ID            NUMBER(20) NOT NULL DEFAULT -1,
            EVENT_TYPE_ID          NUMBER(9) NOT NULL,
            DEDUCT_DAY             NUMBER(8) DEFAULT -1,
            LATN                   VARCHAR2(10 BYTE) INLINE NOT NULL DEFAULT '-1',
            GROUP_ID               NUMBER(10) NOT NULL DEFAULT -1,
            CYCLE_TYPE             VARCHAR2(2 BYTE) INLINE NOT NULL DEFAULT '04',
        primary key (
            OFR_ID,
            OFR_DETAIL_INST_REF_ID,
            LATN_ID,
            EVENT_TYPE_ID,
            LATN,
            GROUP_ID,
            OFR_INST_ID));

    create index IDX2_CYCLE_HISTORY on CYCLE_HISTORY
        (OFR_DETAIL_INST_REF_ID, LATN_ID);

    create index IDX_CYCLE_HISTORY on CYCLE_HISTORY (
        OFR_ID,
        OFR_DETAIL_INST_REF_ID,
        LATN_ID,
        EVENT_TYPE_ID,
        GROUP_ID,
        OFR_INST_ID);


-----------------------20171010 end--------------------------------------
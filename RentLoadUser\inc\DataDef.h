/*******************************************
*Copyrights   2016，深圳天源迪科计算机有限公司
*                 月租项目组
*All rights reserved.
*
*Filename：
*       DataDef.h
*
*Description：
*       月租用户加载用到的数据结构定义
*
*Version：
*       V1.0
*
*LastModifyAuthor:
*		zhongqu
*
*LastModifyDate：
*       2018-02-27
*
********************************************/
#ifndef __DC_DATADEF_H__
#define __DC_DATADEF_H__
#include <string>
#include <vector>
#include "DCUDB.h"
#include "DCDBManer.h"
#include "DCDateTime.h"
#include "DCRentData.h"
#include "UStaMsg.h"
#include "rentThreeUserInfoJson.h"
#include "DCAcctDiscMessage.h"
#include "UFmtMsg.h"


#define USER_NEW			1 //新装
#define USER_STOP		    2 //停复机
#define USER_OLD			3 //老用户
#define USER_TRIAL		    4 //试算
#define USER_IMMED		    5 //立即出账


#define PROC_TYPE_POST_NORAML	    1  //后付费普通进程(老用户)
#define PROC_TYPE_POST_CORSS_JUDGE	2  //后付费跨账户判断流程
#define PROC_TYPE_POST_CROSS_UPDATE	3  //后付费跨账户更新流程(仅需一个进程)
#define PROC_TYPE_PRE_NORMAL	    4  //预付费普通进程(老用户)
#define PROC_TYPE_INST_CHANGE		5  //新装
#define PROC_TYPE_TRANSFER_PRD		6  //账户变更
#define PROC_TYPE_FREZEN_USER		7  //复机
#define PROC_TYPE_TRIAL_USER		8  //试算用户
#define PROC_TYPE_IMMED		        9  //立即出账


#define THREAD_AVAILABLE			1  //线程可用
#define THREAD_DISABLED				0  //线程不可用
#define THREAD_FREE					0  //线程空闲
#define THREAD_BUSY					1  //线程忙

const int NUMBER_OF_CYCLES	= 3;	//数据库异常重试次数



const int FAIL_NOT_FIND_SQLNAME 				= -20014;//配置文件缺少sql配置
const int FAIL_UPDATE_ITEM_OUT_IMMED_USER 	    = -20015;




//delete指针、指针数据
#ifdef SAFE_DELETE_PTR
#undef SAFE_DELETE_PTR
#endif
#define SAFE_DELETE_PTR(ptr) \
	if (NULL != ptr) { delete ptr; ptr = NULL; }
#define SAFE_DELETE_PTR_ARR(ptr) \
	if (NULL != ptr) { delete[] ptr; ptr = NULL; }


//消息控制类模式
enum MsgContrlMode{
		MSGCTRL_MODE_NORMAL=0,	//发送正常消息
		MSGCTRL_MODE_RESEND=1,	//发送超时消息
		MSGCTRL_MODE_LOADOLDBEG=2,	//定时流程开始
		MSGCTRL_MODE_LOADNEWBEG=3,	//新装流程开始
		MSGCTRL_MODE_LOADFROBEG=4,	//复机流程开始
		MSGCTRL_MODE_LOADOLDEND=5	//定时流程结束
};
		
//消息路由时账户类型
enum RouteAcctType{
		SPECIAL_ACCT_COMM=11,  //普通账户
		SPECIAL_ACCT_BIG=12,   //大账户
		SPECIAL_ACCT_INST=14,  //跨账户
		SPECIAL_ACCT_MOST=15,  //特大账户
		SPECIAL_ACCT_WAIT=99
};

struct STBPInput
{
    int st_ndelayms;
	int st_nportocol;
	int st_nBPFlag;
	string st_strAddr;
	STBPInput()
	{
		st_ndelayms = 0;
		st_nportocol = 0;
		st_nBPFlag = 0;
		st_strAddr = "";
	}
	void clear()
	{
		st_ndelayms = 0;
		st_nportocol = 0;
		st_nBPFlag = 0;
		st_strAddr = "";
	}
};

//消息路由时账户类型
enum ItemoutimmedState{
		IMMED_STATE_INIT=0,
		IMMED_STATE_SUCCESS=1,
		IMMED_STATE_FAILED=2,
		IMMED_STATE_DOING=9
};



//配置项
struct STConfigPara
{
	std::string latnList; //
	int nReconnectInterval;    //oracle数据库重连时间间隔,单位秒
	int nReconnectTimes;    //oracle数据库重连次数	
	int ncycleSleepTime;
	int nNewUserSleepTime;
	int nFrozenUserSleepTime;
	int nOldUserSleepTime;
	int nBigAcctThreshold;	
	int nMostBigAcctThreshold;
	int nContralGetOfrInst;
	int nGetDetail;
	int nNewUserInstSendBatN;
	int nFeecacheTimeOutSec;
	int nInstSendBatNum;
	std::string strZKAddr;
	std::string strZKServName;
	std::string strZKRootDir;
	std::string strZKLoginPwd;
	std::string strZKAesPwd;
	std::string strZKDcfLogDir;
	int nZKDcfLogLevel;
	std::string strHashKey;
	int nFlowControl;
	int nFlowSleepUs;
	int nQueueSizeOldUser;
	int nQueueSizeNewUser;
	int nQueueSizeFrozenUser;
	int nFlowTimeOut;	
	bool m_bGroupCycle; //是否支持集团月租

	int nCrossAcctChgFlag;
	int SevenCancelSwitch;
	
	
	int nBPdelayms;
	int nBPportocol;
	int nBPFlag;
	string strBPAddr; 
	int nOpenKpi;

	int nBigAcctMsgSize;					//直接发送消息的最大size，超过则走DCA传送
	int nDcaCacheMsgSize;					//走DCA传送的记录，需要分条插入，每条记录的最大size

	int nTrialDay;		//每月试算日试算用户下月租费
	int nGrayRefreshIntr;
	string sSubscriber;
	string sRouteProcess;
};


//main 函数入参结构体
struct STInputPara
{
    bool bDsfStart;
	int nProType;
	int nProcNum;  //线程总数
	int nLatnID;
	std::string strLatn;
	std::vector<string> vecSQLFields;

	void clear()
	{
	    bDsfStart = true;
		nProType = 0;
		nProcNum = 0;
		nLatnID = 0;
		strLatn = "";
		vecSQLFields.clear();
	}
};


//套餐匹配模块到消息发送模块接口
struct 	RentUserInfo
{	
	long lnSpecialId;
	int nAcctType;
	int nLatnId;
	int nBillingCycleId;//YYYYMMDD
	int nPrdInstNum;
	int nSubInstNum;
	int nOfrInstNum;
	int nGroupNum;
	int nMsgType;
	long lnPidId;	
	long lnRouteAcctId;
	ocs::UFmtHead		stFmtHead;
	ocs::IAcctHead		stAcctHead;
	ocs::IAcctBodyReq	stAcctBodyReq;
	std::vector<long> 	vecTransferId;
};


//融合帐务条件项结构体
struct STATCondItem
{
	int iLeftType;          //左值类型
	int iRightType;         //右值类型
	int iInset_id;           //left从INSET取值用
	int iIn_code_type;
	int iIn_code_offset;
	int iIn_code_length;
	int iColID;             //left从QUERY取值用
	int iGroupid;
	int inext_group_id;
	char szOperator[32];
	char szRight[320];
	char szLeft[64];
	int  iRight;
	int  iLeft;

};


//融合帐务主附实例条件信息结构体
struct StInstElementValue
{
	char sOfferState[30];
	char sCompletedDate[30];
	char sSubCompleted[30];
	char sUserTypeId[30];
	char sInstallDate[30];
	char sCrtDate[30];
	char sPrdId[30];
	int  nIfPrepay;
	
	StInstElementValue()
	{
		memset(sOfferState,0x00,sizeof(sOfferState));
		memset(sCompletedDate,0x00,sizeof(sCompletedDate));
		memset(sSubCompleted,0x00,sizeof(sSubCompleted));
		memset(sUserTypeId,0x00,sizeof(sUserTypeId));
		memset(sInstallDate,0x00,sizeof(sInstallDate));
		memset(sCrtDate,0x00,sizeof(sCrtDate));
		memset(sPrdId,0x00,sizeof(sPrdId));
		nIfPrepay = 0;
	}
};


//定时任务配置
struct STEventPro
{
	long lnCycleEventId;
	int nSetHour;
	int nLatnId;
	char szLastDealTime[16];
	char szLastStartTime[16];
	
	STEventPro()
	
{
		lnCycleEventId=0;
		nSetHour=0;
		nLatnId=0;
		memset(szLastDealTime,0x00,sizeof(szLastDealTime));
		memset(szLastStartTime,0x00,sizeof(szLastStartTime));
	}		
};

struct STImmedUser
{	
	long itemoutimmed_id;
	long acct_id;
	int latn_id;
	string acc_num;	
	
	STImmedUser()
	{
		itemoutimmed_id = 0;
		acct_id = 0;
		latn_id = 0;			
		acc_num = "";
	}
	std::string dump_to_string()
	{
		return string("itemoutimmed_id:") + to_string(itemoutimmed_id) + string(",acct_id:") + to_string(acct_id) + 
			string(",latn_id:") + to_string(latn_id) +  ",acc_num:" + acc_num;
	}

};


//特殊分流账户判断KEY
struct STSpecialAcctKey
{
	long lnAcctId;
	long lnOfrInstId;
	int nGroupId;  //grou_id =1大账户 =2跨账户 =3特大账户

	STSpecialAcctKey()
	
{
		lnAcctId=0;
		lnOfrInstId=0;
		nGroupId=0;
	}
	bool operator <(const STSpecialAcctKey& rhs ) const
    {
		 if ( lnAcctId < rhs.lnAcctId)
		 {
			 return true;
		 }
		 else if ( lnAcctId == rhs.lnAcctId )
		 {
		 	 if ( lnOfrInstId < rhs.lnOfrInstId )
			 {
				 return true;
			 }
			 else if (lnOfrInstId == rhs.lnOfrInstId)
			 {
				 if ( nGroupId < rhs.nGroupId )
				 {
					 return true;
				 }
			 }
		 }
		 return false;
	}
};
	

//特殊分流账户信息
struct STSpecialAcctInfo
{
	long lnAcctId;
	long lnOfrInstId;
	int nGroupId;
	long lnModGroupId;

	STSpecialAcctInfo()
	
{
		lnAcctId=0;
		lnOfrInstId=0;
		nGroupId=0;
		lnModGroupId=0;
	}
};

struct ST_AcctKey
{
	int nAcctType;
	long lnAcctId;    

    ST_AcctKey()
	{
		nAcctType = 0;
		lnAcctId = 0;
	}
	
	bool operator <(const ST_AcctKey& rhs ) const
	{
		if ( nAcctType < rhs.nAcctType )
		{
			return true;
		}
		else if ( nAcctType == rhs.nAcctType )
		{
			if ( lnAcctId < rhs.lnAcctId )
			{
				return true;
			}			
		}
		return false;
	}
};


//用户账户变更信息
struct STTransferAcctPrd
{
	long lnTransferId;
	long lnPrdInstId;
	long lnNewAcctId;
	
	STTransferAcctPrd()
	
{
		lnTransferId=0;
		lnPrdInstId=0;
		lnNewAcctId=0;
	}		
};

struct StPrdInstInfo {
    int64_t lnProdInstId;
    int64_t lnProdId;
    int64_t lnCustId;
    int64_t lnAcctId;
    int64_t lnOfferId;
    std::string szAccNum;
    std::string szAreaCode;
	std::string szBasicState;
    std::string szEffDate;
    std::string szExpDate;
    std::string szStatusCd;
    std::string szCreateDate;
    std::string szFirstFinishDate;
    std::string szInstallDate;
	std::string szBillDate;
    std::string szUserTypeId;
	std::string sPaymentCd;
	int32_t nPreUserFlag;
	int32_t nRealNameFlag;
  
	StPrdInstInfo() :
        lnProdInstId(int64_t(0)),
        lnProdId(int64_t(0)),
        lnCustId(int64_t(0)),
        lnAcctId(int64_t(0)),
        lnOfferId(int64_t(0)),
        szAccNum(std::string("")),
        szAreaCode(std::string("")),
        szBasicState(std::string("")),
        szEffDate(std::string("")),
        szExpDate(std::string("")),
        szStatusCd(std::string("")),
        szCreateDate(std::string("")),
        szFirstFinishDate(std::string("")),
        szInstallDate(std::string("")),
        szBillDate(std::string("")),
        szUserTypeId(std::string("")),
        sPaymentCd(std::string("")),
        nPreUserFlag(int32_t(0)),
        nRealNameFlag(int32_t(0))
        { }

	bool operator<(const StPrdInstInfo& B) const
	{
		if(lnAcctId < B.lnAcctId)
		{
			return true;
		}
		else if(lnAcctId == B.lnAcctId)
		{
			if(lnProdInstId < B.lnProdInstId)
			{
				return true;
			}
			else if(lnProdInstId == B.lnProdInstId)
			{
				if(strcmp(szEffDate.c_str(), B.szEffDate.c_str()) > 0) //逆序
				{
					return true;
				}
			}
		}
		return false;
	}
};


typedef std::vector<T_instChange>  T_vecInstChange;

#endif

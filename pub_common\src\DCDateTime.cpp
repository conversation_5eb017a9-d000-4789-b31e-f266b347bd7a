/*******************************************
*Copyrights  2005，深圳天源迪科计算机有限公司
*                   在线计费项目组
*All rights reserved.
*
* Filename：    DCDateTime.cpp
* Indentifier：     
* Description： 时间处理类        
* Version：     V1.0
* Author:       wangbin
* Finished：    2006年03月10日
* History:
********************************************/

#include <ace/Time_Value.h>
#include <ace/OS.h>
#include "DCDateTime.h"
#include "publiclib.h"
#include "DCLogMacro.h"

#include <time.h>

DCDateTime::DCDateTime()
{
	DCBIZLOG(DCLOG_LEVEL_TRACE, 0,"","DCDateTime::DCDateTime()");

	m_aceTime = ACE_OS::gettimeofday();
}

DCDateTime::DCDateTime(ACE_Time_Value v_timeValue)
{
	DCBIZLOG(DCLOG_LEVEL_TRACE, 0,"","DCDateTime::DCDateTime(ACE_Time_Value v_timeValue)");

	m_aceTime = v_timeValue;
}

DCDateTime::DCDateTime(int v_nSec)
{
    DCBIZLOG(DCLOG_LEVEL_TRACE, 0,"","DCDateTime::DCDateTime(int v_nSec)");

    m_aceTime =  ACE_Time_Value(v_nSec);
}

DCDateTime & DCDateTime::operator=(const ACE_Time_Value v_aceTime)
{
	DCBIZLOG(DCLOG_LEVEL_TRACE, 0,"","DCDateTime & DCDateTime::operator");
	m_aceTime = v_aceTime;
	return (*this);
}

std::string DCDateTime::ToString(const std::string &v_format) const
{
	DCBIZLOG(DCLOG_LEVEL_TRACE, 0,"","std::string DCDateTime::ToString");

	char sTime[512];
	
	if(v_format.length() > 512) return "";
	ToString(sTime,v_format.c_str());
	return sTime;
}

int DCDateTime::ToString(char *v_str,const char *v_format) const
{
	DCBIZLOG(DCLOG_LEVEL_TRACE, 0,"","DCDateTime::ToString");

	char sTmp[512],sTmp1[512],sTmp2[10],sTmp3[512];
	struct tm sttm;
	char *pTmp;
	time_t nTime;
	
	if(strlen(v_format) >= 512) return -1;
	
	nTime = (time_t) (m_aceTime.sec());
	ACE_OS::localtime_r(&nTime,&sttm);

	strcpy(sTmp3,v_format);
	PublicLib::strtoupper(v_format,sTmp);
	while((pTmp = strstr(sTmp,"YYYY")) != NULL)
	{
		sprintf(sTmp2,"%04d",1900+sttm.tm_year);
		PublicLib::strreplace(sTmp,sTmp1,sTmp2,pTmp-sTmp,4);
		strcpy(sTmp,sTmp1);
		PublicLib::strreplace(sTmp3,sTmp1,sTmp2,pTmp-sTmp,4);
		strcpy(sTmp3,sTmp1);
	}
	while((pTmp = strstr(sTmp,"YY")) != NULL)
	{
		sprintf(sTmp2,"%02d",sttm.tm_year % 100);
		PublicLib::strreplace(sTmp,sTmp1,sTmp2,pTmp-sTmp,2);
		strcpy(sTmp,sTmp1);
		PublicLib::strreplace(sTmp3,sTmp1,sTmp2,pTmp-sTmp,2);
		strcpy(sTmp3,sTmp1);
	}
	while((pTmp = strstr(sTmp,"MM")) != NULL)
	{
		sprintf(sTmp2,"%02d",sttm.tm_mon+1);
		PublicLib::strreplace(sTmp,sTmp1,sTmp2,pTmp-sTmp,2);
		strcpy(sTmp,sTmp1);
		PublicLib::strreplace(sTmp3,sTmp1,sTmp2,pTmp-sTmp,2);
		strcpy(sTmp3,sTmp1);
	}
	while((pTmp = strstr(sTmp,"M")) != NULL)
	{
		sprintf(sTmp2,"%d",sttm.tm_mon+1);
		PublicLib::strreplace(sTmp,sTmp1,sTmp2,pTmp-sTmp,1);
		strcpy(sTmp,sTmp1);
		PublicLib::strreplace(sTmp3,sTmp1,sTmp2,pTmp-sTmp,1);
		strcpy(sTmp3,sTmp1);
	}
	while((pTmp = strstr(sTmp,"DD")) != NULL)
	{
		sprintf(sTmp2,"%02d",sttm.tm_mday);
		PublicLib::strreplace(sTmp,sTmp1,sTmp2,pTmp-sTmp,2);
		strcpy(sTmp,sTmp1);
		PublicLib::strreplace(sTmp3,sTmp1,sTmp2,pTmp-sTmp,2);
		strcpy(sTmp3,sTmp1);
	}
	while((pTmp = strstr(sTmp,"D")) != NULL)
	{
		sprintf(sTmp2,"%d",sttm.tm_mday);
		PublicLib::strreplace(sTmp,sTmp1,sTmp2,pTmp-sTmp,1);
		strcpy(sTmp,sTmp1);
		PublicLib::strreplace(sTmp3,sTmp1,sTmp2,pTmp-sTmp,1);
		strcpy(sTmp3,sTmp1);
	}
	while((pTmp = strstr(sTmp,"HH")) != NULL)
	{
		sprintf(sTmp2,"%02d",sttm.tm_hour);
		PublicLib::strreplace(sTmp,sTmp1,sTmp2,pTmp-sTmp,2);
		strcpy(sTmp,sTmp1);
		PublicLib::strreplace(sTmp3,sTmp1,sTmp2,pTmp-sTmp,2);
		strcpy(sTmp3,sTmp1);
	}
	while((pTmp = strstr(sTmp,"H")) != NULL)
	{
		sprintf(sTmp2,"%d",sttm.tm_hour);
		PublicLib::strreplace(sTmp,sTmp1,sTmp2,pTmp-sTmp,1);
		strcpy(sTmp,sTmp1);
		PublicLib::strreplace(sTmp3,sTmp1,sTmp2,pTmp-sTmp,1);
		strcpy(sTmp3,sTmp1);
	}
	while((pTmp = strstr(sTmp,"NN")) != NULL)
	{
		sprintf(sTmp2,"%02d",sttm.tm_min);
		PublicLib::strreplace(sTmp,sTmp1,sTmp2,pTmp-sTmp,2);
		strcpy(sTmp,sTmp1);
		PublicLib::strreplace(sTmp3,sTmp1,sTmp2,pTmp-sTmp,2);
		strcpy(sTmp3,sTmp1);
	}
	while((pTmp = strstr(sTmp,"N")) != NULL)
	{
		sprintf(sTmp2,"%d",sttm.tm_min);
		PublicLib::strreplace(sTmp,sTmp1,sTmp2,pTmp-sTmp,1);
		strcpy(sTmp,sTmp1);
		PublicLib::strreplace(sTmp3,sTmp1,sTmp2,pTmp-sTmp,1);
		strcpy(sTmp3,sTmp1);
	}
	while((pTmp = strstr(sTmp,"SS")) != NULL)
	{
		sprintf(sTmp2,"%02d",sttm.tm_sec);
		PublicLib::strreplace(sTmp,sTmp1,sTmp2,pTmp-sTmp,2);
		strcpy(sTmp,sTmp1);
		PublicLib::strreplace(sTmp3,sTmp1,sTmp2,pTmp-sTmp,2);
		strcpy(sTmp3,sTmp1);
	}
	while((pTmp = strstr(sTmp,"S")) != NULL)
	{
		sprintf(sTmp2,"%d",sttm.tm_sec);
		PublicLib::strreplace(sTmp,sTmp1,sTmp2,pTmp-sTmp,1);
		strcpy(sTmp,sTmp1);
		PublicLib::strreplace(sTmp3,sTmp1,sTmp2,pTmp-sTmp,1);
		strcpy(sTmp3,sTmp1);
	}
	strcpy(v_str,sTmp3);
		
	return 0;
}

DCDateTime operator+(const DCDateTime &v_valueL,const DCDateTime &v_valueR)
{
	return DCDateTime(v_valueL.GetAceTimeValue() + v_valueR.GetAceTimeValue());
}

DCDateTime operator-(const DCDateTime &v_valueL,const DCDateTime &v_valueR)
{
	return DCDateTime(v_valueL.GetAceTimeValue() - v_valueR.GetAceTimeValue());
}

DCDateTime &DCDateTime::FromString(const std::string &v_str,const std::string &v_format)
{
	DCBIZLOG(DCLOG_LEVEL_TRACE, 0,"","DCDateTime &DCDateTime::FromString");

	char sTmp[512],sTmp1[512],sTmp2[10];
	struct tm sttm;
	char *pTmp;
	time_t nTime;
	bool bYearGotten;
	
	strcpy(sTmp1,v_str.c_str());
	if(strlen(v_format.c_str()) >= 512) 
	{
		m_aceTime = 0;
		return (*this);
	}
	
	nTime = (time_t) (0);
	ACE_OS::localtime_r(&nTime,&sttm);
    sttm.tm_sec = 0;
    sttm.tm_min = 0;
    sttm.tm_hour = 0;
    sttm.tm_mday = 0;//将日时分秒初始化，add by huanghua
	PublicLib::strtoupper(v_format.c_str(),sTmp);
	
	bYearGotten = false;
	pTmp = sTmp;
	while((pTmp = strstr(pTmp,"YYYY")) != NULL)
	{
		sprintf(sTmp2,"%.4s",sTmp1+(pTmp-sTmp));
		sttm.tm_year = atoi(sTmp2) - 1900;
		pTmp += 4;
		bYearGotten = true;
	}
	if(!bYearGotten) {
		pTmp = sTmp;
		while((pTmp = strstr(pTmp,"YY")) != NULL)
		{
			sprintf(sTmp2,"%.2s",sTmp1+(pTmp-sTmp));
			sttm.tm_year = atoi(sTmp2);
			pTmp += 2;
		}
	}
	pTmp = sTmp;
	while((pTmp = strstr(pTmp,"MM")) != NULL)
	{
		sprintf(sTmp2,"%.2s",sTmp1+(pTmp-sTmp));
		sttm.tm_mon = atoi(sTmp2)-1;
		pTmp += 2;
	}
	pTmp = sTmp;
	while((pTmp = strstr(pTmp,"DD")) != NULL)
	{
		sprintf(sTmp2,"%.2s",sTmp1+(pTmp-sTmp));
		sttm.tm_mday = atoi(sTmp2);
		pTmp += 2;
	}
	pTmp = sTmp;
	while((pTmp = strstr(pTmp,"HH")) != NULL)
	{
		sprintf(sTmp2,"%.2s",sTmp1+(pTmp-sTmp));
		sttm.tm_hour = atoi(sTmp2);
		pTmp += 2;
	}
	pTmp = sTmp;
	while((pTmp = strstr(pTmp,"NN")) != NULL)
	{
		sprintf(sTmp2,"%.2s",sTmp1+(pTmp-sTmp));
		sttm.tm_min = atoi(sTmp2);
		pTmp += 2;
	}
	pTmp = sTmp;
	while((pTmp = strstr(pTmp,"SS")) != NULL)
	{
		sprintf(sTmp2,"%.2s",sTmp1+(pTmp-sTmp));
		sttm.tm_sec = atoi(sTmp2);
		pTmp += 2;
	}
    //这里将夏时令的值设为1,不然时间会自动加一小时
	sttm.tm_isdst = 1;
    DCBIZLOG(DCLOG_LEVEL_TRACE, 0,"","IS datlight %d",sttm.tm_isdst);

	nTime = mktime(&sttm);
//	printf("时间e:%04d-%02d-%02d,%02d:%02d:%02d\n",sttm.tm_year+1900,sttm.tm_mon+1,
//		sttm.tm_mday,sttm.tm_hour,sttm.tm_min,sttm.tm_sec);
//	printf("nTime is [%lu]\n",nTime);
	
	m_aceTime = nTime;
	
	return (*this);
}

int DCDateTime::GetYear() const
{
	DCBIZLOG(DCLOG_LEVEL_TRACE, 0,"","DCDateTime::GetYear");

	struct tm sttm;
	time_t nTime;
	
	nTime = (time_t) (m_aceTime.sec());
	ACE_OS::localtime_r(&nTime,&sttm);
	return sttm.tm_year+1900;
}

int DCDateTime::GetMonth() const
{
	DCBIZLOG(DCLOG_LEVEL_TRACE, 0,"","DCDateTime::GetMonth");

	struct tm sttm;
	time_t nTime;
	
	nTime = (time_t) (m_aceTime.sec());
	ACE_OS::localtime_r(&nTime,&sttm);
	return sttm.tm_mon+1;
}

int DCDateTime::GetDay() const
{
	DCBIZLOG(DCLOG_LEVEL_TRACE, 0,"","DCDateTime::GetDay");

	struct tm sttm;
	time_t nTime;
	
	nTime = (time_t) (m_aceTime.sec());
	ACE_OS::localtime_r(&nTime,&sttm);
	return sttm.tm_mday;
}

int DCDateTime::GetHour() const
{
	DCBIZLOG(DCLOG_LEVEL_TRACE, 0,"","DCDateTime::GetHour");

	struct tm sttm;
	time_t nTime;
	
	nTime = (time_t) (m_aceTime.sec());
	ACE_OS::localtime_r(&nTime,&sttm);
	return sttm.tm_hour;
}

int DCDateTime::GetMin() const
{
	DCBIZLOG(DCLOG_LEVEL_TRACE, 0,"","DCDateTime::GetMin");

	struct tm sttm;
	time_t nTime;
	
	nTime = (time_t) (m_aceTime.sec());
	ACE_OS::localtime_r(&nTime,&sttm);
	return sttm.tm_min;
}

int DCDateTime::GetSec() const
{
	DCBIZLOG(DCLOG_LEVEL_TRACE, 0,"","DCDateTime::GetSec");

	struct tm sttm;
	time_t nTime;
	
	nTime = (time_t) (m_aceTime.sec());
	ACE_OS::localtime_r(&nTime,&sttm);
	return sttm.tm_sec;
}
	
//取某年某月的天数
int DCDateTime::GetMaxDay(int v_year,int v_mon)
{
	DCBIZLOG(DCLOG_LEVEL_TRACE, 0,"","DCDateTime::GetMaxDay");

	if(v_mon <1 || v_mon > 12) return -1;
	if(v_year < 1000 || v_year > 9999 ) return -1;
	char maxday[12] = {31,28,31,30,31,30,31,31,30,31,30,31};
	if(v_mon != 2) return maxday[ v_mon -1 ];
	if(v_year % 4) return 28;
	if(v_year % 100) return 29;
	if(v_year % 400) return 28;
	return 29;
}

void DCDateTime::SetSec(long sec, long usec)
{
	this->m_aceTime.set(sec,usec);
}

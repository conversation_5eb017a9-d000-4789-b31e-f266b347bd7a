/****************************************************************************************
*Copyrights  2016，深圳天源迪科计算机有限公司
*						OCS项目组
*All rights reserved.
*
* Filename：	DCRentData.h		
* Indentifier：		
* Description：			
* Version：		V1.0
* Author:		zsh
* Finished：	2016年06月01日
* History:
******************************************************************************************/
#ifndef DC_RENTDATE_H
#define DC_RENTDATE_H


#include <map>
#include <string>
#include <vector>
#include <list>
//#include "ace/Hash_Map_Manager.h"
#include "DCDateTime.h"
#include "DCRBMsgStruct.h"

using namespace std;

#define FROZENFLAG  11  //是否是停机保号处理
#define STOP_RESERVE_01  1201           //申请停机  
#define STOP_RESERVE_02  1204           //双停＋申请停机
#define STOP_RESERVE_03  1206           //申请停机＋预拆机

#define ABM_REQUSET_MSG_ID 411          //ABM扣费请求应答MSG ID
#define DISCOUNT_MAIN_OFFER 5  //套餐属性表中的属性ID，5为用户订购的需要打折的主套餐
#define DISCOUNT_BASE_OFFER 6  //套餐属性表中的属性ID，6为折扣套餐

#define ABM_SERVICE_TYPE   1000

#define ERR_ABM_RATEBLE_QUERY_ERR 4506
#define ABM_TRIAL_DEDUCT_NOT_ENOUGH 4526
#define ABM_TRIAL_DEDUCT_ENOUGH     0

#define ABM_ERR_BALANCE                 -30232

#define ABM_RESPOND_DEDUCT  411

#define PARA_PROCESS_TYPE_ALL       -1 //试算和租费扣除进程
#define PARA_PROCESS_TYPE_NOTRIAL   1  //租费扣除进程
#define PARA_PROCESS_TYPE_TRIAL     2  //试算进程

#define TRIAl_DAY_TYPE  1   //试算且试算天
#define TRIAL_NOT_DAY_TYPE 2  //试算事件但不是试算天
#define NOT_TRIAL    0       //非试算
#define NOT_TRIAL_PROCESS 3  //非试算进程

#define GROUP_BIG          1  //大账户
#define GROUP_CROSS        2  //跨账户
#define GROUP_MOST         3  //超大账户

#define MSGTYPE_OLD            0  //老用户流程
#define MSGTYPE_NEW            1  //套餐变更(新装)流程
#define MSGTYPE_TRANSFER       2  //用户过户流程
#define MSGTYPE_ONCE           3  //一次费流程
#define MSGTYPE_IMMED          4  //立即出账流程
#define MSGTYPE_STOP           5  //复机流程


#define TRIAL_DEALDAY_SINGLE       1  //试算时间类型  只在每月其中一天处理（TrialDay配置）
#define TRIAL_DEALDAY_MULTI        2  //试算时间类型  在每月从TrialDay日开始每天都进行试算


//新装用户信息体
struct T_instChange
{
    long        ofr_inst_change_id;        //销售品实例变更标识
    long        ofr_inst_id;               //销售品实例标识
    long        cust_id;
    long        lnAcctID;
    DCDateTime  crt_date;                 //创建时间
    std::string eff_state;                 //是否有效,1为生效,0为失效  
    long        ofr_id;                    //销售品标识  
    long        lnPlanId;
    long        lnCalcPriority;
    char        szEffDate[15]; //生效时间
    char        szExpDate[15]; //失效时间
    char        szEventEffDate[15]; //事件生效时间
    char        szEventExpDate[15]; //事件失效时间
    long        ofr_detail_inst_id;        //销售品明细实例
    long        ofr_detail_inst_ref_id;    //销售品明细实例引用标识
    std::string ofr_detail_type_id;        //销售品明细类型
    
    //主要指
    //D1 -- 客户
    //E1 -- 帐户
    //A1 -- 主产品、B1
    //基础商品
    //C1-- 服务(装、拆、移、改等)等类型
    //F1--集团 
    long        ofr_detail_id;             // 销售品与产品关系标识 
    int         ofr_type;                  //商品类型,0为基础(来源于tb_prd_prd_inst), 1为优惠(来源于tb_prd_ofr_inst)
    int         nIfDealed;                 //变更记录是否做了扣费处理  0:未处理  1:已处理
    int         nOfrInstLatnId;
    int         nLatnId;
    long        lnPrdID;                //产品ID
    std::string strAreaCode;            //区号
    std::string strServNbr;             //号码
	long        lnGroupId;				//组ID，个性化时填个性化的组，否则填-1
	DCDateTime  last_time;              //上次处理时间
	long        lnBaseOfrId;            //基础销售品ID,TB_PRD_PRD_INST.OFR_ID
	long 		lnMainPrdId;			//tb_prd_prd_inst的ID
	long        lnGlobalRatableId;    //全局累积量ID
	int         nBaodiFee;             //全局累积量保底费用
	long        lnGlobalOfrId;
	long        lnGlobalOfrInstId;
	long        lnGlobalPlanId;
	long 		lnPrdInstId;          //主产品实例ID
	long        lnGroupUserGroupId;
	long        lnSubPrdInstId;
    char szUserType[3];
	char szMVNOID[13];
	int         nDiscountValue;  //主套餐折扣率0-100
	list<T_GlobleOfrInfo> lstGlobleOfrInfo;
	T_instChange()
	{
		memset(szUserType,0x0,sizeof(szUserType));
		memset(szMVNOID,0x0,sizeof(szMVNOID));
		memset(szEffDate,0x0,sizeof(szEffDate));
		memset(szExpDate,0x0,sizeof(szExpDate));
		lnGlobalRatableId = -1;
		nBaodiFee = 0;
		lnGlobalOfrId = -1;
		lnGlobalOfrInstId = -1;
		lnGlobalPlanId = -1;
		lnPrdInstId = -1;
		lnSubPrdInstId = -1;
		lnCalcPriority = -1;
		lnPlanId = -1;
		nDiscountValue = 100;
		lstGlobleOfrInfo.clear();
	}
};
typedef std::vector<T_instChange>  T_vecInstChange;

//产品实例表信息结构体
struct T_PrdInst
{
    long lPrdInstID;//产品实例标识
    long lCustID;
    long lAcctID;
    long lnPrdID;
    int      iOfrID;//销售品标识
    char sServNumber[21];//服务号码
    int     iLatnID;//本地网标识
    char   sAreaCode[9];//区号
    char sBasicState[5];//标识 
    char sUrbanFlag[11];//城乡标志包括：10-城市，11-农村
    char sUserTypeID[11];//用户性质
    char szUserType[3];
	char szMVNOID[13];
    long lnUserStasID;
	int  nIvpnFlag;	//IVPN标识	
	int nBaodiFee;
	int nUserMode;
	long lnGlobalOfrId;
	long lnGlobalOfrInstId;
	long lnGlobalRatableId;
	long lnGlobalRatableFee;   //全局累积量的值
	int  nDiscountValue;

	list<T_GlobleOfrInfo> lstGlobleOfrInfo;
    T_PrdInst()
    {
        memset(sServNumber,0x00,sizeof(sServNumber));
        memset(sBasicState,0x00,sizeof(sBasicState));
        memset(sUrbanFlag,0x00,sizeof(sUrbanFlag));
        memset(sUserTypeID,0x00,sizeof(sUserTypeID));
		memset(szUserType,0x00,sizeof(szUserType));
		memset(szMVNOID,0x00,sizeof(szMVNOID));
        lAcctID = -1;
        nUserMode = -1;
        lCustID = -1;
        lPrdInstID = -1;
        lnPrdID = -1;
        iOfrID = -1;
        iLatnID = -1;
        sAreaCode[0] = 0;
        lnUserStasID = -1;
		nIvpnFlag = 0;		
		nDiscountValue = 100;
		nBaodiFee = 0;
		lnGlobalOfrInstId = -1;
		lstGlobleOfrInfo.clear();
    }
};
typedef std::vector<T_PrdInst*> T_vecPrdInst;
typedef std::vector<T_PrdInst*>::iterator T_VecPrdInstIter;


//全局用户信息结构体,批价时用
struct S_GlobalUserInfo               
{
    long lnEventTypeId;                     
    int nDeductType; 
    long lnAcctID;
    long lnServID;
    int nLatnId;
    S_GlobalUserInfo()
    {
        lnEventTypeId = -1;
        nDeductType = -1;
        lnAcctID = -1;
        nLatnId = -1;
        lnServID = -1;
    }
    
};

struct S_ReDuctOfr                      //补扣销售品结构体
{
    long lReDuctID;                     //补扣记录ID
    long lOfrID;                        //补扣销售品ID
    int iDeductType;                    //扣费类型，0为0则不向ABM发送扣费请求，直接记录新的扣费日志;为1则发送扣费请求
    char szStartTime[24];               //补扣开始时间
    char szEndTime[24];                 //补扣结束时间
};

struct S_ReDuctUser	                    //补扣用户信息结构体
{
    T_PrdInst* pUserInfo;		        //补扣用户信息，从主产品实例表中获取
    vector<S_ReDuctOfr*> vecOfrInfo;	//补扣用户对应销售品信息
    S_ReDuctUser()
    {
        pUserInfo = NULL;
        vecOfrInfo.clear();
    }
    ~S_ReDuctUser()
    {
        if (NULL != pUserInfo)
        {
            delete pUserInfo;
            pUserInfo = NULL;
        }
        for (int i=0; i<(int)vecOfrInfo.size(); i++)
        {
            if (NULL != vecOfrInfo[i])
            {
                delete vecOfrInfo[i];
                vecOfrInfo[i] = NULL;
            }
        }//end for i
    }
};
typedef std::vector<S_ReDuctUser*> T_vecReDuctUser;
typedef std::vector<S_ReDuctUser*>::iterator T_vecReDuctUserIter;
typedef std::map<long, T_vecReDuctUser> T_mapReDuctUser;
typedef std::map<long, T_vecReDuctUser>::iterator T_mapReDuctUserIter;


struct T_UserInfo
{	
	int nUserMode;     //用户类型 1:新装;2复机;3老用户;4试算
	int nObversionMode;  //折算方式:0(全月),1(按比例),复机用户专用
	int nTriggerCycle;  //触发账期,新装填新装表的create_date,复机填复机表的state_time,老用户填当前系统时间所属账期,截取前8位
	T_UserInfo()
	{
		nObversionMode = 0;
		nUserMode = -1;
		nTriggerCycle = -1;
	}
	
};


struct T_FrozenInfo
{
	char cStateTime[30];		//变更时间
	long lFrozenId;			//记录标识，通过seq产生
	long lPrdInstId;			//主产品实例ID
	long lLatnId;				//本地网标识
	int nObversionMode;  //折算方式:0(全月),1(按比例)
	T_FrozenInfo()
	{
		memset(cStateTime, 0x00, sizeof(cStateTime));
		lFrozenId = -1;
		lPrdInstId = -1;
		lLatnId = -1;
		nObversionMode = 0;
	}
	
};
typedef vector<T_FrozenInfo*> T_vecFrozenInfo;
typedef vector<T_FrozenInfo*>::iterator T_VecFrozenInfoIter;


/*
//销售品
struct T_PrdOfr
{
    int iOfrID;//ofr_id
    int iOfrTypeID;//ofr_type_id
    int iPricingPlanID;//定价计划
    int iLatnID;//本地网标识
    OfrType iFlag;//销售品类型0:基础 1:优惠2:附属3:保底
    //long iOfrInstID;//销售品实例id
    list<long> ltOfrInstID; //由于西藏存在一个OFR-iD对应二个不同的销售品实例(一个是用户级A1,一个是集团级F1)
    int iPricingSectionID;//定价段落，只针对附属销售品
	std::list<long> ltStategyID;     //定价策略，只针对附属销售品
    std::list<long> ltSubPrdID;      //附属产品ID，只针对附属销售品
    bool bHirstoryFlag;//更新记录是否存在标识 false不存在 true存在
	int nDisctValue;	//IVPN折扣率，默认为100
    char szEffDate[15]; //生效时间
    char szExpDate[15]; //失效时间
    list<T_BalanceContent> ltDeduct; //存储当前套餐批价费用
  	list<STRatableUpdateReq> ltReqUpdateRatable; //用于存储需要更新的累积量信息
  	list<int> ltGroup;
    T_PrdOfr()
    {
        iOfrID = -1;
        iOfrTypeID = -1;
        iPricingPlanID = -1;
        iLatnID = -1;
        iFlag = OFR_OTHER;
        //iOfrInstID = -1;
        ltOfrInstID.clear();
        iPricingSectionID   = -1;
        bHirstoryFlag   = true;
        ltStategyID.clear();
        ltSubPrdID.clear();
		ltDeduct.clear();
		ltReqUpdateRatable.clear();
		ltGroup.clear();
		nDisctValue = 100;
        memset(szEffDate,0,sizeof(szEffDate));
        memset(szExpDate,0,sizeof(szExpDate));
    }
};*/
typedef map<int,T_PrdOfr* > T_mapPrdOfr;
typedef map<int,T_PrdOfr* >::iterator T_mapPrdOfrIter;

//销售品明细信息
struct T_offerDetail
{
    long 		m_lOfferId;
    int 		m_lDetailId;
    std::string 		m_sElemntType;
    std::string 		m_sObjType;
};

struct T_detailInstance
{
    long 			m_lOfferInstanceId;		//销售品实例id,主产品实例为prd_inst_id,附属产品实例为prd_inst_id
    T_offerDetail 	*m_pCurrDetail;
    long 			m_lDetailId;
    long 			m_lDetailInstanceId;	//销售品实例id,主产品实例为prd_inst_id,附属产品实例为prd_inst_id
    long			m_lInstanceId;			//销售品实例id,主产品实例为prd_inst_id,附属产品实例为prd_inst_id
    std::string 			m_sInstanceType;		//'B1'
    int				m_nOfrType;				//0 基础和附属基础销售品
    int				m_nOfrInstLatnId;		//销售品归属本地网
    int				m_nLatnId;				//销售品明细归属本地网
    long			m_lObjId;				//obj_id 优惠销售品为 ofr_detail_inst_ref_id,基础销售品为prd_inst_id
    //附属销售品为 main_prd_inst_id
};



//用户级保底累积量信息
struct T_GlobleRatableInfo
{    
    long lnGlobalRatableId;    //全局累积量ID
	int  nBaodiFee;             //全局累积量对应的保底费用
	int  nGlobalResouceAmount; //当前累积值
	bool bFindGlobalResource;
	long lnUpdateRatableValue; //全局累积量需要更新的值
	long lnFavorRatableValue;  //实际全局累积量减免的值	
	long lnGlobalOfrId;
	long lnGlobalOfrInstId;
	T_GlobleRatableInfo()
	{
		lnGlobalRatableId = -1;    
	    nBaodiFee = 0;            
	    nGlobalResouceAmount = 0;
	    bFindGlobalResource = false;
	    lnUpdateRatableValue = 0; 
	    lnFavorRatableValue = 0; 	
	    lnGlobalOfrId = -1;
	    lnGlobalOfrInstId = -1;
	}
};

//策略级剔除(被互斥策略信息)
struct ST_MutexOfrInfo
{
	int nOfrId;
	long lnStrategyId;

public:
	ST_MutexOfrInfo()
	{
		nOfrId = -1;
		lnStrategyId = -1;
	}
};

//cycle_ofr_relation表信息(策略级互斥)
struct ST_CycStrategyRel
{
	int   nMainOfrId;
	int   nRefOfrId;
	long  lnMainStrategyId;
	long  lnRefStrategyId;
	int   nIfDeduct;
	int   nDictValue;
	int   nConstValue;
	int   nPriGroupId;

public:
	ST_CycStrategyRel()
	{
		nMainOfrId = -1;
		nRefOfrId = -1;
		lnMainStrategyId = -1;
		lnRefStrategyId = -1;
		nIfDeduct = -1;
		nDictValue = -1;
		nConstValue = -1;
		nPriGroupId = -1;
	}
};

//cycle_ofr_relation表信息(销售品级互斥)
struct ST_CycOfrRel 
{
public:
    ST_CycOfrRel()
    {
        nRefOfrId = -1;
        nIfDeduct = -1;
        nDictValue = -1;
        nConstValue = -1;
		nPriGroupId = -1;
    }

    int nRefOfrId;
    int nIfDeduct;
    int nDictValue;
    int nConstValue;
	int nPriGroupId;	//定价组ID，默认为-1
};

struct ST_OfrGroup
{
public:
    int MainOfrID;
    int nGroupID;

    ST_OfrGroup()
    {
        MainOfrID = -1;
        nGroupID = -1;
    }
};


//cycle_ofr_profile表信息
struct ST_CycOfrPro
{
public:
    ST_CycOfrPro()
    {
        nEffType = -1;
        nEventTypeId = -1;
        nDecuctType = -1;
		nIvpnFlag = 0;
		nBroadbandFlag = 0;
		nOffSet = -1;
        nOfrAttr = -1;
    }

    int nEffType;
    int nEventTypeId;
    int	nDecuctType;    //nDecuctType为0表示动态月，为1时表示动态日，为2时表示按入网日扣费
	int nIvpnFlag;		//IVPN打折标识，0不打折，1打折
	int nBroadbandFlag;	//针对无线宽带，0非宽带，1宽带
	int nOffSet;        //当nDecuctType为0时，表示偏移nOffSet个月单位；当nDecuctType为1时，表示偏移nOffSet个日单位
	int nOfrAttr;       //销售品属性，0：固网AAA销售品，默认为-1
};


//定价条件结构
struct ST_RateCondSeq
{
public:
    ST_RateCondSeq()
    {
        m_groupId = -1;
        m_seqId = -1;
        m_comType = -1;
        m_itemCode = "";
        m_comOperators = -1;
        m_itemValue = "";
    }

    int    m_groupId;
    int    m_seqId;
    int    m_comType;
    std::string m_itemCode;
    int    m_comOperators;
    std::string m_itemValue;

};

//累计量信息
struct ST_StrategyRatable 
{
public:
    ST_StrategyRatable()
    {
        nEventStrategyId = -1;
        pszRatable = -1;
		szPriResourceOwnerType[0] = '\0';
		nBaseRatable = -1;
    }

    int  nEventStrategyId;
    int  pszRatable;
	char szPriResourceOwnerType[4];
	int  nBaseRatable;
};


//定价段落信息
struct ST_Section
{
    ST_Section()
    {
        nRatableID = -1;
		lnTariffID = -1;
		nTailMode = -1;
		memset(szOperSwitch, 0x0, sizeof(szOperSwitch));
    }

    int nSectionID;
    int nSectionTypeId;
    long strScaledRateValueId;
    int nAcctItemTypeId;
    int nConditionId;
    int nCycleSectionBegin;
    int nCycleSectionDuration;
    int	nStartRefValueId;
    int nEndRefValueId;
    long lnEventPricingStrategyId;
    int strResourceCode;
    std::string strPresentSetId;
    int nRentUnitId;
    int nMinCounts;
    int nRatableID;
	long lnTariffID;
	char szOperSwitch[2];
	int nTailMode;  //1向下取整；2向上取证；3四舍五入
};

struct ST_CycleHistory
{
    int nOfrID;
    int nLatnId;
    int nOfrType;
    long lnOfrDetailInstID;
    long lnOfrInstID;
    long lnEvtID;
    long lnDeductDAY;
    long lnTimes;
    string strPresentLastTime;

    ST_CycleHistory()
    {
        lnTimes = -1;
        lnDeductDAY = 0;
        lnOfrDetailInstID = -1;
        lnOfrInstID = -1;
    }

};

struct ST_RealDeduct
{
    string strDeductTime;
    string strSessionID;
    string strAreaCode;
    string strDeductNumber;
    int    nLatnId;
    long   lnRealFee;
    long   lnAcctItem;
    int    nUnitType;
    long   lnPrdInstID;
    int    nDeductTYPE;
    int    nServiceType;
    int    nOfrID;
};

/*
struct RatableResource
{
RatableResource()
{
life_type[0] = 0;
ref_type[0] = 0;
base_resource_code[0] = 0;
resource_owner_type[0] = 0;
}

char life_type[4];
char ref_type[4];
int  ref_offset;
int  usage_type;
char base_resource_code[4];       //主累积量标识
char ratable_resource_type[4];    //累积量类型标识  1－时长(秒)； 2－时长(分钟)；3－次数；4－总流量(k)；5－分(金额)；7－上行流量按K；8－下行流量 
char resource_owner_type[4];      //累积量属主标识 80A用户、80I客户、80J账户
};

*/
struct BaseRentRatableResource
{
    BaseRentRatableResource()
    {
        life_type[0] = 0;
        ratable_resource_type ='\0';
    }

    char life_type[4];
    int  start_value;
    int  end_value;
    int  life_time;
    int  base_resource_id;
    char ratable_resource_type; //累积量类型标识  1－时长(秒)； 2－时长(分钟)；3－次数；4－总流量(k)；5－分(金额)；7－上行流量按K；8－下行流量 
};


struct STBaseRentRatable 
{
    //char szRatable_resource_code[4];  //主累计量代码
    long  nRatable_id;                //主累计量代码
    char szResource_owner_type[4];   //累计量属主类型
    int  nBase_life_type;                   //日期类型  0：按天 1：按帐期
    int  nStart_value;                        //开始偏移
    int  nEnd_value;                          //结束偏移
    int  nRatable_resouce_type;  //累积量类型 1－时长(秒)； 2－时长(分钟)；3－次数；4－总流量(k)；5－分(金额)；7－上行流量按K；8－下行流量
    int  nLife_type;                     //日期类型  0：按天 1：按帐期
    int  nRef_offset;                   //参考偏移
    char szRef_type[4];              //参考类型
    long lRatble_cycle_type_iD;      //自定义帐期类型ID

    STBaseRentRatable()
    {
        nRatable_id = -1;
        memset(szResource_owner_type,0,sizeof(szResource_owner_type));
        memset(szRef_type,0,sizeof(szRef_type));
    }
};

struct STSubOfr
{
    int nOfrID;
    long lnPrdID;
    int nSectionID;
    int nPricingPlanID;
    long lnStrategyID;

    STSubOfr()
    {
        nOfrID = -1;
        lnPrdID = -1;
        nSectionID = -1;
        nPricingPlanID = -1;
        lnStrategyID = -1;
    }
};

struct STEvtPro
{
    long lnEventID;
    long lnOfrType;
    bool bTrial; 
    STEvtPro()
    {
        lnEventID = -1;
        lnOfrType = -1;  
        bTrial = false;
    }
};

struct STIvpnOfrRel
{
	long lnGroupId;
	int  nPricingPlanId;
	int  nOfrId;
    int  nDiscVal;

	STIvpnOfrRel()
	{
		lnGroupId = -1;
		nPricingPlanId = -1;
		nOfrId = -1;
		nDiscVal = 100;
	}
};


struct STObjInputRule 
{
	long     lnStrategyRuleRelId;
	long     lnStrategyId;
	long     lnRuleId;
	int      nOperaterType;
	string   strOriginCode;
	string   strDestCode;
	int      nConstructType;
	string   strConstructContet;
	int      nBillingOffset;

	STObjInputRule()
	{
		lnStrategyRuleRelId = -1;
		lnStrategyId = -1;
		lnRuleId = -1;
		nOperaterType = -1;
		nConstructType = 0;
		nBillingOffset = -1;
	}

	void clear()
	{
		lnStrategyRuleRelId = -1;
		lnStrategyId = -1;
		lnRuleId = -1;
		nOperaterType = -1;
		nConstructType = 0;
		nBillingOffset = -1;
		strOriginCode = "";
		strDestCode = "";
		strConstructContet = "";
	}
};

//停机保号用户信息
struct STUserStopState
{
	int   nStateId;
	char  szBasicState[5];
	long  lnUserStasId;

	STUserStopState()
	{
		nStateId = -1;
		szBasicState[0] = '\0';
		lnUserStasId = -1;
	}
};

//用户停机保号key
struct STUserStopFeeKey
{
	int nLatnID;
	int nOfrId;

	STUserStopFeeKey()
	{
		nLatnID = -1;
		nOfrId = -1;
	}

	void SetData(int _nLatnId,int _nOfrId)
	{
		nLatnID = _nLatnId;
		nOfrId  = _nOfrId;
	}

	//结构体作为map的key需重载<
	bool operator< (const STUserStopFeeKey &TmpKey) const
	{
		if (nLatnID < TmpKey.nLatnID)
		{
			return true;
		}
		else if (nLatnID == TmpKey.nLatnID)
		{
			return nOfrId<TmpKey.nOfrId;
		}
		return false;
	}
};

//用户停机保号费用
struct STUserStopFee
{
	int    nStopFeeId;
	int    nOfrId;
	int    nLatnID;
	long   lnFee;
	long   lnAcctItemTypeId;

	STUserStopFee()
	{
		nStopFeeId = -1;
		nOfrId = -1;
		nLatnID = -1;
		lnFee = 0;
		lnAcctItemTypeId = -1;
	}
};

struct STTimeAttr
{
    char m_szYYYY[8];
    char m_szMM[4];
    char m_szDD[4];	
    char m_szHH[4];	
    char m_szMI[4];	
    char m_szSS[4];

    STTimeAttr()
    {
        memset(m_szYYYY,0,sizeof(m_szYYYY));
        memset(m_szMM,0,sizeof(m_szMM));
        memset(m_szDD,0,sizeof(m_szDD));	
        memset(m_szHH,0,sizeof(m_szHH));
        memset(m_szMI,0,sizeof(m_szMI));	
        memset(m_szSS,0,sizeof(m_szSS));
    }
};

//------------------------------月租异常表记录 begin-------------------------
struct STRentRecordExp
{
    char szSession_id[60];            // 会话ID
    char szArea_code[10];             //用户归属区号
    char szService_nbr[20];           //用户号码
    long lnPrd_inst_id;			      //主产品实例ID
    char szDeduct_time[20];           //扣费日期
    int  nEvent_type_id;              //扣费事件ID
    int  nOfr_id;                     //销售品ID
    int  nUser_type;                  //销售品类型 0:基础 1:优惠 2:保底 4:附属 5:增值类销售品 9:其他
    int  nExp_type;                   //异常类型 0:表示累计量查询超时; 1:表示扣费超时, 2:表示扣费失败
    char szDeduct_Info[200];          //扣费费用信息 包括: 账目类型,账本类型,金额数量. 如果不是扣费异常,默认填-1
    char szRatable_Info[200];         //累计量更新信息 包括: 累积量代码,帐期ID,累积量数量. 如果不是累计量异常,默认填-1
	int  nAbm_Ret_Code;
};
//------------------------------月租异常表记录 end---------------------------

//------------------------------月租欠费表记录 begin-------------------------
struct STRentDealArrear 
{
    long lnArrear_id;                 //欠费ID
    char szSession_id[64];            //会话ID
    char szService_nbr[20];			  //用户号码
    char szAera_code[8];              //用户区号
    char szAccount_type_list[512];    //欠费帐目类型列表 包括: 账目类型,账本类型,金额数量
    char szResource_list[512];        //欠费累计量信息列表 包括: 累积量代码,帐期ID,累积量数量
    char szOperate_time[16];          //欠费日期
    int  nArrear_type;                //欠费类型 0表示OCS发起扣费, 1表示周期性费用发起扣费, 2表示离线程序发起扣费
    int  nOperate_state;              //处理状态 -1表示未做处理完, 0表示处理完毕 1表示扣费未知, 默认值：-1

    STRentDealArrear()
    {        
        memset(szAccount_type_list,0,sizeof(szAccount_type_list));
        strcpy(szResource_list, "-1");
        nArrear_type = 1;
        nOperate_state = -1;
    }

    void clear()
    {
        memset(szAccount_type_list,0,sizeof(szAccount_type_list));
    }
};

struct STRentArrear 
{
    long lnAcctID;
    long lnServID;
    int  nLatnID;
    char szSessionID[256];
    char szServNbr[20];
    char szAreaCode[8];
    char szArrearTime[15];
    char szBillingID[9];    
    list<T_BalanceContent> ltArear;  

    void Init()
    {
        ltArear.clear();
        memset(szSessionID,0,sizeof(szSessionID));	
        memset(szServNbr,0,sizeof(szServNbr));	
        memset(szAreaCode,0,sizeof(szAreaCode));	
        memset(szArrearTime,0,sizeof(szArrearTime));	
        memset(szBillingID,0,sizeof(szBillingID));	
    }
};

//------------------------------月租欠费表记录 end---------------------------

struct STRentBillInfo
{
	long lnServID;                      //用户编号
	long lnCustID;                      //客户编号

    int nCorrelationID;                 //同一个会话的话单的唯一标识，只用于GPRS或3G数据业务
    int nTicketSequenceID;              //同一个会话的流水号，只用于GPRS或3G数据业务
    int nServiceScenarious;             //业务类型 800表示周期性费用        
	int nVersion;                       //话单版本号
	int nTicketType;                    //话单类型 网元触发:0 OCS产生:1
	int nPayFlag;                       //主叫或被叫付费标识, 1主叫付费 2被叫付费 3其他付费方式
	int nCallingOperator;               //主叫运营商 1为中国电信
	int nCalledOperator;                //被叫运营商
	int nSubOperator;                   //计费运营商
	int nCdrSequenceNunmber;
	int nFee1;                          //实扣费用, 基本费，单位分(金额) 
	int nAcctItemTypeID;                //帐目类型
	int nNoDeduct;                      //欠费金额，单位分
	int nTerminatedCause;               //终止原因 同OCP中的Result-code
    int nEventId;                       //事件ID        //add by huanghua 20110309 5.2.1

    char szChargedParty[20];            //计费方号码
    char szCallingParty[20];            //规整后的主叫号码
    char szCalledParty[20];             //规整后的被叫号码
    char szOrignialCallingParty[20];    //原始主叫号码
    char szOrignialCalledParty[20];     //原始被叫号码	
	char szEventCause[32];              //周期性费用扣费类型	
	char szTimeStamp[16];               //话单产生时间，格式：YYYYMMDDHHmmSS
	char szHostID[64];                  //OCS主机名，记录OCS的标识
    char szMasterProductID[20];         //主产品标识 1:3G 2:ADSL 3:PHS 4:固话   
    char szSessionId[64];               //SessionId
    char szSessionTerminatedTime[16];   //会话结束时间
    char szOrignialHost[64];            //orignial host
    char szBalanceinfo[256];            //本次事件中改变的账本信息
    char szAccumlatorinfo[256];         //本次事件中改变的累计量信息  
	char szChargeInfo[256];             //本次事件中改变的帐目类型和数量信息
    char szTariffId[128];               //套餐Id组       
    char szBalanceAmount[16];           //用户总余额    //add by huanghua 20101112
    char szPrsGrp[512];                 //定价计划组    //add by huanghua 20110309 5.2.1

    STRentBillInfo()
    {
        nVersion = 0;
        nTicketType = 1;
        strcpy(szHostID, "ocs-host");
        nCorrelationID = 0;
        nTicketSequenceID = 0;
        nServiceScenarious = 800;
        strcpy(szCalledParty, "0");
        strcpy(szOrignialCalledParty, "0");
        nPayFlag = 3;
        strcpy(szMasterProductID, "1");
        nTerminatedCause = 0;
        strcpy(szOrignialHost, "ocs-host");
        strcpy(szBalanceinfo, "0");
        strcpy(szAccumlatorinfo, "0");
        strcpy(szTariffId, "0");
        szPrsGrp[0] = '\0';
        nEventId = 0;
        nCallingOperator = 1;
        nCalledOperator = 1;
        nSubOperator = 1;
        nCdrSequenceNunmber = -1;
		szChargeInfo[0] = 0;
        nFee1 = 0;
        nAcctItemTypeID = -1;
        nNoDeduct = 0;
        strcpy(szBalanceAmount, "null");
    }

    void clear()
    {
		szChargeInfo[0] = 0;
		szTariffId[0] = 0;
        szPrsGrp[0] = '\0';
    }
};

//1时间|2会话ID|3区号|4电话号码|用户标识（prd_inst_id）|销售品ID|事件ID|未扣类型（保底/用户状态）|本地网标识
struct STRentNotNeedInfo
{
	string strDealTime;
	string strAreaCode;
	string strServNbr;
	long   lnPrdInstId;
	long   lnOfrId;
	long   lnEventId;
	int    nSectionId;
	int    nLatnId;
	string strNotNeedType;

	STRentNotNeedInfo()
	{
		strDealTime = "";
		strAreaCode = "";
		strServNbr = "";
		lnPrdInstId = -1;
		lnOfrId = -1;
		lnEventId = -1;
		nLatnId = -1;
		strNotNeedType = "";
		nSectionId = -1;
	}

	void clear()
	{
		strDealTime = "";
		strAreaCode = "";
		strServNbr = "";
		lnPrdInstId = -1;
		lnOfrId = -1;
		lnEventId = -1;
		nLatnId = -1;
		strNotNeedType = "";
		nSectionId = -1;
	}
};

struct STOfrRatableRel
{
	int nOfrId;             //销售品ID
	long lnRatableResourceId; //累积量ID
	int  nBaodiFee;			//保底费用
	STOfrRatableRel()
	{
		nOfrId = -1;
		lnRatableResourceId = -1;
		nBaodiFee = 0;
	}
};


struct STGlobalRatableCond
{
	long lnRatableResourceId; //累积量ID
	int nGroupId;				//条件组ID
	int nGroupSeq;				//组内序列号
	char szComType[3];			//比较类型:10数值比较;20字符串比较
	char szRecordType[7];		//ACT或EVT
	char szComOperator[3];		//比较操作符
	char szItemValue[120];		//字段和值比较,直接作为右值比价,字段和字段比较时，szItemValue是一个code,需要增强
	char source_type[4];
	char value_type[4];
	char left_source_type[2];
	char right_source_type[2];
	STGlobalRatableCond()
	{
		lnRatableResourceId = -1;
		nGroupId = -1;
		nGroupSeq = -1;
		memset(szComType, 0x0, sizeof(szComType));
		memset(szRecordType, 0x0, sizeof(szRecordType));
		memset(szComOperator, 0x0, sizeof(szComOperator));
		memset(szItemValue, 0x0, sizeof(szItemValue));
		memset(source_type, 0x0, sizeof(source_type));
		memset(left_source_type, 0x0, sizeof(left_source_type));
		memset(right_source_type, 0x0, sizeof(right_source_type));
	}
};


typedef struct STSecNotNeedFlag
{
	int nSectionId;
	string strFlag;
}STSecNotNeedFlag;

typedef struct STSecNNeedParm
{
	int nSectionId;
	int nMinCount;
	int RecordType;

	void SetParm(int vSectionId,int vMinCount,int vRecordType)
	{
		nSectionId = vSectionId;
		nMinCount = vMinCount;
		RecordType = vRecordType;
	}
}STSecNNeedParm;

typedef struct STConLimitFlag
{
	int nConditionId;
	int nValue;

	STConLimitFlag()
	{
		nConditionId = -1;
		nValue = 0;
	}
}STConLimitFlag;

enum enUserType
    {
        USER_INST_CHANGE = 1,  //新装
        USER_INST_STATE_CHANGE = 2,  //复机补扣用户
        USER_BASE_CHANGE = 3,   //老用户
        USER_TRIAL = 4   //试算用户    
    };

enum enMsgType
    {
        MSG_ANS_RESOURCE = 401,  //累积量查询应答
        MSG_ANS_BALANCE_QUERY = 403, //余额查询应答
        MSG_ANS_DEDUCT = 411,  //扣费应答  
        MSG_ANS_TEST_DEDUCT = 417  //试扣应答
    };
enum enABMRetCode
    {
        ABM_TIMEOUT = 100001  //abm超时            
    };

#endif

#ifndef _SUMMSGPROXY_DCCONFIG_H_
#define _SUMMSGPROXY_DCCONFIG_H_

#include <stdlib.h>
#include <time.h>
#include <string.h>
#include <fcntl.h>
#include <unistd.h>
#include <dirent.h>
#include <sys/types.h>
#include <sys/time.h>

#include <string>
#include <list>
#include <vector>
using std::string;
using std::list;
using std::vector;

#include "DCLogMacro.h"
#include "TThread.h"
#include "DataDef.h"
#include "DCGrayscaleRoute.h"

class DCConf : public TThread
{
public:
    DCConf();
    ~DCConf();

    static DCConf* Instance();

    int Init(const STConfigPara &cfgpara);

	virtual void routine();
	
public:
	// 灰度调用相关
    string m_strSubscriber;
    string m_strRouteProcess;
	int m_nRefreshIntr; 
	
private:
    static DCConf* m_inst;
	DCDBManer* 		m_dbm;
};

#endif
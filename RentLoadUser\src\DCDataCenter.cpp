/*******************************************
*Copyrights  2008， 深圳天源迪科信息技术股份有限公司
*						在线计费项目组
*All rights reserved.
*
* Filename：	DCDataCenter.cpp
* Indentifier：	
* Description：	月租初始化类
* Version：		V1.0
* Author:		lihk
* Finished：	2008年7月30日
* History:		
********************************************/
#include "DCDataCenter.h"
//#include "common/DCAcctFunction.h"
#include "DCLogMacro.h"
#include "publiclib.h"

using namespace std;

DCDataCenter* DCDataCenter::m_pDataCenter = NULL;

int DCDataCenter::Release()
{
    if (NULL != m_pDataCenter)
    {
        delete m_pDataCenter;
        m_pDataCenter = NULL;
    }
    
    return 0;
}


DCDataCenter::DCDataCenter(void)
{
    bbzero(m_szLogPath);

 	iPid = getpid();
	
	m_isNeedDealCrossUser = false;
}

DCDataCenter::~DCDataCenter(void)
{
    Release();
}

//初始化参数
int DCDataCenter::Init()
{      
    
    if (InitConfig())	//读取配置
    {
        return -1;
    }	

    DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"", "DCDataCenter::Init end!");
    return 0;
}



//初始化配置参数
int DCDataCenter::InitConfig()
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","DCDataCenter::InitConfig begin!");

    //试扣
    int nYear = 0;
    int nMonth = 0;
    int nDay = 0;    	

    char szYear[5] = {0};
	char szMonth[3] = {0};
	char szDay[3] = {0};
	char szBillingCycle[9] = {0};  		
	PublicLib::GetTime(szBillingCycle, YYYYMMDD);	    
	szBillingCycle[8] = '\0';
	strncpy(szYear, szBillingCycle, 4);
	strncpy(szMonth, szBillingCycle + 4, 2);
	strncpy(szDay, szBillingCycle + 6, 2);
	nYear = atoi(szYear);
	nMonth = atoi(szMonth);
	nDay = atoi(szDay);
    int nCurrentMaxDay = DCDateTime::GetMaxDay(nYear,nMonth);

	DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "","DCDataCenter::InitConfig end!");

    return 0;
}

//刷新账期
int DCDataCenter::RefreshBillingCycle(int nBillingCycleID,int nLatnId)
{
    iBillingCycleId = nBillingCycleID;
    iPrevBillingCycleId = PreMonth(iBillingCycleId);
    iNextBillingCycleId = NextMonth(iBillingCycleId);
    iCycleEndDay = MonthEndDay(iBillingCycleId);
    iEndDay = EndDay(iBillingCycleId);
    MonthInfo(iBillingCycleId,sCycleBeginDate,sCycleEndDate,sCycleBeginTime,sCycleEndTime);
    MonthInfo(iPrevBillingCycleId,sPrevCycleBeginDate,sPrevCycleEndDate,sPrevCycleBeginTime,sPrevCycleEndTime);
    MonthInfo(iNextBillingCycleId,sNextCycleBeginDate,sNextCycleEndDate,sNextCycleBeginTime,sNextCycleEndTime);

	m_nLatnId = nLatnId;
    return 0;
}

//根据当前月推算上月
int DCDataCenter::PreMonth(int vi_iMonth)
{
	char sMonth[7] = {0};
	char sY[5] = {0};
	char sM[3] = {0};

	sprintf(sMonth,"%d",vi_iMonth);
	if (6 != strlen(sMonth))
	{
		return -1;
	}
	strncpy(sY,sMonth,4);
	strncpy(sM,sMonth+4,2);

	int iY = atoi(sY);
	int iM = atoi(sM);

	if (1 == iM)
	{
		iM = 12;
		iY = iY - 1;
	}
	else
	{
		iM = iM - 1;
	}

	int iPreMonth = iY * 100 + iM;
	return iPreMonth;
}

//根据当前月推算下月
int DCDataCenter::NextMonth(int vi_iMonth)
{
	char sMonth[7] = {0};
	char sY[5] = {0};
	char sM[3] = {0};

	sprintf(sMonth,"%d",vi_iMonth);
	if (6 != strlen(sMonth))
	{
		return -1;
	}
	strncpy(sY,sMonth,4);
	strncpy(sM,sMonth+4,2);

	int iY = atoi(sY);
	int iM = atoi(sM);

	if (12 == iM)
	{
		iM = 1;
		iY = iY + 1;
	}
	else
	{
		iM = iM + 1;
	}

	int iNextMonth = iY * 100 + iM;
	return iNextMonth;
}

//获取当前月结束日
int DCDataCenter::MonthEndDay(int vi_iMonth)
{
	int nY = vi_iMonth / 100;
	int nM = vi_iMonth % 100;
	int nEndDay;

	if (2 == nM)
	{
		if (0 == nY % 400 || (0 == nY % 4 && 0 != nY % 100))  // 闰年
		{
			nEndDay = 29;
		}
		else
		{
			nEndDay = 28;
		}
	}
	else if (1 == nM || 3 == nM || 5 == nM || 7 == nM || 8 == nM || 10 == nM || 12 == nM)
	{
		nEndDay = 31;
	}
	else
	{
		nEndDay = 30;
	}

	return nEndDay;
}

//获取当前日――相对于指定帐期
int DCDataCenter::EndDay(int nBillingCycleID)
{	
	char pszTime[10]={0};
	PublicLib::GetTime(pszTime, YYYYMMDD);
	
	int nSystemMonth = atoi(pszTime)/100;

	if (nSystemMonth > nBillingCycleID)
	{
		return MonthEndDay(nBillingCycleID);
	}
	else
	{
		return (int)(atoi(pszTime)%100);
	}
}


//获取当前月相关信息
int DCDataCenter::MonthInfo(int vi_iMonth,char *vo_sBeginDate,char *vo_sEndDate,char *vo_sBeginTime,char *vo_sEndTime)
{
	if (100001 > vi_iMonth || 999912 < vi_iMonth)
	{
		return -1;
	}

	sprintf(vo_sBeginDate,"%d01",vi_iMonth);
	sprintf(vo_sEndDate,"%d%02d",vi_iMonth,MonthEndDay(vi_iMonth));
	sprintf(vo_sBeginTime,"%d01000000",vi_iMonth);
    // add yangp 20110919
#ifndef __SHANGHAI__
	sprintf(vo_sEndTime,"%d%02d235959",vi_iMonth,EndDay(vi_iMonth));
#else
    sprintf(vo_sEndTime,"%d%02d235959",vi_iMonth,MonthEndDay(vi_iMonth));
#endif

	return 0;
}


/****************************************************************************************
*Copyrights  2006，深圳天源迪科计算机有限公司
*						OCS项目组
*All rights reserved.
*
* Filename：	ConfigureFile.cpp		
* Indentifier：		
* Description： 读配置		
* Version：		V1.0
* Author:		guoxd
* Finished：	2006年12月27日
* History:		
******************************************************************************************/

#include "ConfigureFile.h"
#include "SysParam.h"
#include "trim.h"

#include <stdlib.h>
#include <string.h>
#include <assert.h>

#if defined(STDIOSTREAM_DISABLE)
#include <iostream.h>
#else
#include <iostream>
#endif


// define all comment prefix
const char * CONF_COMMENTS = ";#";

// key and value delimiter
const char * KEYVALDELIM = "=";


//##ModelId=3BCBD14F0341
bool ConfigureFile::initialization(const char *filename/*=0*/)
{
  if (!SysParam::initialization(filename))
    return false;

  if (filename == 0)
  {
    m_envName = DCONF_ENV;

    char *env;
    if ((env = getenv(m_envName.c_str())) == 0)
      return false;
    m_filename = env;
  }
  else
    m_filename = filename;

  return true;
}

//##ModelId=3BB2E8FD0091
bool ConfigureFile::end()
{
  return true;
}

//##ModelId=3BB2E8FD00A5
bool ConfigureFile::getValue(const string& sectionPath, const string& name,
                             string& value)
{
  if (SysParam::getValue(sectionPath, name, value))
    return true;

  m_fstreamConfig.clear();
  m_fstreamConfig.open(m_filename.c_str(), ios::in);
  if (m_fstreamConfig.rdstate() != ios::goodbit)
  {
    m_fstreamConfig.close();
    return false;
  }

  if (!locate(sectionPath))
  {
    m_fstreamConfig.close();
    return false;
  }

  char linebuf[2049];
  string line;
  string section, key;
  string pathNew;
  LINE_TYPE linetype;
  bool isOk = false;
  while (m_fstreamConfig.getline(linebuf, 2048))
  {
    line = linebuf;

    linetype = lineType(line);
    if (linetype == LINE_COMMENT)
      continue;

    if ((linetype == LINE_KEYVAL) && (keyLineProcess(line, key, value)))
    {
      if (name != key)
        continue;
      isOk = true;
      break;
    }

    bool isBegin;
    if ((linetype == LINE_SECTION)
        && (sectionLineProcess(line, section, isBegin)))
    { // check for section
      if (isBegin)
      {
        string pathNew = sectionPath + SECTDELIM + section;
        if (toSectionEnd(section))
          continue;
      }
    }
    break;
  }
  m_fstreamConfig.close();
  return isOk;
}

bool ConfigureFile::ModifyValue(const string& sectionPath, const string& name,string& value)
{
  if (SysParam::getValue(sectionPath, name, value))
    return true;
  m_fstreamConfig.clear();
  m_fstreamConfig.open(m_filename.c_str(), ios::out|ios::in);
  if (m_fstreamConfig.rdstate() != ios::goodbit)
  {
    m_fstreamConfig.close();
    return false;
  }

  if (!locate(sectionPath))
  {
    m_fstreamConfig.close();
    return false;
  }

  char linebuf[2049];
  string line;
  string section, key,newvalue;
  string pathNew;
  LINE_TYPE linetype;
  bool isOk = false;
  string::size_type pos;
  while (m_fstreamConfig.getline(linebuf, 2048))
  {
    line = linebuf;

    linetype = lineType(line);
    if (linetype == LINE_COMMENT)
      continue;

    if ((linetype == LINE_KEYVAL) && (keyLineProcess(line, key, newvalue)))
    {
      if (name != key)
        continue;
       else
	  {
	      pos = line.find_first_of(KEYVALDELIM);
             if ((pos == string::npos) || ((pos + 1) > line.length()))
                   return false;
	  	m_fstreamConfig.seekp(-(line.length()-pos),ios::cur);
                newvalue = line.substr(++pos);
		if(value.length()<newvalue.length())
		{
                     for(int i=0;i<(newvalue.length()-value.length());i++)
					value+=" "; 	
		}
              m_fstreamConfig.write(value.c_str(),value.length());
	  }
      isOk = true;
      break;
    }

    bool isBegin;
    if ((linetype == LINE_SECTION)
        && (sectionLineProcess(line, section, isBegin)))
    { // check for section
      if (isBegin)
      {
        string pathNew = sectionPath + SECTDELIM + section;
        if (toSectionEnd(section))
          continue;
      }
    }
    break;
  }
  m_fstreamConfig.close();
  return isOk;
}

//##ModelId=3BC27C350213
bool ConfigureFile::setSectionPath(const string& sectionPath)
{
  if (SysParam::setSectionPath(sectionPath))
    return true;

  m_fstreamConfig.clear();
  m_fstreamConfig.open(m_filename.c_str(), ios::in);
  if (m_fstreamConfig.rdstate() != ios::goodbit)
    return false;

  if (!locate(sectionPath))
  {
    m_fstreamConfig.close();
    return false;
  }

  char linebuf[2049];
  string line;
  string section, key, value;
  LINE_TYPE linetype;
  bool isOk = false;
  while (m_fstreamConfig.getline(linebuf, 2048))
  {
    line = linebuf;

    linetype = lineType(line);
    if (linetype == LINE_COMMENT)
      continue;

    // process key value line
    if ((linetype == LINE_KEYVAL) && (keyLineProcess(line, key, value)))
    {
      PARAM param;
      param.name = key;
      param.value = value;
      m_paramMapCurrent.insert(PARAMMAP::value_type(sectionPath, param));
      continue;
    }

    // process section line
    bool isBegin;
    if ((linetype == LINE_SECTION)
        && (sectionLineProcess(line, section, isBegin)))
    { // check for section
      if (isBegin)
      { // new sub section process
        if (!toSectionEnd(section))
          break;
        m_subsectionMapCurrent.insert(SECTIONMAP::value_type(sectionPath,
                                                             section));
      }
      else
      { // section end, return
        string pathParent, sectionCurrent;
        extractSectionPath(sectionPath, pathParent, sectionCurrent);
        if (sectionCurrent == section)
          isOk = true;
        break;
      }
      continue;
    }
    break;
  }

  m_fstreamConfig.close();
  if (isOk)
  {
    m_currentSectIter = m_subsectionMapCurrent.begin();
    m_currentParamIter = m_paramMapCurrent.begin();
  }
  return isOk;
}


//##ModelId=3BB2E8FD0109
int ConfigureFile::getSectionValue(string& name, string& value)
{
  return SysParam::getSectionValue(name, value);
}

//##ModelId=3BB3F33F037D
int ConfigureFile::getSubSection(string& subsection)
{
  return SysParam::getSubSection(subsection);
}


//##ModelId=3BB2E8FD011D
bool ConfigureFile::openSection(const string& sectionPath)
{
  if (SysParam::openSection(sectionPath))
    return true;

  m_fstreamConfig.clear();
  m_fstreamConfig.open(m_filename.c_str(), ios::in);
  if (m_fstreamConfig.rdstate() != ios::goodbit)
    return false;

  if (!locate(sectionPath))
  {
    m_fstreamConfig.close();
    return false;
  }

  bool isOk = scanSection(sectionPath);
  m_fstreamConfig.close();
  return isOk;
}

//##ModelId=3BB2E8FD0131
bool ConfigureFile::closeSection(const string& sectionPath)
{
  return SysParam::closeSection(sectionPath);
}

//##ModelId=3BB2E8FD014F
bool ConfigureFile::closeAllSection()
{
  return SysParam::closeAllSection();
}


//##ModelId=3BB2F3570222
bool ConfigureFile::addValue(const string& sectionPath, const string& name,
                             const string& value)
{
  return true;
}

//##ModelId=3BB2F3360242
bool ConfigureFile::addSubSection(const string& sectionPath,
                                  const string& subsection)
{
  return true;
}


//##ModelId=3BB2E8FD0163
bool ConfigureFile::scanSection(const string& sectionPath)
{
  char linebuf[2049];
  string line;
  string section, key, value;
  LINE_TYPE linetype;
  while (m_fstreamConfig.getline(linebuf, 2048))
  {
    line = linebuf;

    linetype = lineType(line);
    if (linetype == LINE_COMMENT)
      continue;

    // process key value line
    if ((linetype == LINE_KEYVAL) && (keyLineProcess(line, key, value)))
    {
      SysParam::addValue(sectionPath, key, value);
      continue;
    }

    // process section line
    bool isBegin;
    if ((linetype == LINE_SECTION)
        && (sectionLineProcess(line, section, isBegin)))
    { // check for section
      if (isBegin)
      { // new sub section process
        string pathNew = sectionPath + SECTDELIM + section;
        if (!scanSection(pathNew))
          return false;
      }
      else
      { // section end, return
        string pathParent, sectionCurrent;
        extractSectionPath(sectionPath, pathParent, sectionCurrent);
        if (sectionCurrent == section)
          return SysParam::addSubSection(pathParent, sectionCurrent);
        return false;
      }
    }
    continue;
  }
  return false;
}

//##ModelId=3BB2E8FD016D
bool ConfigureFile::locate(const string& sectionPath)
{
  string line, section, sectionCurrent;
  string pathCurrent;
  char linebuf[2049];   //不修改LINE_BUFFER_SIZE,不用修改头文件
  LINE_TYPE linetype;
  while (m_fstreamConfig.getline(linebuf, 2048))
  {
    line = linebuf;

    linetype = lineType(line);
    if (linetype != LINE_SECTION)
      continue;

    // check for section
    bool isBegin;
    if (!sectionLineProcess(line, section, isBegin))
      return false;

    if (isBegin)
    {
      string pathNew = pathCurrent + SECTDELIM + section;
      if (sectionPath.find(pathNew) != 0)
      {
        if (!toSectionEnd(section))
          return false;
        isBegin = false;
      }
      else
      {
        if (pathNew == sectionPath)
          return true;
        pathCurrent = pathNew;
        sectionCurrent = section;
      }
      continue;
    }
    else
    {
      if (sectionCurrent != section)
        return false;

      string pathParent;
      extractSectionPath(pathCurrent, pathParent, sectionCurrent);
      pathCurrent = pathParent;
      extractSectionPath(pathCurrent, pathParent, sectionCurrent);
      continue;
    }
  }
  return false;
}


//##ModelId=3BB2E8FD0195
bool ConfigureFile::sectionLineProcess(const string& lineVal, string& section,
                                       bool &isBegin)
{
  string line = lineVal;
  cleanLineComments(line);

  assert(!line.empty());
  assert(line.size() >= 3);

  if((*line.begin() != LSECTCHAR) || (*line.rbegin() != RSECTCHAR)
     || (line.size() < 3))
    return false;

  if (line.at(1) == ESECTCHAR)
  { // section end
    section = line.substr(2, line.length() - 3);
    isBegin = false;
  }
  else
  { // new section begin
    section = line.substr(1, line.length() - 2);
    isBegin = true;
  }
  return true;
}
//CHG by lirq 2003/03/28

#include "BossDes.h"
//##ModelId=3BB2E8FD01C7
bool ConfigureFile::keyLineProcess(const string& lineVal, string& key,
                                   string& value)
{
  string::size_type pos;
  string line = lineVal;

  cleanLineComments(line);
  assert(!line.empty());

  pos = line.find_first_of(KEYVALDELIM);
  if ((pos == string::npos) || ((pos + 1) > line.length()))
    return false;

  key = line.substr(0, pos);
  value = line.substr(++pos);
  trim(key);
  trim(value);
  
  // 如果是加密的value这解密它
  CCharPtr szValue0 = value.c_str();
  int nLeft = szValue0.Find("\\\\pak{",0);
  if( nLeft != -1 )
  {
	int nRight = szValue0.Find("}\\", nLeft);
	if ( nRight != -1)
	{
		CBossDes unk;
		unk.m_szData = szValue0.Mid(nLeft+6,nRight-(nLeft+6)-1);
		unk.Decrypt();
		unk.Hstr2Str(unk.m_szData, unk.m_szData);
		value = LPCSTR(unk.m_szData);
	}
  }
  return true;
}


//##ModelId=3BB2E8FD0217
void ConfigureFile::cleanLineComments(string& line)
{
  trim(line);
  if (line.empty())
    return;

  string::size_type pos, maxpos;
  maxpos = line.length();
  pos = line.find_first_of(WHITE_SPACE);
  while ((pos != string::npos) && ((pos + 1) < maxpos))
  {
    if (strchr(CONF_COMMENTS, line.at(pos + 1)) != 0)
    {
      line = line.substr(0, pos);
      break;
    }
    pos = line.find_first_of(WHITE_SPACE, ++pos);
  }
}


//##ModelId=3BB2E8FD0221
ConfigureFile::LINE_TYPE ConfigureFile::lineType(const string& lineVal)
{
  if (lineVal.empty())
    return LINE_COMMENT;

  string line = lineVal;
  trim(line);
  if (line.empty())
    return LINE_COMMENT;

  // check for comment
  if (strchr(CONF_COMMENTS, *line.begin()) != 0)
    return LINE_COMMENT;

  cleanLineComments(line);

  if ((*line.begin() != LSECTCHAR) && (line.find_first_of(KEYVALDELIM)
                                       != string::npos))
    return LINE_KEYVAL;

  if ((*line.begin() == LSECTCHAR) && (*line.rbegin() == RSECTCHAR)
      && (line.size() >= 3))
    return LINE_SECTION;

  return LINE_UNKNOWN;
}

//##ModelId=3BB359C500CF
bool ConfigureFile::toSectionEnd(const string& sectionname)
{
  char linebuf[2049];
  string line, section, sectionCurrent;
  string sectionPath;
  LINE_TYPE linetype;

  sectionPath += SECTDELIM + sectionname;
  sectionCurrent = sectionname;

  bool isBegin;
  while (m_fstreamConfig.getline(linebuf, 2048))
  {
    line = linebuf;
    linetype = lineType(line);
    if (linetype != LINE_SECTION)
      continue;

    // check for section
    if (!sectionLineProcess(line, section, isBegin))
      return false;

    if (isBegin)
    {
      sectionPath += SECTDELIM + section;
      sectionCurrent = section;
      continue;
    }
    else
    {
      if (sectionCurrent != section)
        return false;

      string pathParent;
      extractSectionPath(sectionPath, pathParent, sectionCurrent);
      if (pathParent.empty())
        return true;
      sectionPath = pathParent;
      extractSectionPath(sectionPath, pathParent, sectionCurrent);
      continue;
    }
  }
  return false;
}

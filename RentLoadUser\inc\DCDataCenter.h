#ifndef _DCDATACENTER_H
#define _DCDATACENTER_H

#include "DCEventTime.h"
//#include "common/DCAcctDataStruct.h"
#include <unistd.h>

#include <string>
#include <map>

//using std::map;
//using std::pair;

#define bbzero(m) memset((m), 0, sizeof(m))

#define CYCLE_MON  0	//月租标识
#define CYCLE_DAT  1	//日租标识
#define CYCLE_TRI  2	//试算标识
#define CYCLE_SEM  3    //学期扣费标识
#define CYCLE_NEW  9	//新装标识
#define CYCLE_ALL  100	//全业务标识

#define CYC_OFR_REL 1	//cycle_ofr_relation表标识(销售品互斥)
#define CYC_OFR_PRO 2	//cycle_ofr_profile表标识
#define CYC_EVT_PRO 3	//cycle_event_profile表标识

#define BIL_RATE_COND 4		//tb_bil_rate_condition表标识


#define ABM_FAIL_TIMES 10	//发送abm扣费请求尝试最大次数
#define NO_RECORD_FLAG -1009
#define NO_NEED_DEAL   1010

#define COND_TYPE_TREE	   0
#define COND_TYPE_NOT_TREE 1


#define NO_DISCT_VALUE          100   //打折优惠为100

#define MSG_NOTENOUGH_USER      0 //余额不足用户提醒
#define MSG_ALL_USER            1 //全量用户余额提醒 


typedef std::map<long,DCEventTime> T_MapDate;


class DCDataCenter
{
public:
    ~DCDataCenter(void);

    static DCDataCenter* instance()
    {
        if(m_pDataCenter == NULL)
        {
            m_pDataCenter = new DCDataCenter();	
        }
        return m_pDataCenter;
    }


public:
    int Init();
    int Release();

    int RefreshBillingCycle(int nBillingCycleID,int nLatnId);
    
    long getItemTypeId()
    {
        return m_lnAcctItemTypeId;
    }
   
private:
	int PreMonth(int vi_iMonth);
	int NextMonth(int vi_iMonth);
	int MonthEndDay(int vi_iMonth);
	int EndDay(int nBillingCycleID);
	int MonthInfo(int vi_iMonth,char *vo_sBeginDate,char *vo_sEndDate,char *vo_sBeginTime,char *vo_sEndTime);
	
public:
    DCDateTime m_currDealTime;                      //事件执行时间
    
    int   m_nCurrEventTypeId;    //当前事件Id
    long m_lnAcctItemTypeId;//停机保号账目类型

	//ocs::IAcctHead     m_stAcctHead;
	int m_nLatnId;	
    
    int  iBillingCycleId;                       //当前账期标识,YYYYMM
    int  iEndDay;                               //当前截止日
    int  iCycleEndDay;                          //当前账期结束日

    int  iPrevBillingCycleId;                   //上一账期标识
    int  iNextBillingCycleId;                   //下一账期标识
    char sAreaCode[16];                         //本地网区号    
    char cBillingCycleLock;                     //本地网封帐标识
    
    char szCurrDealTime[15];                //当前账期,格式为YYYYMMDDHH24MiSS
    char sCycleBeginDate[9];                    //当前账期开始日期，格式为YYYYMMDD
    char sCycleEndDate[9];                      //当前账期结束日期，格式为YYYYMMDD
    char sCycleBeginTime[15];                   //当前账期开始时间，格式为YYYYMMDDHH24MiSS
    char sCycleEndTime[15];                     //当前账期结束时间，格式为YYYYMMDDHH24MiSS

    char sPrevCycleBeginDate[9];                //上一账期开始日期，格式为YYYYMMDD
    char sPrevCycleEndDate[9];                  //上一账期结束日期，格式为YYYYMMDD
    char sPrevCycleBeginTime[15];               //上一账期开始时间，格式为yyyymmddhh24Miss
    char sPrevCycleEndTime[15];                 //上一账期结束时间，格式为yyyymmddhh24miss

    char sNextCycleBeginDate[9];                //下一账期开始日期，格式为YYYYMMDD
    char sNextCycleEndDate[9];                  //下一账期结束日期，格式为YYYYMMDD
    char sNextCycleBeginTime[15];               //下一账期开始时间，格式为yyyymmddhh24miss
    char sNextCycleEndTime[15];                 //下一账期结束时间，格式为yyyymmddhh24miss

	char cProcessType;						   //‘1’ 为实时任务，’2 ‘定时任务，’3’ 为手工任务（全量/部分) ‘4’ 为预出账任务）
    
	int  iPid;                                 //进程实际ID

	std::string strTopology;	//程序初始化时现取的Topology

	bool m_isNeedDealCrossUser;
private:
    DCDataCenter(void);

    int InitConfig();	//初始化配置参数   
	
private:
    static DCDataCenter *m_pDataCenter;	//类的自身指针


    bool m_bFlag;		//Map的标志
    char m_szLogPath[256];	//日志路径


};

#endif

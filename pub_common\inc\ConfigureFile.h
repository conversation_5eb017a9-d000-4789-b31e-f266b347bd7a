// Language:    C++
//
// OS Platform: UNIX
//
// Authors: <AUTHORS>
//
// History:
// Copyright (C) 2001 by <PERSON><PERSON><PERSON>, Linkage. All Rights Reserved.
//
// Comments:
//

#ifndef CONFIGUREFILE_H_INCLUDED_C44D18DD
#define CONFIGUREFILE_H_INCLUDED_C44D18DD

#include "config-all.h"

#include "SysParam.h"

#include <map>
#include <list>
#include <string>


#if defined(STDIOSTREAM_DISABLE)
#include <fstream.h>
#else
#include <fstream>
#endif


USING_NAMESPACE(std)


const string DCONF_ENV("BOSS_CONFIG");

// define all delimiter chars

// prefix char of section
const char LSECTCHAR = '<';

// suffix char of section
const char RSECTCHAR = '>';

// mark section end
const char ESECTCHAR = '/';


//##ModelId=3BB2E8C10261
//##Documentation
//## 系统参数访问
//##
//## 系统参数组织由三部分构成：节（section）、参数名（name）和参数值（value）。
//##
//## 节包含多对参数名和参数值，同时节可以包含多个子节，形成树状结构。
//##
//## 类提供根据节、参数名查询参数值的方法；提供指定参数名，遍历其下的参数、
//##  值和子节名的方法。
//##
//##
//##
//## 参数文件组织例图：
//##
//## <section_name>
//## name1 = value1
//## name2 = value2
//## name3 = value3
//## <subsection_name>
//## name11 = value11
//## name12 = value12
//## name13 = value13
//## </subsection_name>
//## name4 = value4
//## </section_name>

class ConfigureFile : public SysParam
{
 public:
  //##ModelId=3BCAC1910285
  ConfigureFile() {;}

  //##ModelId=3BCAC1910307
  virtual ~ConfigureFile() {;}

  //##ModelId=3BCBD14F0341
  virtual bool initialization(const char *filename = 0);

  //##ModelId=3BB2E8FD0091
  virtual bool end();

  //##ModelId=3BB2E8FD00A5
  //##Documentation
  //## 根据节名和参数名查询参数值
  virtual bool getValue(const string& sectionPath, const string& name,
                        string& value);

  //##Documentation
  //## 修改当前节的参数的值
  //##

  bool ModifyValue(const string& sectionPath, const string& name
                      ,string& value);

  //##ModelId=3BB2E8FD0109
  //##Documentation
  //## 获取当前节的参数或子节名
  //##
  
  virtual int getSectionValue(string& name, string& value);

  //##ModelId=3BB2E8FD011D
  //##Documentation
  //## 打开指定节
  //##
  //## 打开的节的内容会缓存到内存，在参数较多时能加速查询速度。
  virtual bool openSection(const string& sectionPath);

  //##ModelId=3BB2E8FD0131
  //##Documentation
  //## 关闭指定节
  virtual bool closeSection(const string& sectionPath);

  //##ModelId=3BB2E8FD014F
  //##Documentation
  //## 关闭所有节
  virtual bool closeAllSection();

  //##ModelId=3BB3F33F037D
  virtual int getSubSection(string& subsection);

  //##ModelId=3BC27C350213
  //##Documentation
  //## 设置当前节
  virtual bool setSectionPath(const string& sectionPath);

 protected:
  //##ModelId=3BB2F3570222
  virtual bool addValue(const string& sectionPath, const string& name,
                        const string& value);

  //##ModelId=3BB2F3360242
  virtual bool addSubSection(const string& sectionPath,
                             const string& subsection);

 private:

  //##ModelId=3BB2ECFB01EF
  enum  LINE_TYPE { LINE_SECTION, LINE_COMMENT, LINE_KEYVAL, LINE_UNKNOWN };

  //##ModelId=3BB2F05C01C1
  enum  { LINE_BUFFER_SIZE = 256 };

  //##ModelId=3BB2E8FD0163
  //##Documentation
  //## 扫描指定节的内容，包括其下所包含的所有子节
  bool scanSection(const string& sectionPath);

  //##ModelId=3BB2E8FD016D
  bool locate(const string& sectionPath);

  //##ModelId=3BB2E8FD0195
  bool sectionLineProcess(const string& line, string& section, bool &isBegin);

  //##ModelId=3BB2E8FD01C7
  bool keyLineProcess(const string& line, string& key, string& value);

  //##ModelId=3BB2E8FD0217
  //##Documentation
  //## 清除行中的注释信息
  void cleanLineComments(string& line);

  //##ModelId=3BB2E8FD0221
  //##Documentation
  //## 判断行的类型
  LINE_TYPE lineType(const string& line);

  //##ModelId=3BB359C500CF
  bool toSectionEnd(const string& section);

  //##ModelId=3BB2E8EA0382
  string m_filename;

  //##ModelId=3BB2E8EA03A1
  string m_envName;

  //##ModelId=3BB2E8EA03B5
  std::fstream m_fstreamConfig;

};


#endif /* CONFIGUREFILE_H_INCLUDED_C44D18DD */

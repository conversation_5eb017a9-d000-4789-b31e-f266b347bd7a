/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					sm项目组
*All rights reserved.
*
*Filename：
*		DCRentLoadUser.h
*Indentifier：
*
*Description：
*		月租用户数据加载类
*Version：
*		V1.0
*Author:
*		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_CALLZKSERVERMAKER_H__
#define __DC_CALLZKSERVERMAKER_H__
#include "DCBasePlugin.h"
#include "DCLogMacro.h"
#include "DCOBJSet.h"
#include "DCSeriaOp.h"
#include "DataDef.h"
#include "publiclib.h"
#include "DCFClientLogNew.h"
#include "DCFLocalClientNew.h"
#include "DCDBManer.h" 

#include "DCPairList.h"
#include "DCPairManager.h"
#include "CallbackAsyncHandlerNew.h"
#include "DCUidCacheManager.h"

#include "DCKpiSender.h"
#include "DCTCompress.h"
#include "DCFClientDataDefNew.h"
#include "DCGrayscaleRoute.h"

class SendCallbackAsyncHandler:public dcf_new::CallbackAsyncHandler
{
public:
	SendCallbackAsyncHandler(DCPairList *pairList) {
		m_pairList = pairList;
		
	}

	~SendCallbackAsyncHandler(){

	}

public:
	void callback(const std::string& uuid, const std::string& serviceName, const char *pParams, int len)
	{
		SMsgPair sMsg;
		string ipErrorcode = "";
		if(strlen(pParams) > 0)
		{
			ipErrorcode =  pParams;
		}
		if(m_pairList->size()>0)
		{
			m_pairList->out(uuid,sMsg,ipErrorcode);//暂时不需要返回值
		}
		DCBIZLOG(DCLOG_LEVEL_INFO, 0, "SendCallbackAsyncHandler","CALLBACK: uuid=[%s] serviceName=[%s] msg=[%s]",uuid.c_str(),serviceName.c_str(),pParams);
		DCDATLOG("RE00007:%s!%s!%s",uuid.c_str(),serviceName.c_str(),pParams);
	}
private:
	DCPairList *m_pairList;
};

class DCCallZKServerMaker
{
	public:	
		DCCallZKServerMaker()        
			:en(ESeriaBinString),enBody(ESeriaBinString)
		{
			m_pClient = NULL;
			m_pSendCallback = NULL;
            m_pairList = NULL;
            m_pairManager = NULL;
            m_uidCacheManager = NULL;
			m_nSendType = 1;
			m_ptrBPMon = NULL;
            m_bDcfServMode = false;
			m_pGrayscaleRoute = NULL;
			m_pDbm = NULL;
			m_strSubscriber = "";
			m_sRouteProcess = "";
			
			//初始化互斥锁
			pthread_mutex_init(&thread_mutex, NULL);			
		}
		
		virtual ~DCCallZKServerMaker();
		
		int init(bool bDcfServMode,DCPairList *pairList,DCUidCacheManager *uidCacheManager,STConfigPara cfgPara);
        int init(STConfigPara cfgPara);
        const char* GenUnifiledSeq();
		int process(RentUserInfo* pInstUserInfo, int     nMsgCtrl, DCDBManer *p_dbm);
		int GetGrayServName(const int &nLatnId,const long &lnRouteAcctId,string &serviceBILL);
		int InitGrayRouteDCFClients(const char* routeProcess);
				
	private:
		Compressor *m_Compress;//每个线程new一个对象
		
		DCSeriaEncoder en;
		DCSeriaEncoder enBody;
		DCSeriaPrinter pt;

		std::string m_strZKAddr;
		std::string m_strZKServName;
		std::string m_strZKRootDir;
		std::string m_strZKLoginPwd;
		std::string m_strZKAesPwd;
		dcf_new::DCFLocalClient *m_pClient;

		char m_szLogPath[1024];
		SendCallbackAsyncHandler *m_pSendCallback;
		DCPairList *m_pairList;
		int m_nSendType;	
		int m_nQueueSize;
		int m_nQueueSizeOldUser;//定时流程控制队列大小
		int m_nQueueSizeNewUser;//新装流程控制队列大小
		int m_nQueueSizeFroUser;//复机流程控制队列大小
		int m_nFlowTimeOut;

        DCPairManager *m_pairManager;
		DCUidCacheManager *m_uidCacheManager;
		std::string m_strHashKey;

		int m_nFlowSleepUs;

		vector<string> m_vecGroupUid;

		DCKpiMon* m_ptrBPMon;
		
		pthread_mutex_t  thread_mutex;

		int m_nBigAcctMsgSize;
		int m_nDcaCacheMsgSize;
		int m_nOpenKpi;
		std::string m_sRouteProcess;
        bool m_bDcfServMode;

		// 灰度路由相关成员变量
		DCGrayscaleRoute* m_pGrayscaleRoute;
		DCDBManer* m_pDbm;
		std::string m_strSubscriber;
		std::string m_sRouteProcess;
};
#endif


【命令格式如下】
latn_id|cycle_id|task_type|SQL字段
latn_id：本地网ID
cycle_id：6位账期
task_type：任务类型


【后付费跨账户更新流程 [@]需替换为latn_id】
1) 查询SQL
select acct_id,ofr_inst_id 
from tb_bil_cross_account_mid_[@] 
order by ofr_inst_id,acct_id;

2) 预占SQL
update tb_bil_cross_account_mid_[@] set deal_state=21 where ofr_inst_id=? and acct_id=?


【后付费新装 [@]需替换为latn_id】
1) 查询SQL
select Interface_ofr_change_id, 
	ofr_inst_id,
	date_format(Create_date,'%Y%m%d%H%i%s')
from interface_ofr_change_[@]
where Acct_deal_state in (0,2)
and date_format(create_date,'%Y%m%d')=date_format(sysdate(),'%Y%m%d')

2) 预占SQL
update interface_ofr_change_[@] set Acct_deal_state=21 where Interface_ofr_change_id=?


【预付费新装 [@]需替换为latn_id】
1) 查询SQL
select Interface_ofr_change_id,
	ofr_inst_id,
	date_format(Create_date,'%Y%m%d%H%i%s')
from interface_ofr_change_[@] 
where Acct_deal_state in (0,2)

2) 预占SQL
update interface_ofr_change_[@] set Acct_deal_state=21 where Interface_ofr_change_id=?


【变更 [@]需替换为latn_id】
1) 查询SQL
select ACCT_ID,
	PRD_INST_ID,
	PRD_TRANSFER_ID 
from PROD_INST_TRANSFER_[@] 
where ACCT_DEAL_STATE=0

2) 预占SQL
update PROD_INST_TRANSFER_[@] set Acct_deal_state=21 where PRD_TRANSFER_ID=?


【复机】
1) 查询SQL
select Frozen_id, 
	Prd_inst_id,
	State_time, 
	obversion_mode 
from tb_prd_frozen_user
where Acct_deal_state in (0,2) 
and change_type = 0  
and Latn_id = ?  

2) 预占SQL
update tb_prd_frozen_user set acct_deal_state=21 where FROZEN_ID=?

/*******************************************
 *Copyrights   2007，深圳天源迪科计算机有限公司
 *                   技术平台项目组
 *All rights reserved.
 *
 *Filename：
 *       DCPairManager.cpp
 *Indentifier：
 *
 *Description：
 *       序列值对超时处理线程
 *Version：
 *       V1.0
 *Author:
 *       YF.Du
 *Finished：
 *       2008年10月10日
 *History:
 *       2008/10/10  V1.0 文件创建
 ********************************************/
#include "DCPairManager.h"
#include "DataDef.h"

using namespace std;

DCPairManager::DCPairManager()
{
	m_pairList = NULL;
	m_timeOut = 0;
}

DCPairManager::~DCPairManager()
{
}

int DCPairManager::init(DCPairList *pairList, unsigned long timeOut)
{
	m_pairList = pairList;
	
	//超时时间由毫秒转换成微秒
	m_timeOut = timeOut*1000;
}

void DCPairManager::routine()
{
	//DCLogMsgCallback* pLogMsgCallback = DCLogMsgCallbackCmn_SIG::instance();
	//ACE_LOG_MSG->set_flags (ACE_Log_Msg::MSG_CALLBACK);
	//ACE_LOG_MSG->msg_callback (pLogMsgCallback);
	
	int nspsize = 0;
	
	vector<SMsgPair> vecTimeOut;
	
	while (1)
	{
		sleep(1);
		nspsize = m_pairList->size();
		
		if (nspsize)
		{
			m_pairList->clearTimeOut(m_timeOut, vecTimeOut);
		}
		
		for (unsigned int j = 0; j<vecTimeOut.size(); j++)
		{
			DealTimeOut(vecTimeOut[j]);
		}
		vecTimeOut.clear();
		
	}
}

void DCPairManager::DealTimeOut(const SMsgPair msg)
{
	//打印超时日志
	struct timeval current;
	gettimeofday(&current, NULL);
	
	long dm = (current.tv_sec-msg.begin.tv_sec)*1000000+current.tv_usec-msg.begin.tv_usec;
	if(msg.nAcctType==SPECIAL_ACCT_INST)
	{
		DCBIZLOG(DCLOG_LEVEL_INFO,0,"DCPairManager::DealTimeOut","TimeOut:CrossAcct ModGroupId[%ld] keyuid[%s] groupnum[%d], sendsec[%ld.%06ld], nowsec[%ld.%06ld], diffsec[%ld.%06ld].",
			msg.lnSpecialId,msg.uuid.c_str(),msg.nGroupNum,msg.begin.tv_sec,msg.begin.tv_usec,current.tv_sec,current.tv_usec,dm/1000000,dm%1000000);
	}
	else
	{
		DCBIZLOG(DCLOG_LEVEL_INFO,0,"DCPairManager::DealTimeOut","TimeOut:CommonAcct AcctId[%ld] keyuid[%s] groupnum[%d], sendsec[%ld.%06ld], nowsec[%ld.%06ld], diffsec[%ld.%06ld].",
			msg.lnSpecialId,msg.uuid.c_str(),msg.nGroupNum,msg.begin.tv_sec,msg.begin.tv_usec,current.tv_sec,current.tv_usec,dm/1000000,dm%1000000);
	}
	
	DCDATLOG("RF00001:%s!%ld!%06ld!%ld!%06ld!%ld!%06ld",msg.uuid.c_str(),msg.begin.tv_sec,msg.begin.tv_usec,current.tv_sec,current.tv_usec,dm/1000000,dm%1000000);

}


/***********************************************************************
* Module:  DCAtCondition.cpp
* Author:  gaos
* Modified: 2010年5月26日 14:22:32
* Purpose: Implementation of the class DCAtCondition
* Comment: 融合帐务条件
***********************************************************************/
//#include "common/DCAcctFunction.h"
#include "DCAtCondition.h"

#include <ace/Log_Msg.h>
#include <ace/Log_Msg_Backend.h>
#include <ace/Log_Record.h>
#include "DCLogMacro.h"

using namespace std;

////////////////////////////////////////////////////////////////////////
// Name:       DCAtCondition::Init(int nProgramId)
// Purpose:    Implementation of DCAtCondition::Init()
// Return:     int
////////////////////////////////////////////////////////////////////////

int DCAtCondition::Init(int nInsetId,DCDBManer *dbm)
{
	m_nInsetId = nInsetId;
	m_dbm = dbm;
	return 0;
}


void DCAtCondition::Clear()
{
	m_itr = m_mapCondition.begin();
	while(m_itr != m_mapCondition.end())
	{
		delete [](m_itr->second);

		++m_itr;
	}
	m_mapCondition.clear();
}


bool DCAtCondition::LoadCondtion()
{
	UDBSQL*pCondQuery = m_dbm->GetSQL("param_unite_condition_all");
	if (NULL == pCondQuery)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCAtCondition::LoadCondtion","not find sql[param_unite_condition_all]");
		return false;
	}

	m_user_load = true;

	int nConditionid;
	int last_conditionid =-1;
	int item_position = 0;
	
	STATCondItem *p_stItem ;

	pCondQuery->UnBindParam();
	pCondQuery->BindParam(1,CONDITION_SERVICE_TYPE_REALACCT);
	pCondQuery->Execute();
	while (pCondQuery->Next())
	{
		pCondQuery->GetValue(10,nConditionid);
		
		if(nConditionid != last_conditionid)
		{
			m_itr = m_mapCondition.find(nConditionid);
			if(m_itr ==  m_mapCondition.end())
			{//没有准备的，需要准备
				p_stItem = new STATCondItem[ONE_CONDITION_ITEM_NUMS];
				m_mapCondition.insert(pair<int,STATCondItem* >(nConditionid,p_stItem));
			}
			else
			{
				p_stItem = m_itr->second;	
			}

			memset(p_stItem,0,ONE_CONDITION_ITEM_NUMS*sizeof(STATCondItem));
			item_position = 0 ;            //记录下标位置
			last_conditionid = nConditionid;

			pCondQuery->GetValue(1,p_stItem[item_position].iGroupid);
		}
		else
		{
			pCondQuery->GetValue(1,p_stItem[item_position].iGroupid);
			//condition_id相同，监测group_id是否相同
			if(p_stItem[item_position -1].iGroupid != p_stItem[item_position].iGroupid)
			{
				//填充前一个group组的inext_groupid值
				for(int i =item_position-1;i>-1; --i )
				{
					if(p_stItem[i].inext_group_id != -1)
					{
						break;
					}
					p_stItem[i].inext_group_id = item_position;
				}
			}

		}

		p_stItem[item_position].inext_group_id = -1;
		pCondQuery->GetValue(2,p_stItem[item_position].iLeftType);//ET_COMPARE_CODE_TYPE
		pCondQuery->GetValue(3,p_stItem[item_position].szLeft);//ET_COMPARE_CODE
		pCondQuery->GetValue(4,p_stItem[item_position].iInset_id);//ET_INSET_ID
		pCondQuery->GetValue(5,p_stItem[item_position].iIn_code_type);//ET_IN_CODE_TYPE
		pCondQuery->GetValue(6,p_stItem[item_position].iIn_code_offset);//ET_IN_CODE_OFFSET
		pCondQuery->GetValue(7,p_stItem[item_position].iIn_code_length);//ET_IN_CODE_LENGTH
		pCondQuery->GetValue(8,p_stItem[item_position].szOperator);//ET_COMPARE_OPERATOR
		pCondQuery->GetValue(9,p_stItem[item_position].szRight);//ET_COMPARE_VALUE

		//对右值进行变量类替换，如[BILLING_CYCLE_ID]之类的变量
		if (p_stItem[item_position].szRight[0] == '[')
		{
			ReplaceCode(p_stItem[item_position].szRight);
		}

		++item_position;
	}

	pCondQuery->Close();
	return true;
}

////////////////////////////////////////////////////////////////////////
// Name:       DCAtCondition::LoadCondItem()
// Purpose:    Implementation of DCAtCondition::LoadCondItem()
// Return:     int
////////////////////////////////////////////////////////////////////////

bool DCAtCondition::LoadCondItem(long lnCondID, int nJudgeFlag, multimap<int,STATCondItem> &mmapTmp)
{
	mmapTmp.clear();

	UDBSQL*pCondQuery = m_dbm->GetSQL("param_unite_condition");
	if (NULL == pCondQuery)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCAtCondition::LoadCondItem","not find sql[param_unite_condition]");
		return false;
	}
	pCondQuery->UnBindParam();
	pCondQuery->BindParam(1,lnCondID);
	pCondQuery->BindParam(2,CONDITION_SERVICE_TYPE_REALACCT);
	pCondQuery->Execute();
	while (pCondQuery->Next())
	{
		int nGroupID;
		STATCondItem stItem;
		nGroupID = 0;
		memset(&stItem,0x00,sizeof(STATCondItem));

		pCondQuery->GetValue(1,nGroupID);
		pCondQuery->GetValue(2,stItem.iLeftType);
		pCondQuery->GetValue(3,stItem.szLeft);
		pCondQuery->GetValue(4,stItem.iInset_id);
		pCondQuery->GetValue(5,stItem.iIn_code_type);
		pCondQuery->GetValue(6,stItem.iIn_code_offset);
		pCondQuery->GetValue(7,stItem.iIn_code_length);
		pCondQuery->GetValue(8,stItem.szOperator);
		pCondQuery->GetValue(9,stItem.szRight);

        //对右值进行变量类替换，如[BILLING_CYCLE_ID]之类的变量
        if (stItem.szRight[0] == '[')
        {
            ReplaceCode(stItem.szRight);
        }

		mmapTmp.insert(multimap<int,STATCondItem>::value_type(nGroupID,stItem));
	}

	if (0 >= mmapTmp.size() && lnCondID != -1)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCAtCondition::LoadCondItem","Condition not defined, condition id [%d]",lnCondID);
	}

	pCondQuery->Close();
	return true;
}

bool  DCAtCondition::LoadCondItem(long lnCondID, STATCondItem* vo_condition)
{
	m_itr = m_mapCondition.find(lnCondID);

	if(m_itr ==  m_mapCondition.end())
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"DCAtCondition::LoadCondItem","Condition not defined, condition id [%ld]",lnCondID);

		return false;
	}
	
	memcpy(vo_condition,m_itr->second,ONE_CONDITONN_ITEM_SIZE);

	return true;
}
// End add by liuligang 2012-2-28

////////////////////////////////////////////////////////////////////////
// Name:       DCAtCondition::JudgeCond()
// Purpose:    Implementation of DCAtCondition::JudgeCond()
// Parameters:
// - vi_lCondID
// - vi_pConfigSQL
// Return:     bool
////////////////////////////////////////////////////////////////////////

bool DCAtCondition::JudgeCond(long lnCondID)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCAtCondition::JudgeCond","JudgeCond, Condition id :%ld.",lnCondID);
	char szTmp[100]={0};
	if(!m_user_load)
	{
		multimap<int,STATCondItem> mmapTmp;
		if (!LoadCondItem(lnCondID,TYPE_LEFT_CODE_QUERY,mmapTmp))
		{
			return false;
		}

		multimap<int,STATCondItem>::iterator itr = mmapTmp.begin();
		for (; mmapTmp.end() != itr; itr++)
		{
			STATCondItem *pstItem = &(itr->second);
			memset(szTmp,0x00,sizeof(szTmp));
			GetElementValue(pstItem->szLeft, szTmp);
			if(szTmp[0]!=0)
				strcpy(pstItem->szLeft , szTmp);
		}

		return m_pCompare->Compare(mmapTmp);
	}
	else
	{
		if (!LoadCondItem(lnCondID,m_condition))
		{
			//没有这个条件返回无限制，及条件满足
			return false;
		}
		STATCondItem *pstItem ;
		for(int i =0;m_condition[i].inext_group_id !=0 ;++i)
		{
			pstItem = &m_condition[i];

			char szTmp[100]={0};
			GetElementValue(pstItem->szLeft, szTmp);
			if(szTmp[0]!=0)
				strcpy(pstItem->szLeft , szTmp);
			
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"DCAtCondition::JudgeCond","group id :[%d],left value :[%s],operator :[%s] right value :[%s]",pstItem->iGroupid,pstItem->szLeft,pstItem->szOperator,pstItem->szRight);

		}

		return  m_pCompare->Compare(m_condition);
	}

	return true;
}

////////////////////////////////////////////////////////////////////////
// Name:       DCAtCondition::DCAtCondition()
// Purpose:    Implementation of DCAtCondition::DCAtCondition()
// Return:     
////////////////////////////////////////////////////////////////////////

DCAtCondition::DCAtCondition()
{
	m_user_load = false;
	m_pCompare = new DCAtCompare();
}

////////////////////////////////////////////////////////////////////////
// Name:       DCAtCondition::~DCAtCondition()
// Purpose:    Implementation of DCAtCondition::~DCAtCondition()
// Return:     
////////////////////////////////////////////////////////////////////////

DCAtCondition::~DCAtCondition()
{
	if(m_pCompare!=NULL)
	{
		delete m_pCompare;
		m_pCompare = NULL;
	}

	Clear();
}

//替换一些常量， 返回替换的次数
int  DCAtCondition::ReplaceCode(char* v_sString)
{
	int nRet=0;
	char sTemp[128] = {0};

	//本地网ID
	sprintf(sTemp, "%d", DCDataCenter::instance()->m_nLatnId);
	nRet = ReplaceStr(v_sString, "[LATN_ID]", sTemp);

	//本地网区号
	nRet = ReplaceStr(v_sString, "[AREA_CODE]", DCDataCenter::instance()->sAreaCode);

	// modify begin yangp 20120105
	// 以上[...]在切换帐期时不需要更新
	nRet = 0;
	// modify end
	
	//当前账期标识  
	sprintf(sTemp, "%d", DCDataCenter::instance()->iBillingCycleId);
	nRet += ReplaceStr(v_sString, "[BILLING_CYCLE_ID]", sTemp);


	//当前账期开始日期，格式为YYYYMMDD
	nRet += ReplaceStr(v_sString, "[CYCLE_BEGIN_DATE]", DCDataCenter::instance()->sCycleBeginDate);

	//当前账期结果日期，格式为YYYYMMDD      
	nRet += ReplaceStr(v_sString, "[CYCLE_END_DATE]", DCDataCenter::instance()->sCycleEndDate);

	//当前账期开始时间，格式为YYYYMMDDHH24MiSS
	nRet += ReplaceStr(v_sString, "[CYCLE_BEGIN_TIME]", DCDataCenter::instance()->sCycleBeginTime);

	//当前账期结果时间，格式为YYYYMMDDHH24MiSS
	nRet += ReplaceStr(v_sString, "[CYCLE_END_TIME]", DCDataCenter::instance()->sCycleEndTime);

	//上一账期标识  
	sprintf(sTemp, "%d", DCDataCenter::instance()->iPrevBillingCycleId);
	nRet += ReplaceStr(v_sString, "[PREV_BILLING_CYCLE_ID]", sTemp);

	//上一账期开始日期，格式为YYYYMMDD        
	nRet += ReplaceStr(v_sString, "[PREV_CYCLE_BEGIN_DATE]", DCDataCenter::instance()->sPrevCycleBeginDate);

	//上一账期结果日期，格式为YYYYMMDD        
	nRet += ReplaceStr(v_sString, "[PREV_CYCLE_END_DATE]", DCDataCenter::instance()->sPrevCycleEndDate);

	//上一账期开始时间，格式为YYYYMMDDHH24MiSS
	nRet += ReplaceStr(v_sString, "[PREV_CYCLE_BEGIN_TIME]", DCDataCenter::instance()->sPrevCycleBeginTime);

	//上一账期结果时间，格式为YYYYMMDDHH24MiSS
	nRet += ReplaceStr(v_sString, "[PREV_CYCLE_END_TIME]", DCDataCenter::instance()->sPrevCycleEndTime);

	//下一账期标识  
	sprintf(sTemp, "%d", DCDataCenter::instance()->iNextBillingCycleId);
	nRet += ReplaceStr(v_sString, "[NEXT_BILLING_CYCLE_ID]", sTemp);

	//下一账期开始日期，格式为YYYYMMDD        
	nRet += ReplaceStr(v_sString, "[NEXT_CYCLE_BEGIN_DATE]", DCDataCenter::instance()->sNextCycleBeginDate);

	//下一账期结果日期，格式为YYYYMMDD        
	nRet += ReplaceStr(v_sString, "[NEXT_CYCLE_END_DATE]", DCDataCenter::instance()->sNextCycleEndDate);

	//下一账期开始时间，格式为YYYYMMDDHH24MiSS
	nRet += ReplaceStr(v_sString, "[NEXT_CYCLE_BEGIN_TIME]", DCDataCenter::instance()->sNextCycleBeginTime);

	//下一账期结果时间，格式为YYYYMMDDHH24MiSS
	nRet += ReplaceStr(v_sString, "[NEXT_CYCLE_END_TIME]", DCDataCenter::instance()->sNextCycleEndTime);


	// 当前系统最后一天
	//char szSysTime[16];
	PublicLib::GetTime(sTemp,YYYYMM);
	int nMonth = atoi(sTemp);
	int iSysEndDay = PublicLib::getMaxDay((int)(nMonth/100),(int)(nMonth%100));
	char szSysEndDay[16] = {0};
	sprintf(szSysEndDay,"%s%d",sTemp,iSysEndDay);
	nRet += ReplaceStr(v_sString, "[SYSTEM_END_DAY]", szSysEndDay);
	
	// add begin yangp 20111208
	// 当前系统第一天
	strncpy(sTemp+6,"01",3);
	nRet += ReplaceStr(v_sString, "[SYSTEM_FIRST_DAY]", sTemp);
    
	// add end

	return nRet;
}


//替换字符串：将v_sString中vi_sOld全替换成vi_sNew    返回：替换次数
int DCAtCondition::ReplaceStr(char *v_sString,const char *vi_sOld,const char *vi_sNew)
{
	int nRet = 0;

	if ( NULL == v_sString || NULL == vi_sOld || NULL == vi_sNew )
	{
		return nRet;    
	}
	int t_lenold =strlen(vi_sOld);
	int t_lennew =strlen(vi_sNew);

	if ( 0 == t_lennew || 0 == t_lenold)
	{
		return nRet;
	}

	if (0 == strcmp(vi_sOld, vi_sNew))
	{
		return nRet;
	}


	char * pp;

	pp = strstr(v_sString,vi_sOld);

	if(NULL != pp)
	{	
		char sTmp[4096];
		char * p ;

		strcpy(sTmp,pp+t_lenold);
		strcpy(pp,vi_sNew);

		p = sTmp;		

		nRet++;
		char * aim_p = pp + t_lennew;

		while (true)
		{
			pp = strstr(p,vi_sOld);

			if(pp)
			{
				memcpy(aim_p,p,pp -p);
				aim_p+=(pp-p);
				strcpy(aim_p,vi_sNew);
				aim_p +=t_lennew;
				nRet++;

				p = pp +t_lenold;
			}
			else
			{
				strcat(v_sString,p);
				break;
			}
		}
	}

	return nRet;
}


void  DCAtCondition::GetElementValue(char* szDateCode,char* szValue)
{
	if (strcmp(szDateCode, "OFFER_STATE") == 0)	  //STATUS_CD
	{
		strcpy(szValue, m_stInfo.sOfferState);
	}
	else if (strcmp(szDateCode, "COMPLETED_DATE") == 0)		//prod_inst_# //FIRST_FINISH_DATE
	{
		strncpy(szValue, m_stInfo.sCompletedDate,8);
	}
	else if(strcmp(szDateCode,"SUB_COMPLETED_DATE") == 0) //prod_inst_sub_# //FIRST_FINISH_DATE
	{
		strncpy(szValue,m_stInfo.sSubCompleted,8);
	}
	else if(strcmp(szDateCode,"USER_TYPE_ID") == 0)
	{
		strcpy(szValue,m_stInfo.sUserTypeId);
	}
	else if(strcmp(szDateCode,"INSTALL_DATE") == 0)
	{
		strncpy(szValue,m_stInfo.sInstallDate,8);
	}
	else if(strcmp(szDateCode,"CRT_DATE") == 0)  //CREATE_DATE
	{
		strncpy(szValue,m_stInfo.sCrtDate,8);
	}
	else if(strcmp(szDateCode,"PRD_ID") == 0)  //PROD_ID
	{
		strcpy(szValue,m_stInfo.sPrdId);
	}
	else if(strcmp(szDateCode, "IF_PREPAY") == 0)  // pre pay flag
	{
		if (m_stInfo.nIfPrepay == 1)
		{
			strcpy(szValue, "1");
		}
		else
		{
			strcpy(szValue, "0");
		}
	}
}

void  DCAtCondition::SetElementValue(StInstElementValue stInfo)
{
	strcpy(m_stInfo.sOfferState , stInfo.sOfferState);
	strcpy(m_stInfo.sCompletedDate , stInfo.sCompletedDate);
	strcpy(m_stInfo.sSubCompleted , stInfo.sSubCompleted);
	strcpy(m_stInfo.sUserTypeId , stInfo.sUserTypeId);
	strcpy(m_stInfo.sInstallDate , stInfo.sInstallDate);
	strcpy(m_stInfo.sCrtDate , stInfo.sCrtDate);
	strcpy(m_stInfo.sPrdId , stInfo.sPrdId);
	m_stInfo.nIfPrepay = stInfo.nIfPrepay;
}


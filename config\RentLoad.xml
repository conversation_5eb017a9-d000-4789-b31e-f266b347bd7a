<?xml version="1.0" standalone="no" ?> 
<OCSConfig>
	<RentLoad>
		<log>
			<param name="logAddr"		>127.0.0.1:9007</param>
			<param name="level"			>2</param>
			<param name="prefix"		>ACCT</param>
	    	<param name="module"		>RentLoadUser</param> 
	    	<param name="perf"			>0</param>
			<param name="perf.ms"		>0</param>
		</log>
		<Latn>
			<param name="Latn553"				>553</param> 
			<param name="Latn554"				>554</param> 
			<param name="Latn555"				>555</param> 
			<param name="Latn556"				>556</param>
			<param name="Latn558"				>558</param> 
			<param name="Latn562"				>562</param> 
			<param name="Latn563"				>563</param>  
			<param name="Latn564"				>564</param>  
			<param name="Latn566"				>566</param>  
		</Latn>

		<param name="CorssChange"                       >1</param> 
		<param name="Subscriber"                       >RentLoad</param> 
		<param name="RouteProcess"                       >ToAcct</param> 
		<param name="GrayRefreshIntr"                       >60</param> 
		<param name="openKpi">0</param><!--使用监控指标和稽核指标开关 0关闭 1开启-->

		<dcfServerZK><!--统一任务分发-->
			<param name="ZookeeperAddr">192.168.161.21:22815</param><!--服务地址-->
			<param name="AESPasswd">abcdefghabcd</param><!--统一任务分发ZK连接-->
			<param name="MaxGetTaskNum">5</param><!--最大接收消息记录数-->
		</dcfServerZK>

		<Latn553>
			<param name="ReconnectInterval"	>5</param>
			<param name="ReconnectTimes"	>3</param>
			<param name="SleepTime"			>10</param>
			<param name="NewUserSleepTime"		>1</param>
			<param name="FrozenUserSleepTime"		>1</param>
			<param name="OldSleepTime"		>1</param>
			<param name="ZKServerAddr">134.64.225.20:22818,134.64.225.21:22818,134.64.225.22:22818,134.64.225.154:22818,134.64.225.155:22818</param>
			<param name="ZKServerName"		>5gacctservice553</param>
			<param name="ZKRootDir"			>/jstorm5gsa2</param>
			<param name="ZKDcfLogDir"		>/opt/dcf/tools/env/log/zookeeper/DCFClient_SERVC</param><!--调ZK服务的日志-->
			<param name="ZKDcfLogLevel"		>5</param>
			<param name="CycleSleepTime"	>30</param><!--循环扫描间隔休眠时间，单位秒-->
			<param name="MonIntervalMs"		>1000</param><!--发送统计指标间隔时间，单位毫秒-->
			<param name="BigAcctThreshold"	>30</param><!--大账户阈值，账户下用户数大于等于此值则存入分流表-->
			<param name="ZKServLoginPwd"	>abcdefghijklmnopqrstuvwxyz</param><!--服务化调用登陆密码-->
			<param name="ZKServAesPwd"		>aesabc</param><!--服务化调用加密密码-->
			<param name="FlowControl"		>1</param><!--发送流量控制开关配置项，=0为不控制，其他均为控制-->
			<param name="FlowTimeOut"		>12600</param><!--回调超时时间,单位为100ms-->
			<param name="EXPZKServerAddr"		>134.95.132.227:33818,134.95.132.228:33818,134.95.132.103:33818</param>
			<param name="EXPZKServerName"		>5gacctservice553</param>
			<param name="EXPZKRootDir"			>/jstorm5gsa2</param>
			<param name="EXPZKServLoginPwd"		>abcdefghijklmnopqrstuvwxyz</param><!--服务化调用登陆密码-->
			<param name="EXPZKServAesPwd"		>aesabc</param><!--服务化调用加密密码-->
			<param name="MostBigAcctThreshold"		>2000</param><!--特大账户阈值，账户下用户数大于等于此值则存入分流表group=3-->
			<param name="ContralGetOfrInst"		>1</param><!--=1取套餐实例，=0不取由acctbolt实现-->
			<param name="ZKHashKey"		>hashkey</param><!--调服务分流控制键,两张图保持一致-->
			<param name="FlowQueueSize"		>258</param><!--控制队列大小，默认为1024-->
			<param name="InstSendBatchNum"		>0</param><!--一批发送最多的套餐实例数-->
			<param name="NewUserFlowQSize"		>1024</param><!--新装流程控制队列大小，默认为1024-->
			<param name="NewUserInstSendBatN"		>10</param><!--新装流程一批发送的套餐实例数-->   
			<param name="FrozenUserFlowQSize"		>1024</param><!--复机流程控制队列大小，默认为1024-->
			<param name="FrozenUserInstSendBatN"		>10</param><!--复机流程一批发送的套餐实例数-->
			<param name="FlowSleepUs"		>2000</param><!--发送消息后休眠时间，单位微妙，用于控制发送速度-->
			<param name="DetailSource"			>0</param><!--开关值=0，不发送明细信息由账务优惠自己增强，开关值不等于0，发送明细-->
			<param name="FeecacheTimeOutSec"		>16</param><!--feecache记录超时时间，单位分钟-->
			<param name="BigAcctMsgSize">1024000</param><!---直接发送消息的最大size，超过则走DCA传送，单位byte-->
			<param name="DcaCacheMsgSize">131072</param><!---走DCA传送的记录，需要分条插入，每条记录的最大size，单位byte-->
			<param name="KpiDelayMs">1</param>
			<param name="KpiPortocol">1</param>			
			<param name="KpiFlag">0</param><!--0:不统计指标,1:统计完直接发送给大屏代理(可执行程序),2:只统计不发送,通过kpibolt发送给大屏代理(jstorm bolt)-->
			<param name="KpiAddr">**************:8797</param>
		</Latn553>		
	</RentLoad>
</OCSConfig>
#ifndef  _DC_RENT_ERR_H
#define  _DC_RENT_ERR_H

#define  ABM_RATEBLE_SUCCESS      500                     //收到累计量请求后批价正确,且需要向ABM发扣费请求
#define  ABM_RATEBLE_NODEDUCT     501                     //收到累计量请求后批价正确,但不需要向ABM发扣费请求  

#define  NO_FOUND_STRATEGY              (-200)              //没有找到定价策略 
#define  NO_FOUND_INST_STRATEGY         (-201)              //查询个性化策略失败 
#define  FIND_SUB_STRATEGY_ERR          (-203)              //查询用户订购的附属策略失败
#define  CHECK_SECTION_CONDITON_ERR     (-202)              //检查段落条件失败
#define  DEAL_ABM_DEDEUCT_ERR           (-300)              //处理ABM扣费应答失败
#define  ABM_RESPONSE_TYPE_ERR          (-301)              //ABM应答消息类型错误
#define  GET_OBJECT_ERR                 (-400)              //从对象池中取对象失败
#define  ABM_SEND_ERR                   (-401)              //向ABM发送扣费请求


#define ERR_NOT_FIND_CONDITION                          (-30223) //没有找到匹配的条件
#define ERR_FAIL_COM_SOURCE_CODE                        (-30224)// 找不到source_code
#define ERR_FAIL_COM_RATE_CONDITION                     (-30225)// 条件比较失败
#define Not_FOUNT_COM_RATE_CONDITION                     (-30226)// 找不到资费条件
#define ERR_TRUE_COM_RATE_CONDITION              0



//数据库连接错误码
const int ERR_DB_CONN_TIMEOUT	= -12170;	//timeout connect to db
const int ERR_DB_CONN_TERM		= -3135;	//update的时候数据库连接断开
const int ERR_DB_NOT_CONNECTED	= -3114;	//commit的时候数据库连接断开
const int ERR_DB_NOT_CONNECTED2	= -3113;	//end-of-file on communication channel
const int ERR_DB_NOT_CONNECTED3	= -12541;	//not connected to db
const int ERR_DB_NOT_CONNECTED4	= -12560;	//not connected to db
const int ERR_DB_SESSION_KILLED = -28;		//session be killed
const int ERR_DB_NOT_LOGON      = -1012;	//not logon to db
const int ERR_DB_SHUT_DOWN      = -1089;     //shutdown
const int ERR_DB_DBLINK_DOWN    = -2068;    //DBlink shutdown 
const int ERR_DB_SYNTAX_ANALYSIS = -1003 ;  //Syntax analysis
const int ERR_DB_THE_SAME_DBLINK_COVER    = -2051;    //another   session   in   same   transaction   failed  
const int ERR_DB_DBLINK_NAME_ERROR    = -2063;    //preceding line from <link_name> 
const int ERR_DB_KILL_SESSION   = -4004;


const int SQL_NOT_FIND_RECORD		=	-50;
const int SQL_NO_VALID_RECORD		=	-70;
const int NO_FIND_SQL  				=   -100;
const int SQL_EXCEPTION				=   -110;



#endif











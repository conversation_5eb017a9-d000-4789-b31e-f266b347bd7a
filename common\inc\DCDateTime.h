/*******************************************
*Copyrights  2016，深圳天源迪科计算机有限公司
*                   在线计费项目组
*All rights reserved.
*
* Filename：    DCDateTime.h
* Indentifier：     
* Description： 时间处理类        
* Version：     V1.0
* Author:       zsh
* Finished：    2016年06月01日
* History:
********************************************/

#ifndef DCDateTime_H_
#define DCDateTime_H_

#include <ace/Time_Value.h>
#include <string>

class DCDateTime
{
public:
	DCDateTime(const ACE_Time_Value v_timeValue);
    DCDateTime(const int v_nSec);   //以秒为构造函数
	DCDateTime();
	ACE_Time_Value &GetAceTime() {return m_aceTime;}
	
	std::string ToString(const std::string &v_format = "YYYYMMDDHHNNSS") const;
	int ToString(char *v_str,const char *v_format = "YYYYMMDDHHNNSS") const;
	DCDateTime & FromString(const std::string &v_str,const std::string &v_format = "YYYYMMDDHHNNSS");
	
	DCDateTime & operator=(const ACE_Time_Value v_aceTime);
/*	ACE_Time_Value &operator ACE_Time_Value() 
	{
		return m_aceTime;
	}
	const ACE_Time_Value &operator ACE_Time_Value() const
	{
		return m_aceTime;
	}
*/	
	ACE_Time_Value& GetAceTimeValue()
	{
		return m_aceTime;
	}

	const ACE_Time_Value& GetAceTimeValue() const
	{
		return m_aceTime;
	}

	bool operator<(const DCDateTime &v_right)
	{
		return (m_aceTime < v_right.m_aceTime);
	}
	bool operator>(const DCDateTime &v_right)
	{
		return (m_aceTime > v_right.m_aceTime);
	}
	bool operator==(const DCDateTime &v_right)
	{
		return (m_aceTime == v_right.m_aceTime);
	}

	friend DCDateTime operator+(const DCDateTime &v_valueL,const DCDateTime &v_valueR);
	friend DCDateTime operator-(const DCDateTime &v_valueL,const DCDateTime &v_valueR);
	
	int GetYear()	const;
	int GetMonth()	const;
	int GetDay()	const;
	int GetHour()	const;
	int GetMin()	const;
	int GetSec()	const;

	void SetSec(long sec, long usec=0);
	
	//取某年某月的天数
	static int GetMaxDay(int v_year,int v_mon);
	
private:
	ACE_Time_Value m_aceTime;
};


#endif

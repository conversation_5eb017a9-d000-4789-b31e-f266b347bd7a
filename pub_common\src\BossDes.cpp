/****************************************************************************************
*Copyrights  2006，深圳天源迪科计算机有限公司
*						OCS项目组
*All rights reserved.
*
* Filename：	BossDes.cpp		
* Indentifier：		
* Description： 加解密码实现		
* Version：		V1.0
* Author:		guoxd
* Finished：	2006年12月27日
* History:		
******************************************************************************************/

#include <stdio.h>
#include <stdlib.h>
#include "BossDes.h"

CBossDes::CBossDes()
{
}
CBossDes::~CBossDes()
{
}

static char INT2HCHAR(int nNum)
{
	if ( 0<=nNum && nNum<=9 )
		return '0'+nNum;
	if ( 10<=nNum && nNum<=15 )
		return 'A'+nNum-10;
	throw(nNum);
	return '0';
}

bool CBossDes::Str2Hstr(const CCharPtr& szSrc, CCharPtr& szDst)
{
	int nLen = szSrc.Length();
	CCharPtr mDst;
	mDst.SetLen(nLen*2+1);
	mDst[nLen*2] = 0;
	try
	{
		for( int n=0; n<nLen; n++ )
		{
			mDst[2*n+0] = INT2HCHAR(szSrc[n]/16);
			mDst[2*n+1] = INT2HCHAR(szSrc[n]%16);
		}
	}
	catch ( int nErr )
	{
		nErr = 0;
		return false;
	}
	szDst = mDst;
	return true;
}

static char HCHAR2INT(char chPart1, char chPart2)
{
	if( ( ('0'<=chPart1 && chPart1<='9') ||
		  ('A'<=chPart1 && chPart1<='F') )  &&
		( ('0'<=chPart2 && chPart2<='9') ||
		  ('A'<=chPart2 && chPart2<='F') ) )
	{
		int nPart[2]={0,0};
		if ('0'<=chPart1 && chPart1<='9')
			nPart[0] = chPart1 - '0';
		else
			nPart[0] = chPart1 - 'A';

		if ('0'<=chPart2 && chPart2<='9')
			nPart[1] = chPart2 - '0';
		else
			nPart[1] = chPart2 - 'A' + 10;
		return char(nPart[0]*16+nPart[1]);
	}
	throw(0);
	return '0';
}

bool CBossDes::Hstr2Str(const CCharPtr& szSrc, CCharPtr& szDst)
{
	int nLen = szSrc.Length();
	if (nLen%2 != 0)
		return false;
	CCharPtr mDst;
	mDst.SetLen(nLen/2+1);
	mDst[nLen/2] = 0;
	try
	{
		for ( int n=0; n<nLen; n+=2 )
		{
			mDst[n/2] = HCHAR2INT(szSrc[n],szSrc[n+1]);
		}
	}
	catch ( int nErr )
	{
		nErr = 0;
		return false;
	}
	szDst = mDst;
	return true;
}

// 从1开始数把bit搬到某个位置
static unsigned char MoveBit(unsigned char szSrc, int nFrom, int nTo)
{
	unsigned char chBuf = 0;
	unsigned char uMask = 0x01<<(8-nFrom);
	chBuf = uMask&szSrc;
	chBuf = (chBuf>>(8-nFrom));
	chBuf = chBuf<<(8-nTo);
	return chBuf;
}

static void Mask(char* pszChar3)
{
	unsigned char& A = ((unsigned char*)pszChar3)[0];
	unsigned char& B = ((unsigned char*)pszChar3)[1];
	unsigned char& C = ((unsigned char*)pszChar3)[2];
	unsigned char szBuf[3];
	// A1A2A3A4 A5A6A7A8   B1B2B3B4 B5B6B7B8  C1C2C3C4 C5C6C7C8 
	// C1A4B1C7 A7C4B2B7   C2A2C8B4 A1B5B8B3  C3A8B6A5 C5A6C6A3 
	szBuf[0] = MoveBit(C,1,1)|MoveBit(A,4,2)|MoveBit(B,1,3)|MoveBit(C,7,4)|
		MoveBit(A,7,5)|MoveBit(C,4,6)|MoveBit(B,2,7)|MoveBit(B,7,8);
	szBuf[1] = MoveBit(C,2,1)|MoveBit(A,2,2)|MoveBit(C,8,3)|MoveBit(B,4,4)|
		MoveBit(A,1,5)|MoveBit(B,5,6)|MoveBit(B,8,7)|MoveBit(B,3,8);
	szBuf[2] = MoveBit(C,3,1)|MoveBit(A,8,2)|MoveBit(B,6,3)|MoveBit(A,5,4)|
		MoveBit(C,5,5)|MoveBit(A,6,6)|MoveBit(C,6,7)|MoveBit(A,3,8);
	A = szBuf[0];
	B = szBuf[1];
	C = szBuf[2];
}
static void Mask2(char* pszChar3)
{
	unsigned char& A = ((unsigned char*)pszChar3)[0];
	unsigned char& B = ((unsigned char*)pszChar3)[1];
	unsigned char& C = ((unsigned char*)pszChar3)[2];
	unsigned char szBuf[3];
	// A1A2A3A4 A5A6A7A8   B1B2B3B4 B5B6B7B8  C1C2C3C4 C5C6C7C8 
	// C1A4B1C7 A7C4B2B7   C2A2C8B4 A1B5B8B3  C3A8B6A5 C5A6C6A3
	// B5B2C8A2 C4C6A5C2   A3A7B8B4 B6C3A8B7  A1B1C1A6 C5C7A4B3*
	szBuf[0] = MoveBit(B,5,1)|MoveBit(B,2,2)|MoveBit(C,8,3)|MoveBit(A,2,4)|
		MoveBit(C,4,5)|MoveBit(C,6,6)|MoveBit(A,5,7)|MoveBit(C,2,8);
	szBuf[1] = MoveBit(A,3,1)|MoveBit(A,7,2)|MoveBit(B,8,3)|MoveBit(B,4,4)|
		MoveBit(B,6,5)|MoveBit(C,3,6)|MoveBit(A,8,7)|MoveBit(B,7,8);
	szBuf[2] = MoveBit(A,1,1)|MoveBit(B,1,2)|MoveBit(C,1,3)|MoveBit(A,6,4)|
		MoveBit(C,5,5)|MoveBit(C,7,6)|MoveBit(A,4,7)|MoveBit(B,3,8);
	A = szBuf[0];
	B = szBuf[1];
	C = szBuf[2];
}

/////////////////////////////////////////////////////////////////////////////
bool CBossDes::Encrypt()
{
	//
	int nLen = m_szData.Length();
	if ( nLen%2 != 0 )
		return false;
	CCharPtr szKKK;
	szKKK.SetLen(nLen/2*3+1);
	char szBuf[3];
	for ( int n=0; n<nLen/2; n++ )
	{
		szBuf[0] = m_szData[2*n+0];
		szBuf[1] = m_szData[2*n+1];
		szBuf[2] = 0xE0|((n+1)&0x1F);
		Mask(szBuf);
//		Mask2(szBuf);
		szKKK[3*n+0] = szBuf[0];
		szKKK[3*n+1] = szBuf[1];
		szKKK[3*n+2] = szBuf[2];
	}
	szKKK[nLen/2*3+1] = 0;
	m_szData = szKKK;
	return true;
}

bool CBossDes::Decrypt()
{
	int nLen = m_szData.Length();
	if ( nLen%3 != 0 )
		return false;
	CCharPtr szKKK;
	szKKK.SetLen(nLen/3*2+1);
	char szBuf[3];
	for ( int n=0; n<nLen/3; n++ )
	{
		szBuf[0] = m_szData[3*n+0];
		szBuf[1] = m_szData[3*n+1];
		szBuf[2] = m_szData[3*n+2];
		Mask2(szBuf);
		szKKK[2*n+0] = szBuf[0];
		szKKK[2*n+1] = szBuf[1];
		if ((szBuf[2]&0x1F) != ((n+1)&0x1F) )
			return false;
	}
	szKKK[nLen/3*2+1] = 0;
	m_szData = szKKK;
	return true;
}

#ifdef MAKE_MAIN
int main()
{
	char* szBuf = NULL;
	szBuf = new char[1024*1024+1];
	while (fgets(szBuf, 1024*1024, stdin) != NULL)
	{
		CCharPtr strBuf;
		strBuf.Attach(szBuf);
		int nLeft = strBuf.Find("\\\\pwd[",0);
		if (nLeft != -1)
		{
			int nRight = strBuf.Find("]\\", nLeft);
			if ( nRight != -1)
			{
				CCharPtr szLeft = strBuf.Left(nLeft);
				CCharPtr szPwdU = strBuf.Mid(nLeft+6,nRight-(nLeft+6)-1);

				CBossDes kk;
				kk.Str2Hstr(szPwdU,kk.m_szData); 
				kk.Encrypt();
				szLeft += "\\\\pak{";
				fputs(szLeft, stdout);
				fputs(kk.m_szData, stdout);
				CCharPtr szLast = strBuf.Right(strBuf.Length()-(nRight+2)-1);
				szLast = "}\\" + szLast;
				fputs(szLast, stdout);
			}
			else
			{
				fputs(szBuf, stdout);
			}
		}
		else
		{
			fputs(szBuf, stdout);
		}
		strBuf.Detach();
	}
	delete[] szBuf;
	return 0;
}
#endif                                              

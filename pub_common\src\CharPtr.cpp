/****************************************************************************************
*Copyrights  2006，深圳天源迪科计算机有限公司
*						OCS项目组
*All rights reserved.
*
* Filename：	CharPtr.cpp		
* Indentifier：		
* Description： 字符相关操作		
* Version：		V1.0
* Author:		guoxd
* Finished：	2006年12月27日
* History:		
******************************************************************************************/

#include <stdio.h>
#include <stdarg.h>
#include "CharPtr.h"

static char* g_pNull = (char*)"";

//////////////////////////////////////////////////////////////////////
bool CCharPtr::memset(LPSTR lpszDst, int nFilled, int nBuffLen)
{
	for ( int n=0; n<nBuffLen; n++)
		lpszDst[n] = nFilled;
	return true;
}

bool CCharPtr::strlen(LPCSTR lpszStr, int& nLen)
{
	if ( lpszStr == NULL )
		return false;
	int n = 0;
	while ( *lpszStr!='\0' && n>=0 )
	{
		lpszStr++;
		n++;
	}
	nLen = n;
	return nLen>=0;
}

bool CCharPtr::strcpy(LPSTR lpszDst, LPCSTR lpszSrc, int nBuffLen, int& nCopyed)
{
	nCopyed = 0;
	if ( lpszDst == NULL && nBuffLen==0)
		return false;
	if (lpszSrc == NULL)
		return true;
	while ( *lpszSrc!='\0' && nBuffLen-->0 )
	{
		*lpszDst++ = *lpszSrc++;
		nCopyed++;
	}
	lpszDst[nBuffLen] = 0;
	return true;
}

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CCharPtr::CCharPtr(int nBlock) : m_nBlock(nBlock)
{
    m_pointer = g_pNull;
    m_nMaxLen = 0;
}

LPSTR CCharPtr::Attach(LPSTR psz, int nLen)
{
	LPSTR pOld = m_pointer;
	if ( nLen == 0 )
	{
		if ( strlen(psz, nLen) )
		{
			m_nMaxLen = nLen;
		}
		else
		{
			m_nMaxLen = 0;
		}
	}
	else
	{
		m_nMaxLen = nLen;
	}
	m_pointer = psz;
	return pOld;
}

bool CCharPtr::CopyToBuf(LPSTR pBuff, int& nBuffLen, bool bForce)
{
	if ( pBuff == NULL )
		return false;
	if ( nBuffLen > m_nMaxLen && !bForce)
		return false;
	int n=0;
	for ( n=0; n<nBuffLen&&n<m_nMaxLen; n++)
	{
		pBuff[n] = m_pointer[n];
	}
	nBuffLen = n;
	return true;
}

bool CCharPtr::Copy(LPCSTR str, int nLen)
{
	if ( str == NULL || nLen > m_nMaxLen || nLen < 0 )
		return false;
	int n = 0;
	for ( n=0; n<nLen; n++ )
	{
		m_pointer[n] = str[n];
	}
	m_pointer[nLen] = '\0';
	return true;
}

LPSTR CCharPtr::Detach()
{
	LPSTR pOld = m_pointer;
	m_pointer = g_pNull;
	m_nMaxLen = 0;
	return pOld;
}

CCharPtr::CCharPtr(const CCharPtr& src):m_nBlock(src.m_nBlock)
{
    m_pointer = g_pNull;
    m_nMaxLen = 0;

    this->operator = (src);
}

CCharPtr::CCharPtr(char ch, int nCount):m_nBlock(20)
{
    m_pointer = g_pNull;
    m_nMaxLen = 0;
	if ( nCount < 1 )
		nCount = 1;
    PrepareBuffer(nCount);
    for(int n=0; n<nCount; n++)
	{
		m_pointer[n] = ch;
	}
}

CCharPtr::CCharPtr(LPCSTR szSrc, bool bCopy):m_nBlock(20)
{
    m_pointer = g_pNull;
    m_nMaxLen = 0;
	int nLen = 0;
	if ( strlen(szSrc, nLen) )
	{
		if ( bCopy )
		{
			PrepareBuffer(nLen+1);
			int nCopyed;
			strcpy(m_pointer, szSrc, nLen, nCopyed);
		}
		else
		{
			m_pointer = (char*)szSrc;
			m_nMaxLen = nLen+1;
		}
	}
}

CCharPtr& CCharPtr::operator = (const CCharPtr& src)
{
	if ( this != &src && m_pointer != src.m_pointer)
	{
		PrepareBuffer( src.m_nMaxLen + 1 );
		if ( m_pointer != NULL )
		{
			int nCopyed;
			strcpy(m_pointer, src, src.m_nMaxLen, nCopyed);
		}
	}
    return *this;
}

CCharPtr& CCharPtr::operator = (LPCSTR str)
{
    if ( str && str != m_pointer )
    {
		int nLen;
		if ( strlen(str, nLen) )
		{
			PrepareBuffer(nLen + 1);
			int nCopyed;
			strcpy(m_pointer, str, nLen, nCopyed);
		}
    }
    return *this;
}

CCharPtr& CCharPtr::operator += (const CCharPtr& src)
{
    CCharPtr temp = *this;
    PrepareBuffer(temp.Length() + src.Length()+1);
	*this = temp;
	int nLen = 0;
	strcpy(m_pointer+temp.Length(), src.m_pointer, src.Length(), nLen);
    return *this;
}

CCharPtr operator + (const CCharPtr& szLeft, const CCharPtr& szright)
{
    const CCharPtr& src1 = szLeft;
    const CCharPtr& src2 = szright;
    int nLen = 0;
    nLen += src1.Length();
    nLen += src2.Length();

    CCharPtr ret;
    ret.PrepareBuffer(nLen);
    ret = src1;
    ret += src2;

    //这里会自动call拷贝构造函数
    return ret;
}

bool CCharPtr::operator == (LPCSTR str) const
{
	if ( m_pointer == str )
		return true;

	char* p1 = m_pointer;
	char* p2 = (char*)str;
	while ( *p1++ == *p2++)
	{
		if ( *p1 == '\0')
			return true;
	}
	return false;
}

bool CCharPtr::operator == (const CCharPtr& src) const
{
	if ( this == &src)
		return true;

    return this->operator == (src.m_pointer);

}

bool CCharPtr::operator != (const CCharPtr& src) const
{
    return !this->operator ==(src);
}

CCharPtr::~CCharPtr()
{
    if (m_pointer != g_pNull && m_pointer != NULL)
    {
        delete[] m_pointer;
    }
}

int CCharPtr::Format(LPCSTR szFormat, ...)
{
    if (m_nMaxLen < 255)
        SetLen(256);
    va_list marker;
    /* Initialize variable arguments. */
    va_start( marker, szFormat );
    int nRet = vsprintf(m_pointer, szFormat, marker);
    /* Reset variable arguments.      */
    va_end( marker );
    return nRet;
}

CCharPtr CCharPtr::Mid(int nStart, int nCount) const
{
    int nPos = Length();
    nPos = nStart<nPos ? nStart : nPos;
    nPos = 0>nPos ? 0 : nPos;
    int nMaxLen = Length()-nStart+1;
    nMaxLen = nCount<nMaxLen ? nCount+1 : nMaxLen;

    CCharPtr ret;
    ret.PrepareBuffer(nMaxLen);
    for (int n=0; n<nMaxLen; n++)
    {
        ret.m_pointer[n] = m_pointer[nPos+n];
    }
    return ret;
}

CCharPtr CCharPtr::Left(int nCount) const
{
    int nMaxLen = Length();
    if (nCount < 0 )
    {
        nMaxLen = 0;
    }
    else
    {
        nMaxLen = nMaxLen<nCount ? nMaxLen: nCount;
    }
    CCharPtr ret;
    ret.PrepareBuffer(nMaxLen+1);

    for (int n=0; n<nMaxLen; n++)
    {
        ret.m_pointer[n] = m_pointer[n];
    }
	ret.m_pointer[nCount] = 0;
    return ret;
}

int CCharPtr::Replace( char chOld, char chNew )
{
	int nCount =0;
    int nMaxLen = Length();
    for (int n=0; n<nMaxLen; n++)
    {
        if ( m_pointer[n] == chOld)
		{
			nCount++;
			m_pointer[n] = chNew;
		}
	}
    return nCount;
}

CCharPtr CCharPtr::Right(int nCount) const
{
    int nMaxLen = Length();
    if (nCount < 0 )
    {
        nMaxLen = 0;
    }
    else
    {
        nMaxLen = nMaxLen<nCount ? nMaxLen : nCount;
    }
    CCharPtr ret;
    ret.PrepareBuffer(nMaxLen);
    int nPos = Length() - nMaxLen;
    for (int n=0; n<nMaxLen; n++)
    {
        ret.m_pointer[n] = m_pointer[nPos+n];
    }
    return ret;
}

void CCharPtr::MakeUpper()
{
	for ( int n=0; n<Length(); n++)
	{
		if ( 'a' <= m_pointer[n] && m_pointer[n] <= 'z' )
		{
			m_pointer[n] -= 'a';
			m_pointer[n] += 'A';
		}
	}
}

void CCharPtr::MakeLower()
{
	for ( int n=0; n<Length(); n++)
	{
		if ( 'A' <= m_pointer[n] && m_pointer[n] <= 'Z' )
		{
			m_pointer[n] -= 'A';
			m_pointer[n] += 'a';
		}
	}
}

bool CCharPtr::PrepareBuffer(int nLen)
{
    if (nLen <= m_nMaxLen)
    {
		// assert (m_pointer != NULL)
		memset(m_pointer, 0, m_nMaxLen);
		return true;
    }

	if ( m_nBlock == 0 )
		return false;

	char* pOld = m_pointer;
    while (m_nMaxLen < nLen)
	{
		m_nMaxLen += m_nBlock;
	}

    m_pointer = new char[m_nMaxLen + 1];

    if ( m_pointer == NULL )
	{
		m_pointer = pOld;
		return false;
	}
    memset(m_pointer, 0, m_nMaxLen);
	if (pOld != g_pNull && pOld != NULL)
		delete[] pOld;
    return true;
}

CCharPtr operator + (LPCSTR str1, const CCharPtr& src2)
{
    return CCharPtr(str1) + src2;
}

bool operator == (LPCSTR str1, const CCharPtr& src2)
{
    CCharPtr src1(str1);
    return src1 == src2;
}

bool operator != (LPCSTR str1, const CCharPtr& src2)
{
    return !operator == (str1, src2);
}

char CCharPtr::operator [] (int nIdx) const
{
    if ( nIdx>=0 && nIdx<m_nMaxLen )
    {
        if (m_pointer != NULL)
            return m_pointer[nIdx];
    }
    return '\0';
}

char& CCharPtr::operator [] (int nIdx)
{
	if (m_nRefCount > 0)
	{
	}
    static char chErr = '\0';
    if ( nIdx>=0 && nIdx<m_nMaxLen )
    {
        if (m_pointer != NULL)
            return m_pointer[nIdx];
    }
    return chErr;
}

int CCharPtr::Length() const
{
    if (m_pointer == NULL)
        return 0;
	int nLen;
	strlen(m_pointer, nLen);
    return nLen;
}

LPSTR CCharPtr::GetBuffer(int nLen)
{
    SetLen(nLen);
    return m_pointer;
}

int CCharPtr::SetLen(int nLen)
{
    PrepareBuffer(nLen);
    return m_nMaxLen;
}

int CCharPtr::Find( char ch , int nStart ) const
{
    if (m_pointer == NULL)
        return -1;
	for (int n=nStart; n<m_nMaxLen; n++)
	{
		if ( m_pointer[n] == ch )
			return n;
    }
	return -1;
}

int CCharPtr::Find( LPCSTR str , int nStart ) const
{
    if (m_pointer == NULL)
        return -1;
/*
    LPSTR pRet = strstr(m_pointer+nStart, str);
    if (pRet == NULL)
        return -1;
    return pRet - m_pointer + 1;
*/
	int nLen = 0;
	if ( ! strlen(str, nLen) )
		return 0;
	int nLenMe = 0;
	if ( ! strlen(m_pointer, nLenMe) )
		return -1;
	for (int n=nStart; n<m_nMaxLen; n++)
	{
		if ( m_pointer[n] != str[0] )
			continue;
		int j=0;
		for ( j=0; j<nLen; j++ )
		{
			if ( m_pointer[n+j] != str[j] )
				break;
		}
		if ( j == nLen )
			return n;
    }
	return -1;

}

int CCharPtr::ReverseFind( char ch , int nStart ) const
{
    CCharPtr tmp = Left(Length()-nStart);
    int nPos = tmp.Find(ch,0);
    if (nPos == -1)
        return -1;

    int nNextPos = tmp.Find(ch,nPos);
    while(nNextPos != -1)
    {
        nPos = nNextPos;
        nNextPos = tmp.Find(ch,nPos);
    }
    return nPos;
}

int CCharPtr::ReverseFind( LPCSTR str , int nStart ) const
{
    CCharPtr tmp = Left(Length()-nStart);
    int nPos = tmp.Find(str,0);
    if (nPos == -1)
        return -1;

    int nNextPos = tmp.Find(str,nPos);
    while(nNextPos != -1)
    {
        nPos = nNextPos;
        nNextPos = tmp.Find(str,nPos);
    }
    return nPos;
}


bool CCharPtr::operator != (LPCSTR str) const
{
    return this->operator != (CCharPtr(str));
}

//inline CCharPtr::operator LPCSTR () const
CCharPtr::operator LPCSTR () const
{
	return m_pointer;
}
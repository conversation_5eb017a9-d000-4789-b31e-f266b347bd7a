  /*-------------------------------------------------------------------

  ---                                                             ---

  ---            <PERSON>zhen Tianyuan DIC Comp. Ltd.                 ---

  ---            Programmed by <PERSON>                     ---

  ---            Created time: 1999-09-15                         ---

  ---            Refreshed time: 1999-09-22                       ---                               

  ---            Communication  telepone: +81-0755-3329693        ---

  ---            e-mail: <EMAIL>                          ---

  ---                                                             ---

  -------------------------------------------------------------------*/

  /* utilLocal.cc included all functions(getCurrentDate,isWeekDay and 

     isStringCode implemented. */                



#include <time.h>
#include <stdio.h>
#include <stdlib.h>
#include <math.h>

#include <string.h>

#include <errno.h>
//
#include <fcntl.h>


#ifndef WIN32
	#include <sys/types.h>
	#include <sys/timeb.h>
	#include "UtilLocal.h"

	#include <sys/mman.h>
	#include <unistd.h>
#else
	#include <sys\types.h>
	#include <sys\timeb.h>
	#include "UtilLocal.h"
	#include <stdio.h>
	#include <io.h> 
#endif


using namespace std;


/* get hour minute and second */

void getHHMMSS(const char *time_string,int *hour,int *min,int *sec,char flag)

{

   int len=0,i=0,_flag=1,j=0;

   char tmp[3];

   tmp[0]='\0';

   len=strlen(time_string);

   for(i=0;i<len;i++)

   {

   	if(time_string[i]==flag)

   	{

   	   switch (_flag){

   	   case 1:

   	       *hour=atol(tmp);

   	       tmp[0]='\0';

   	       j=0;

   	       break;

   	   case 2:

   	       *min=atol(tmp);

   	       tmp[0]='\0';

   	       j=0;

   	       break;

   	   case 3:

   	       *sec=atol(tmp);

   	       tmp[0]='\0';

   	       j=0;

   	       break;

   	   default:

   	       j=0;

   	       break;   }

   	    _flag++;

   	 }

   	 else

   	 {

   	    tmp[j]=time_string[i];

   	    j++;

   	 }

   }

   if(strlen(tmp)>0)

       *sec=atol(tmp);

   return;

}



/*void getFileName(char *path,char *file_name)

{

    char sep[]="/";

    char* token;

    token=strtok(path,sep);

    while(token!=NULL)

    {

        strcpy(file_name,token);

        

        token=strtok(NULL,sep);

    }

    return;

}*/



void getCurrentDate(char* v_date)

{

    time_t ltime;

    struct tm *today;

    if(v_date==(char *)NULL)

	return;

    

    /* Set time zone from TZ environment variable. If TZ is not set,

     * the operating system is queried to obtain the default value 

     * for the variable.      */

    tzset();

    

    /* Display operating system-style date and time. */

    time( &ltime );

    

    /* Convert to time structure. */

    today = localtime( &ltime );

    

    /* output the year, month and day ,hour,minute and second in format:"yyyymmddhhmmss" */

    sprintf(v_date,"%d%.2d%.2d%.2d%.2d%.2d",today->tm_year+1900,today->tm_mon+1,

            today->tm_mday,today->tm_hour,today->tm_min,today->tm_sec);

    v_date[14]='\0';

    return;

}



void getWeekDay(int *v_week)

{

    time_t ltime;

    struct tm *today;

    /* Set time zone from TZ environment variable. If TZ is not set,

     * the operating system is queried to obtain the default value 

     * for the variable.      */

    tzset();



    /* Display operating system-style date and time. */

    time( &ltime );



    /* Convert to time structure. */

    today = localtime( &ltime );

    *v_week=today->tm_wday;

    if(*v_week==0)

        *v_week=7;

    return;

}



void dateToWeek(const char* v_date,int *v_week)

{

     char temp[5];

     struct tm tm1,*tm2;

     time_t ltime;

     int year,mon,day,hh,mm,ss;

     temp[0]='\0';

     strncpy(temp,v_date,4);

     year=atol(temp)-1900;

     memset(temp,'\0',5);

     strncpy(temp,v_date+4,2);

     mon=atol(temp)-1;

     strncpy(temp,v_date+6,2);

     day=atol(temp);

     strncpy(temp,v_date+8,2);

     hh=atol(temp);

     strncpy(temp,v_date+10,2);

     mm=atol(temp);

     strncpy(temp,v_date+12,2);

     ss=atol(temp);

     tm1.tm_sec=ss;

     tm1.tm_min=mm;

     tm1.tm_hour=hh;

     tm1.tm_mday=day;

     tm1.tm_mon=mon;

     tm1.tm_year=year;

     ltime=mktime(&tm1);

     tm2=localtime(&ltime);

     *v_week=tm2->tm_wday;

     if(*v_week==0)

        *v_week=7;

     return;

}



void selftimeAdd(char* begin_time,int seconds)

{

  int year,mon,day,hh,mm,ss;

  char temp[5];

  struct tm tm1,*tm2;

  time_t ltime;

  temp[0]='\0';

  memset(&tm1,0,sizeof(tm1));

  strncpy(temp,begin_time,4);

  year=atol(temp);

  year=year-1900;

  memset(temp,'\0',5);

  strncpy(temp,begin_time+4,2);

  mon=atol(temp)-1;

  strncpy(temp,begin_time+6,2);

  day=atol(temp);

  strncpy(temp,begin_time+8,2);

  hh=atol(temp);

  strncpy(temp,begin_time+10,2);

  mm=atol(temp);

  strncpy(temp,begin_time+12,2);

  ss=atol(temp);

  tm1.tm_sec=ss;

  tm1.tm_min=mm;

  tm1.tm_hour=hh;

  tm1.tm_mday=day;

  tm1.tm_mon=mon;

  tm1.tm_year=year;

  ltime=mktime(&tm1);

  ltime+=seconds;

  //printf("ltime=%d\n",ltime);

  tm2=localtime(&ltime);

  

  sprintf(begin_time,"%d%.2d%.2d%.2d%.2d%.2d",tm2->tm_year+1900,tm2->tm_mon+1,

            tm2->tm_mday,tm2->tm_hour,tm2->tm_min,tm2->tm_sec);

  return;

	

}





/* calculate the end_time after the begin time is added many seconds */

/* time format is yyyymmddhhmmss  */

void timeAdd(const char* begin_time,int seconds,char* end_time)

{

  int year,mon,day,hh,mm,ss;

  char temp[5];

  struct tm tm1,*tm2;

  time_t ltime;

  temp[0]='\0';

  

  memset(&tm1,0,sizeof(tm1));

  strncpy(temp,begin_time,4);

  year=atol(temp);

  year=year-1900;

  memset(temp,'\0',5);

  strncpy(temp,begin_time+4,2);

  mon=atol(temp)-1;

  strncpy(temp,begin_time+6,2);

  day=atol(temp);

  strncpy(temp,begin_time+8,2);

  hh=atol(temp);

  strncpy(temp,begin_time+10,2);

  mm=atol(temp);

  strncpy(temp,begin_time+12,2);

  ss=atol(temp);

  tm1.tm_sec=ss;

  tm1.tm_min=mm;

  tm1.tm_hour=hh;

  tm1.tm_mday=day;

  tm1.tm_mon=mon;

  tm1.tm_year=year;

  ltime=mktime(&tm1);

  //printf("ltime=%d\n",ltime);

  ltime+=seconds;

  //printf("ltime+++=%d\n",ltime);

  tm2=localtime(&ltime);

  sprintf(end_time,"%d%.2d%.2d%.2d%.2d%.2d",tm2->tm_year+1900,tm2->tm_mon+1,

            tm2->tm_mday,tm2->tm_hour,tm2->tm_min,tm2->tm_sec);

  return;

} 



/* calculate the seconds between the begin time and the end time */

/* time format is yyyymmddhhmmss   */

int secondsBetween(const char* begin_time,const char* end_time)

{

   char temp[5];

   int result=0;

   struct tm b_tm,e_tm;

   time_t b_time,e_time;

   int b_year,b_mon,b_day,b_hh,b_min,b_sec,e_year,e_mon,e_day,e_hh,e_min,e_sec;

   temp[0]='\0';

   

   strncpy(temp,begin_time,4);

   temp[4]='\0';

   b_year=atol(temp)-1900;

   strcpy(temp,"    ");

   temp[0]='\0';

   strncpy(temp,begin_time+4,2);

   temp[2]='\0';

   b_mon=atol(temp)-1;

   strncpy(temp,begin_time+6,2);

   temp[2]='\0';

   b_day=atol(temp);

   strncpy(temp,begin_time+8,2);

   temp[2]='\0';

   b_hh=atol(temp);

   strncpy(temp,begin_time+10,2);

   temp[2]='\0';

   b_min=atol(temp);

   strncpy(temp,begin_time+12,2);

   temp[2]='\0';

   b_sec=atol(temp);

   strncpy(temp,end_time,4);

   temp[4]='\0'; 

   e_year=atol(temp)-1900;

   strcpy(temp,"    ");

   temp[0]='\0';

   strncpy(temp,end_time+4,2);

   temp[2]='\0';

   e_mon=atol(temp)-1;

   strncpy(temp,end_time+6,2);

   temp[2]='\0';

   e_day=atol(temp);

   strncpy(temp,end_time+8,2);

   temp[2]='\0';

   e_hh=atol(temp);

   strncpy(temp,end_time+10,2);

   temp[2]='\0';

   e_min=atol(temp);

   strncpy(temp,end_time+12,2);

   temp[2]='\0';

   e_sec=atol(temp);

   b_tm.tm_sec=b_sec;

   b_tm.tm_min=b_min;

   b_tm.tm_hour=b_hh;

   b_tm.tm_mday=b_day;

   b_tm.tm_mon=b_mon;

   b_tm.tm_year=b_year;



   e_tm.tm_sec=e_sec;

   e_tm.tm_min=e_min;

   e_tm.tm_hour=e_hh;

   e_tm.tm_mday=e_day;

   e_tm.tm_mon=e_mon;

   e_tm.tm_year=e_year;



   b_time=mktime(&b_tm);

   e_time=mktime(&e_tm);

   result=e_time-b_time;

   return result;

}



/* time unit is hour */

int timeCount(const char* begin_time,int *duration1,const char* offset,int *duration2)

{

    int call_hour,call_min,call_sec,offset_hour,offset_min,offset_sec;

    int seconds_betw,n_count=0;

    char temp[3],begin_t[7],new_begin[15],begin_time1[15];

    temp[0]='\0';

    begin_t[0]='\0';

    new_begin[0]='\0';

    begin_time1[0]='\0';

    strcpy(begin_time1,begin_time);

    strncpy(begin_t,begin_time+8,6);

    strncpy(temp,begin_t,2);

    call_hour=atol(temp);

    strncpy(temp,begin_t+2,2);

    call_min=atol(temp);

    strncpy(temp,begin_t+4,2);

    call_sec=atol(temp);

    strncpy(temp,offset,2);

    offset_hour=atol(temp);

    strncpy(temp,offset+2,2);

    offset_min=atol(temp);

    strncpy(temp,offset+4,2);

    offset_sec=atol(temp);

    seconds_betw=(call_hour-offset_hour)*3600+(call_min-offset_min)*60+(call_sec-offset_sec);

    strncpy(new_begin,begin_t,8);

    strncat(new_begin,offset,6);



    if(seconds_betw<0)

    {

        if(*duration1+seconds_betw<=0)

	    n_count+=0;

        else

        {

            if(seconds_betw+*duration1<=*duration2)

               n_count=*duration1+seconds_betw;

            else

            {

                n_count+=*duration2;

                *duration1=*duration1+seconds_betw-24*3600-*duration2;

                if(*duration1>0)

                {

		    timeAdd(new_begin,24*3600,begin_time1);

                    timeCount(begin_time1,duration1,offset,duration2);

                }

	     }

         }

    }

    else

    {

	if(seconds_betw+*duration1<=*duration2)

            n_count+=*duration1;

        else

        {

            n_count+=*duration2-seconds_betw;

            *duration1=*duration1+seconds_betw-24*3600-*duration2;

            if(*duration1>0)

            {

               timeAdd(new_begin,24*3600,begin_time1);

               timeCount(begin_time1,duration1,offset,duration2);

            }

        }

    }

    return n_count;

}



/* time unit is month,if the call_time(begin_time, end_time) is included in the   time (offset1,offset2) return 1,otherwise return 0 */

int monthBetween(int offset1,int duration1,const char* offset2,int duration2,

const char* begin_time,int call_duration)

{

  int begin_day=0,end_day=0,call_year=0,call_mon=0,call1,call2;

  int seconds_cycle=0,month_day=0;

  char tmp[5],end_time[15],off_time1[15],off_time2[15],tmp_time[15];

  tmp[0]='\0';

  end_time[0]='\0';

  off_time1[0]='\0';

  off_time2[0]='\0';

  tmp_time[0]='\0';

  /* off_time1 is the begin time of discount,off_time2 is the end time of discount */

  strncpy(off_time1,begin_time,6);

  off_time1[6]='\0';

  sprintf(off_time1,"%6s%.2d%6s",off_time1,offset1+duration1-1,offset2);

  timeAdd(off_time1,duration2*3600,off_time2);

  timeAdd(begin_time,call_duration,end_time);

  strncpy(tmp,begin_time+6,2);

  begin_day=atol(tmp);

  strncpy(tmp,end_time+6,2);

  end_day=atol(tmp);

  strncpy(tmp,begin_time,4);

  call_year=atol(tmp);

  tmp[0]='\0';

  strncpy(tmp,begin_time+4,2);

  call_mon=atol(tmp);

  if(begin_day<offset1)

  {

      call1=call_duration-secondsBetween(begin_time,off_time1);

      if(call1<=0)

          return 0;

      return 1;

  }

  if(begin_day==offset1)

  {

     if(strncmp(begin_time+8,off_time1+8,6)<0)

     {

         call1=call_duration-secondsBetween(begin_time,off_time1);

         if(call1<=0)

            return 0;

         return 1; 

    }

    if(strncmp(begin_time+8,off_time1+8,6)>0)

    {

        if(strncmp(begin_time+8,off_time2+8,6)<=0)

            return 1;

        else

        {

           timeAdd(off_time1,24*3600,tmp_time);

           if(call_duration>secondsBetween(begin_time,tmp_time))

              return 0;

            return 1;

       }

    }  

    else

        return 1;

  }

  if((begin_day>offset1)&&(begin_day<offset1+duration1-1))

  {

      strcpy(off_time1," ");

      off_time1[0]='\0';

      strncpy(off_time1,begin_time,8);

      strcat(off_time1,offset2);

      timeAdd(off_time1,duration2*3600,off_time2);

      if(secondsBetween(begin_time,off_time1)>0)

      {

          if(call_duration>secondsBetween(begin_time,off_time1))

              return 1;

          return 0;

      }

      if(secondsBetween(begin_time,off_time1)<0)

      {

          if(secondsBetween(begin_time,off_time2)>0)

              return 1;

          else

          {

               timeAdd(off_time2,24*3600,off_time1);

               if(secondsBetween(begin_time,off_time1)>=call_duration)

                  return 0;

               return 1;

          }

      }

      else 

          return 1;

  }    

  if(begin_day==offset1+duration1-1)

  {

     strcpy(off_time1,"  ");

     off_time1[0]='\0';

     strncpy(off_time1,begin_time,8);

     off_time1[8]='\0';

     strcat(off_time1,offset2); 

     timeAdd(off_time1,duration2*3600,off_time2);

     if(secondsBetween(begin_time,off_time1)>0)

     {

         if(secondsBetween(begin_time,off_time1)<=call_duration)

             return 1;

         return 0;

     }

     if(secondsBetween(begin_time,off_time1)<0)

     {

         if(secondsBetween(begin_time,off_time2)>0)

             return 1;

         if((call_mon==1)||(call_mon==3)||(call_mon==5)||(call_mon==7)||(call_mon==8)||(call_mon==10)||(call_mon==12))

             month_day=31;

         if((call_mon==4)||(call_mon==6)||(call_mon==9)||(call_mon==11))

             month_day=30;

         else

        {

           if((call_year%4==0&&call_year%100!=0)||(call_year%400==0))

 	       month_day=29;

           else

               month_day=28;

        }

        seconds_cycle=(month_day-(offset1+duration1-1)+offset1)*24*3600-duration2*3600-secondsBetween(off_time1,begin_time);

        if(seconds_cycle>=call_duration)

           return 0; 

        return 1;

     }

  } 

  if(begin_day>offset1+duration1-1)

  {

      strncpy(off_time1,begin_time,6);

      off_time1[6]='\0';

      sprintf(off_time1,"%6s%.2d%6s",off_time1,offset1+duration1-1,offset2);

      if((call_mon==1)||(call_mon==3)||(call_mon==5)||(call_mon==7)||(call_mon==8)||(call_mon==10)||(call_mon==12))

         month_day=31;

      if((call_mon==4)||(call_mon==6)||(call_mon==9)||(call_mon==11))

         month_day=30;

      else

      {

         if((call_year%4==0&&call_year%100!=0)||(call_year%400==0))

            month_day=29;

         else

            month_day=28;

      }

      seconds_cycle=(month_day-(offset1+duration1-1)+offset1)*24*3600-duration2*3600-secondsBetween(off_time1,begin_time);

      if(seconds_cycle>=call_duration)

          return 0;

      return 1;

  }

  return 0;

}





/* time unit is season */

int seasonBetween(int offset1,int duration1,const char* offset2,int duration2,const char* begin_time,int call_duration)

{

  int begin_day=0,end_day=0,call_year=0,call_mon=0,call_mon1=0,tmp_mon=0;

  int seconds_cycle=0,month_day=0,mon_mod=0;

  char tmp[5],end_time[15],off_time1[15],off_time2[15],tmp_time[15];

  tmp[0]='\0';

  end_time[0]='\0';

  off_time1[0]='\0';

  off_time2[0]='\0';

  tmp_time[0]='\0';

  /* off_time1 is the begin time of discount,off_time2 is the end time of discount */

  strncpy(tmp,begin_time,4);

  call_year=atol(tmp);

  strcpy(tmp,"    ");

  tmp[0]='\0';

  strncpy(tmp,begin_time+4,2);

  call_mon=atol(tmp);

  strcpy(tmp," ");

  tmp[0]='\0';

  strncpy(tmp,end_time+4,2);

  call_mon1=atol(tmp);

  mon_mod=(call_mon%3)?(call_mon%3):3;

  if(mon_mod>=offset1 && mon_mod<=(offset1+duration1-1))

  {

      strncpy(off_time1,begin_time,8);

      off_time1[8]='\0';

      sprintf(off_time1,"%8s%6s",off_time1,offset2);

      timeAdd(off_time1,duration2*3600,off_time2);

      timeAdd(begin_time,call_duration,end_time);

      strncpy(tmp,begin_time+6,2);

      begin_day=atol(tmp);

      strncpy(tmp,end_time+6,2);

      end_day=atol(tmp);

      if(secondsBetween(begin_time,off_time1)>0)

      {

          if(secondsBetween(begin_time,off_time1)<call_duration)

              return 1;

          return 0;

       }

        

        

      if(secondsBetween(begin_time,off_time1)<0)

      {

          if(secondsBetween(begin_time,off_time2)>0)

              return 1;

          if(secondsBetween(begin_time,off_time2)<=0)

          {

         	  timeAdd(off_time1,24*3600,tmp_time);

                  if(secondsBetween(begin_time,tmp_time)<call_duration)

                     return 1;

                   return 0;

          }

          

       }

       return 1;

  }

  if(mon_mod<offset1)

  {

        tmp_mon=call_mon+offset1-mon_mod;

  	strncpy(off_time1,begin_time,4);

  	off_time1[4]='\0';

  	sprintf(off_time1,"%4s%.2d%2s%6s",off_time1,tmp_mon,"01",offset2);

  	/*timeAdd(off_time1,duration2*3600,off_time2);*/

  	if(secondsBetween(begin_time,off_time1)<call_duration)

  	    return 1;

  	return 0;

  }

  if(mon_mod>(offset1+duration1-1))

  {

  	tmp_mon=call_mon+mon_mod-offset1-duration1+1;

  	if(tmp_mon>12)

  	    sprintf(off_time1,"%d%.2d%2s%6s",call_year+1,tmp_mon,"01",offset2);

  	else

  	{

  	    strncpy(off_time1,begin_time,4);

  	    off_time1[4]='\0';

  	    sprintf(off_time1,"%4s%.2d%2s%6s",off_time1,tmp_mon,"01",offset2);

  	}

  	if(secondsBetween(begin_time,off_time1)<call_duration)

  	    return 1;

  	return 0;

  }

  return 0;

}





/* time unit is year */

int yearBetween(int offset1,int duration1,const char* offset2,int duration2,const char* begin_time,int call_duration)

{

      int begin_day=0,end_day=0,call_year=0,call_mon=0,call1,call2;

      int seconds_cycle=0,month_day=0;

      char tmp[5],end_time[15],off_time1[15],off_time2[15],tmp_time[15];

      tmp[0]='\0';

      end_time[0]='\0';

      off_time1[0]='\0';

      off_time2[0]='\0';

      tmp_time[0]='\0';

      /* off_time1 is the begin time of discount,off_time2 is the end time of discount */

      strncpy(tmp,begin_time,4);

      call_year=atol(tmp);

      strcpy(tmp,"    ");

      tmp[0]='\0';

      strncpy(tmp,begin_time+4,2);

      call_mon=atol(tmp);

      if(call_mon<offset1)

      {

      	 strncpy(off_time1,begin_time,4);

      	 off_time1[4]='\0';

      	 sprintf(off_time1,"%4s%.2d%2s%6s",off_time1,offset1,"01",offset2);

      	       

     	 if(secondsBetween(begin_time,off_time1)<call_duration)

            return 1;

         return 0;

       }

       if(call_mon>(offset1+duration2-1))

       {

       	   sprintf(off_time1,"%d%d%2s%6s",call_year+1,offset1,"01",offset2);

       	   if(secondsBetween(begin_time,off_time1)<call_duration)

       	       return 1;

       	   return 0;

       	}

       	if(call_mon>=offset1 && call_mon<=(offset1+duration1-1))

       	{

            strncpy(off_time1,begin_time,8);

            off_time1[8]='\0';

            strcat(off_time1,offset2);

            off_time1[14]='\0';

            if(secondsBetween(begin_time,off_time1)>0)

            {

            	if(secondsBetween(begin_time,off_time1)<call_duration)

            	    return 1;

            	return 0;

            }

            if(secondsBetween(begin_time,off_time1)<0)

            {

            	if(secondsBetween(off_time1,begin_time)<duration2*3600)

            	    return 1;

            	 timeAdd(off_time1,24*3600,off_time2);

            	 if(secondsBetween(begin_time,off_time2)<call_duration)

            	     return 1;

            	 return 0;

             }

             else

                 return 1;

          }

          return 0;

      

}





/* time unit is half year */

int halfyearBetween(int offset1,int duration1,const char* offset2,int duration2,const char* begin_time,int call_duration)

{

      int begin_day=0,end_day=0,call_year=0,call_mon=0,tmp_mon;

      int seconds_cycle=0,month_day=0,mon_mod=0;

      char tmp[5],end_time[15],off_time1[15],off_time2[15],tmp_time[15];

      tmp[0]='\0';

      end_time[0]='\0';

      off_time1[0]='\0';

      off_time2[0]='\0';

      tmp_time[0]='\0';

      /* off_time1 is the begin time of discount,off_time2 is the end time of discount */

      

      strncpy(tmp,begin_time,4);

      call_year=atol(tmp);

      strcpy(tmp,"    ");

      tmp[0]='\0';

      strncpy(tmp,begin_time+4,2);

      call_mon=atol(tmp);

      mon_mod=(call_mon%6)?(call_mon%6):6;

      if(mon_mod<offset1)

      {

      	  tmp_mon=offset1-mon_mod;

      	  sprintf(off_time1,"%d%.2d%2s%6s",call_year,call_mon+tmp_mon,"01",offset2);

      	  if(secondsBetween(begin_time,off_time1)<call_duration)

      	      return 1;

      	  return 0;

      }

      if(mon_mod>=offset1 && mon_mod<=(offset1+duration1-1))

      {

          strncpy(off_time1,begin_time,8);

          off_time1[8]='\0';

          sprintf(off_time1,"%8s%6s",off_time1,offset2);

          timeAdd(off_time1,duration2*3600,off_time2);

          timeAdd(begin_time,call_duration,end_time);

         

          if(secondsBetween(begin_time,off_time1)>0)

          {

              if(secondsBetween(begin_time,off_time1)<call_duration)

                  return 1;

              return 0;

          }

        

        

          if(secondsBetween(begin_time,off_time1)<0)

          {

              if(secondsBetween(begin_time,off_time2)>0)

                  return 1;

              if(secondsBetween(begin_time,off_time2)<=0)

              { 

         	  timeAdd(off_time1,24*3600,tmp_time);

                  if(secondsBetween(begin_time,tmp_time)<call_duration)

                     return 1;

                   return 0;

              }

          

          }

          return 1;

      }

  

  if(mon_mod>(offset1+duration1-1))

  {

  	tmp_mon=call_mon+mon_mod-offset1-duration1+1;

  	if(tmp_mon>12)

  	    sprintf(off_time1,"%d%.2d%2s%6s",call_year+1,offset1,"01",offset2);

  	else

  	{

  	    strncpy(off_time1,begin_time,4);

  	    off_time1[4]='\0';

  	    sprintf(off_time1,"%4s%.2d%2s%6s",off_time1,call_mon+tmp_mon,"01",offset2);

  	}

  	if(secondsBetween(begin_time,off_time1)<call_duration)

  	    return 1;

  	return 0;

  }

  return 0;      

}



/* a common calculate function */

int timeBetween(const char* unit,int offset1,int duration1,const char* offset2,int  duration2,const char* begin_time,int call_duration)

{

    char temp[5],hms1[7],new_time[15],new_time1[15],new_time2[15],end_time[15];

    int t_type,call_year,call_mon,call_day,call_hour,call_min,call_sec;

    int type_hour,type_min,type_sec,result,_week;

    int sec_bet=0;

    temp[0]='\0';

    hms1[0]='\0';

    strncpy(temp,begin_time,4);

    call_year=atol(temp);

    memset(temp,'\0',5);

    strncpy(temp,begin_time+4,2);

    call_mon=atol(temp);

    strncpy(temp,begin_time+6,2);

    call_day=atol(temp);

    strncpy(temp,begin_time+8,2);

    call_hour=atol(temp);

    strncpy(temp,begin_time+10,2);

    call_min=atol(temp);

    strncpy(temp,begin_time+12,2);

    call_sec=atol(temp);

    strncpy(hms1,begin_time+8,6);

    strncpy(temp,offset2,2);

    type_hour=atol(temp);

    strncpy(temp,offset2+2,2);

    type_min=atol(temp);

    strncpy(temp,offset2+4,2);

    type_sec=atol(temp);

    t_type=atol(unit);

    result=0;

    switch(t_type){

    case 1:  /* time unit is hour */

       /*timeCount(begin_time,&call_duration,offset2,&duration2);*/

       strncpy(new_time,begin_time,8);

       strncat(new_time,offset2,6);

       sec_bet=secondsBetween(begin_time,new_time);

       if(sec_bet>=0)

       {

           if(sec_bet<call_duration)

           {

               result=1;

               break;

           }

       }

       else

       {

           if( (sec_bet+duration2*3600) >0 )

           {

                result=1;

       	       	break;

       	   }

       	   else

       	   {

       	   	if(call_duration>24*3600+sec_bet)

       	   	{

       	   	    result=1;

       	   	    break;

       	   	}

       	   }	

       }

       break;

        

    case 2:  /* time unit is day  */

       new_time[0]='\0';

       sprintf(new_time,"%8s%6s",begin_time,offset1,offset2);  

       

       sec_bet=secondsBetween(begin_time,new_time);

       if(sec_bet>=0)

       {

            if(sec_bet<call_duration)

                 result=1;

            

       }

       else

       {

            if( (sec_bet+duration2*3600) >0 )

                result=1;

       		       	   	

       }

       /*if(call_day<=offset1)

       {  

       	   new_time[0]='\0';

           sprintf(new_time,"%6s%.2d%6s",begin_time,offset1,offset2);  

       

       	   sec_bet=secondsBetween(begin_time,new_time);

           if(sec_bet>=0)

           {

              if(sec_bet<call_duration)

              {

                 result=1;

                 break;

              }

           }

           else

           {

               if( (sec_bet+duration2*3600) >0 )

               {

                   result=1;

       	       	   break;

       	       }       	   	

           }

       }

       else if(call_day>offset1)

       {

       	   if(call_day<=offset1+duration1-1)

       	   {

       	   	 strncpy(new_time,begin_time,8);

                 strncat(new_time,offset2,6);

                                

       	         sec_bet=secondsBetween(begin_time,new_time);

                 if(sec_bet>=0)

                 {

                    if(sec_bet<call_duration)

                    {

                       result=1;

                       break;

                    }

                 }

                 else

                 {

                    if( (sec_bet+duration2*3600) >0 )

                    {

                       result=1;

       	       	       break;

       	            }       	   	

                } 

       	   }

       }*/

       break;

    case 4:  /* time unit is ten days  */

       break;

    case 3:  /* time unit is week  */

       dateToWeek(begin_time,&_week);       

       if(_week<=offset1)

       {

       	   strncpy(new_time,begin_time,8);

           strncat(new_time,offset2,6);

       	   sec_bet=secondsBetween(begin_time,new_time);

       	   if(sec_bet>=0)

       	   {

       	       if(sec_bet<call_duration)

       	       {

       	          result=1;

       	          break;

       	       }

       	   }

       	   else

       	   {

       	       if( (sec_bet+duration2*3600) >0 )

       	       {

       	       	   result=1;

       	       	   break;

       	       }

       	   	

       	   }       	   

        }   

        else if(_week>offset1)

        {

            if(_week<=offset1+duration1-1)

            {

 	       strncpy(new_time,begin_time,8);

               strncat(new_time,offset2,6);

       	       sec_bet=secondsBetween(begin_time,new_time);

       	       if(sec_bet>=0)

       	       {

       	          if(sec_bet<call_duration)

       	          {

       	            result=1;

       	            break;

       	          }

       	      }

       	      else

       	      {

       	         if( (sec_bet+duration2*3600) >0 )

       	         {

       	       	     result=1;

       	       	     break;

       	         }       	   	

       	      }

       	   }

       	          	   

	}

        

       break;

    case 5:  /* time unit is half month  */

       break;



    case 6:  /* time unit is month  */

       /*return(monthBetween(offset1,duration1,offset2,duration2,begin_time,call_duration));*/

       if(call_mon==offset1)

       {  

       	   new_time[0]='\0';

           strncpy(new_time,begin_time,8);

           strncat(new_time,offset2,6);

       	   sec_bet=secondsBetween(begin_time,new_time);

           if(sec_bet>=0)

           {

              if(sec_bet<call_duration)

              {

                 result=1;

                 break;

              }

           }

           else

           {

               if( (sec_bet+duration2*3600) >0 )

               {

                   result=1;

       	       	   break;

       	       }       	   	

           }

       }

       else if(call_mon>offset1)

       {

       	   if(call_mon<=offset1+duration1-1)

       	   {

       	   	 strncpy(new_time,begin_time,8);

                 strncat(new_time,offset2,6);

       	         sec_bet=secondsBetween(begin_time,new_time);

                 if(sec_bet>=0)

                 {

                    if(sec_bet<call_duration)

                    {

                       result=1;

                       break;

                    }

                 }

                 else

                 {

                    if( (sec_bet+duration2*3600) >0 )

                    {

                       result=1;

       	       	       break;

       	            }       	   	

                } 

       	   }

       }

       break;

    case 7:  /* time unit is season */

       return (seasonBetween(offset1,duration1,offset2,duration2,begin_time,call_duration));

       break;

    case 8:  /* time unit is half year */

        return (halfyearBetween(offset1,duration1,offset2,duration2,begin_time,call_duration));

        break;

    case 9:  /* time unit is year */

       return (yearBetween(offset1,duration1,offset2,duration2,begin_time,call_duration));

       break;

    default:  /* other is undefined */

       return 0;

    

   } 

   return result;

}

             

/*  timer_t to string datetime 'yyyymmddhh24miss' */

void toDateTime(time_t seconds,char * dateTime,short typeHint)

{

    

    

    struct tm *tblock;

    

    tblock=NULL;

    tblock = localtime(&seconds);

    

    sprintf(dateTime,"%d%.2d%.2d%.2d%.2d%.2d",tblock->tm_year+1900+typeHint,tblock->tm_mon+1,

            tblock->tm_mday,tblock->tm_hour,tblock->tm_min,tblock->tm_sec);

    dateTime[14]='\0';        

    /*

    _dateTime[0]='\0';

    strcpya(_dateTime,"19940101000000\0");

    timeAdd(dateTime,seconds,dateTime);

    timeAdd(_dateTime,seconds,dateTime);	

    _dateTime[14]='\0';

    */

    return;

}





int isStringCode(const char* v_code,int v_flag)

{

  

   char _tmp; 

   int i;

   /* get the string length */

   int length=strlen(v_code);

   

   /* judge the every element whether it is between '0'-'9' or 'A'-'Z' or 'a'-'z'. 

    * if one element is not proper,return 0. */

   for(i=0;i<length;i++)

   {

	_tmp=*(v_code+i);

	if(v_flag==ALL_DIGITAL)

         {      

		if(!((_tmp>='0')&&(_tmp<='9')))

			return 0;

         }

        else{

        if(v_flag==ALL_LETTER)

        {

		if(!(((_tmp>='A')&&(_tmp<='Z'))||((_tmp>='a')&&(_tmp<='z'))))

			return 0;

        }

        else

        {

        if(v_flag==ALL_LETTER|ALL_DIGITAL)

	{

        	if(!(((_tmp>='0')&&(_tmp<='9'))||((_tmp>='A')&&(_tmp<='Z'))||((_tmp>='a')&&(_tmp<='z'))))

        		return 0;

        }

        else

          return -1;

        }

        }

           

}

   /* all elements are proper, return 1. */

   return 1;

}



/* authenticate the user */

int authenticate( const char* session_name)

{

   int session_key1,session_key2;

    unsigned short key=47645;

    char decode[13];

    /*f_type1_decrypt((unsigned char *)&decode,(unsigned char*)session_name,9,&key);*/



   memcpy(&session_key1,decode,4);

   memcpy(&session_key2,decode+4,4);

   if(session_key1==session_key2)

       return 1;

   return 0;

}



/* calculate the discount result , the calculattion method is variable */

void methodCalculate(const char* method,int quantity ,int in_value,int* out_value)

{

    int in=0,out=0;

    in=in_value;

    switch(atol(method))

    {

       case 10:

         out=in*quantity/100*(-1);

         break;

       case 20:

         out=quantity-in;

         if(out>0)

            out=0;

         break;

       case 30:

         out=quantity*(-1);

         if(quantity>=in_value)

	    out=(-1)*in_value;

         break;

      default:

         out=0;

         break;

  }

  *out_value=out;

  return;

}

       

void stringTrim(char *str)

{

   int i=0,j=0,k=0,pos=0;

   int len=0;

   char tmp;

   len=strlen(str);

   for(i=0;i<len;i++)

   {

      tmp=*(str+i);

      if(tmp==' ')

      {

	 pos=i;

	 if(pos==len-1)

	 {

	    *(str+pos)='\0';

            return;

         }

         for(j=i+1;j<len;j++)

         {

            tmp=*(str+j);

            if(tmp==' ')

               k=1;

            else

            {

               k=0;

	       break;

            }

         }

         if(k==0)

            continue;

         else

	 {

	    *(str+pos)='\0';

            return;

         }

      }

   }

   return;

}

void stringLTrim(char *str)

{

   int i=0,j=0,k=0,pos=0;

   int len=0;

   char tmp;

   len=strlen(str);

   for(i=0;i<len;i++)

   {

      tmp=*(str+i);

      if(tmp==' ')

      {

	 pos=i;

         for(j=i+1;j<len;j++)

         {

            tmp=*(str+j);

            if(tmp==' ')

               ;

            else

            {

               k=j;

	       break;

            }

         }

        }

        else

            break;

     }

     if(k>0)

     {

         for(i=0;i<len;i++)

         {

            if(i+k==len)

            {

                *(str+i)='\0';

                break;

            }

            

     	    *(str+i)=*(str+i+k);

     	 }

                

     }

   return;

}

void stringRTrim(char *str)

{

   int i=0,j=0,k=0,pos=0;

   int len=0;

   char tmp;

   len=strlen(str);

   for(i=0;i<len;i++)

   {

      tmp=*(str+i);

      if(tmp==' ')

      {

	 pos=i;

	 if(pos==len-1)

	 {

	    *(str+pos)='\0';

            return;

         }

         for(j=i+1;j<len;j++)

         {

            tmp=*(str+j);

            if(tmp==' ')

               k=1;

            else

            {

               k=0;

	       break;

            }

         }

         if(k==0)

            continue;

         else

	 {

	    *(str+pos)='\0';

            return;

         }

      }

   }

   return;

}



void stringAllTrim(char *str)

{

    stringRTrim(str);

    stringLTrim(str);

    return;

}

/* judge how many months to calculate the rent */

int monthRent(const char *time_unit,int count,const char* start_date,const char* end_date)

{

  char tmp[3];

  int day=0,result=0,day1=0;

  tmp[0]='\0';

  strncpy(tmp,end_date+6,2);

  day=atol(tmp);

  strncpy(tmp,start_date+6,2);

  day1=atol(tmp);

  switch(atol(time_unit)){

    case 1:  /* time unit is hour */

       

    case 2:  /* time unit is day  */

         result=0;

         break;

    case 4:  /* time unit is ten days  */

       if(count%3)

       {

       	  if(day>20)

       	      result=count/3+1;

       	  else

       	  {

       	      if(day1<20)

       	      {

       	          if(count%2)

       	             result=count/3;

       	          else

       	             result=count/3+1;

       	      }

       	  }

       }

       else

       	   result=count/3;

       break;

    case 3:  /* time unit is week  */

       if(count%2)

       {

       	  if(count==1)

       	  {

              if(day>=23)

              {

                  /*if(day1>=23)

                     result=(count*7)/30;

                  else

                     result=(count*7)/30+1;*/

                  result=(count*7)/30+1;

              }

              else

              {

       	          /*if(day1<23)

       	             result=(count*7)/30+1;

       	          else

                     result=(count*7)/30;*/

                  result=(count*7)/30;

              }

          }

          else

          {

             if(day>=23)

             {

                if(day1>=23)

                   result=(count*7)/30;

                else

                   result=(count*7)/30+1;

             }

             else

             {

       	         if(day1<23)

       	            result=(count*7)/30+1;

       	         else

                    result=(count*7)/30;

             }          

          }

          

        }

        else

        {

          if(day>=15)

             result=(count*7)/30+1;

          else

          {

             if(day1<15)

                result=(count*7)/30+1;

             else

                result=(count*7)/30;

          }

          

        }     

       break;

    case 5:  /* time unit is half month  */

       if(count%2)

       {

       	  if(day>=15)

       	     result=count/2+1;

       	  else

       	     result=count/2;

       	}

       	else

       	  result=count/2;

       break;



    case 6:  /* time unit is month  */

       result=count;

       break;

    case 7:  /* time unit is season */

       result=count*3;

       break;

    case 8:  /* time unit is half year */

        result=count*6;

        break;

    case 9:  /* time unit is year */

       result=count*12;

       break;

    default:  /* other is undefined */

       result=0;

       break;

  }

  return result;

}



/* get all LBS system parameters from config file */



void getLBSConfig(char* result,const char* flag,int *status)

{

    FILE *fp;

    char* fname;

    char content[128]; 
    int _flag=-1,_add=0;
#ifndef WIN32
    fname=getenv("LBSConfig");
#else
	char szConfPath[256];
	strcpy(szConfPath,"c:\\LBSConfig");
	fname = (char*)szConfPath;
#endif

    

    *status=-1;

    

    if(fname==NULL)

       return;

    fp=fopen(fname,"r");

    if(fp==NULL)

       	return;



    fscanf(fp,"%s",content);

    while(!feof(fp))

    {

        if(content[0]=='[')

        {

            fscanf(fp,"%s",content);

            

            if(strncmp(content,flag,strlen(flag))==0)

            {

                _flag=1;      	

                fscanf(fp,"%s",content);

                break;

            }

         }

        fscanf(fp,"%s",content);

     }



     fclose(fp);

     if(content[0]=='/')

     {

     	stringTrim(content);

     	_add=strlen(content);

     	

     	if(content[_add-1]!='/')

     	{

     	    content[_add]='/';

     	    content[_add+1]='\0';

     	}

      }

      strcpy(result,content);

      result[strlen(result)]='\0';

      *status=_flag;

      

      return;

}



/*读取联机采集计费接口配置信息*/

void getDCMConfig(char* result,const char* flag,int *status)

{

    FILE *fp;

    char* fname;

    char content[128];

    int _flag=-1,_add=0;

    fname=getenv("DCMConfig");

    

    *status=-1;

    

    if(fname==NULL)

       return;

    fp=fopen(fname,"r");

    if(fp==NULL)

       	return;



    fscanf(fp,"%s",content);

    while(!feof(fp))

    {

        if(content[0]=='[')

        {

            fscanf(fp,"%s",content);

            

            if(strncmp(content,flag,strlen(flag))==0)

            {

                _flag=1;      	

                fscanf(fp,"%s",content);

                break;

            }

         }

        fscanf(fp,"%s",content);

     }



     fclose(fp);

     if(content[0]=='/')

     {

     	stringTrim(content);

     	_add=strlen(content);

     	

     	if(content[_add-1]!='/')

     	{

     	    content[_add]='/';

     	    content[_add+1]='\0';

     	}

      }

      strcpy(result,content);

      result[strlen(result)]='\0';

      *status=_flag;

      

      return;

}



/* get the last day of a month */

/*void lastDayOfMonth(int &month,int &day)

{

  if((month==1)||(month==3)||(month==5)||(month==7)||(month==8)||(month==10)||(month==12))

         return;

  if((month==4)||(month==6)||(month==9)||(month==11))

  {

  	if(day>30)

  	{

           day=1;

           month++;

           return;

        }

  }

  else

  {

     if((year%4==0&&year%100!=0)||(year%400==0))

     {

     	  if(day>29)

          {

              day=day-29;

              month++;

              return;

           }

     }

     else

     {

          if(day>29)

          {

              day=day-29;

              month++;

              return;

           }

     }

  }	

}

*/





/* get file name from path */

void getFileName(char *path,char *file_name)

{

    char sep[]="/";

    char* token;

    token=strtok(path,sep);

    while(token!=NULL)

    {

    	strcpy(file_name,token);

    	token=strtok(NULL,sep);

    }

    return;

}	    



/*judge whether the char arry is time format */

int isTime(const char*time)

{

   char tmp[5];

   int year=0,mon=0,day=0;

   int len=strlen(time);

   tmp[0]='\0';

   if(len==8||len==14)

       ;

   else

       return 0;

   if(!isStringCode(time,ALL_DIGITAL))

       return 0;  

   if( strncmp(time,"1970",4)>=0 && strncmp(time,"9999",4)<=0 )

       ;

   else

       return 0;

   if(strncmp(time+4,"01",2)>=0 && strncmp(time+4,"12",2)<=0)

       ;

   else

       return 0;

   if(strncmp(time+6,"01",2)>=0 && strncmp(time+6,"31",2)<=0)

       ;

   else

       return 0;

   strncpy(tmp,time,4);

   year=atol(tmp);

   memset(tmp,'\0',5);

   strncpy(tmp,time+4,2);

   mon=atol(tmp);

   tmp[0]='\0';

   strncpy(tmp,time+6,2);

   day=atol(tmp);

   

   if((mon==4)||(mon==6)||(mon==9)||(mon==11))

      if(day>30)

         return 0;   

   if(mon==2)

   {

      if((year%4==0&&year%100!=0)||(year%400==0))

      {

      	if(day>29)

      	    return 0;

      }      

      else

      {

         if(day>29)

             return 0;

      }

   }

   

   if(len==8)

       return 1;

   if(strncmp(time+8,"00",2)>=0 && strncmp(time+8,"23",2)<=0)

       ;

   else

       return 0;

   if(strncmp(time+10,"00",2)>=0 && strncmp(time+10,"59",2)<=0)

       ;

   else

       return 0;

   if(strncmp(time+12,"00",2)>=0 && strncmp(time+12,"59",2)<=0)

       ;

   else

       return 0;

   return 1;

}

/*  get application num  */

int findPidByName(const char * name)

{

   FILE *fp;

   int num=0;

   char comm[128];

   comm[0]='\0';

   sprintf(comm,"ps -ef|grep -v 'grep'|grep -c %s > on.lst\0\0",name);

   if(system(comm)<0)

      return -1;

   fp=fopen("on.lst","rb");

   if(fp==NULL)

      return -1;

   fscanf(fp,"%d",&num);

   fclose(fp);

   remove("on.lst");

   if(num>0)

     return 1;

   else

     return 0;

	

}

   

/* write control file head to sql*Load */

int writeHead(FILE *fp)

{

 

   char comm[255];

   comm[0]='\0';

   strcpy(comm,"load data\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',255);

   strcpy(comm,"infile *\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',255);

   strcpy(comm,"append into  table acct_item\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',255);

   strcpy(comm,"fields terminated by \",\"\n");

   /*strcpy(comm,"fields terminated by \",\" optionally enclosed by '\"'\n");*/

   if(fwrite(comm,strlen(comm),1,fp)<=0)

       return 0;

   memset(comm,'\0',255);

   strcpy(comm,"(ACCT_ITEM_ID,SERV_ID, SERV_SEQ_NBR,ACCT_ID,ACCT_SEQ_NBR,ACCT_ITEM_TYPE_ID,CHARGE,BILLING_CYCLE_ID,created_date date \"yyyymmdd\",PARTNER_ID,STATE,state_date date \"yyyymmdd\" )\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',255);

   strcpy(comm,"begindata\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   return 1;

}

/*---------*/





int writeCall(FILE *fp,short cur_month)

{

    char comm[255];

   comm[0]='\0';

   strcpy(comm,"load data\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',255);

   strcpy(comm,"infile *\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',255);

   sprintf(comm,"append into table call_ticket partition (CT%.2d)\n",(cur_month>6)?(cur_month-6):cur_month );

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',255);

   strcpy(comm,"fields terminated by \",\"\n");

   /*strcpy(comm,"fields terminated by \",\" optionally enclosed by '\"'\n");*/

   if(fwrite(comm,strlen(comm),1,fp)<=0)

       return 0;

   memset(comm,'\0',255);

   strcpy(comm,"(TICKET_ID,TICKET_TYPE,CALLING_AREA_CODE,CALLING_NBR,CALLED_AREA_CODE,CALLED_NBR,START_TIME date \"yyyymmddhh24miss\",DURATION,SOURCE_ID,DIRECTION,PARTNER_ID,TRUNK_ID,TRUNK_SEQ_NBR,STATE,CREATED_DATE date \"yyyymmddhh24miss\",END_TIME date \"yyyymmddhh24miss\",COUNTS,CUR_MONTH)\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',255);

   strcpy(comm,"begindata\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   return 1;

}



/* 写 CALL_TICKET 控制文件，包括SERV_ID、费用等 */

int writeCall2(FILE *fp,short cur_month)

{

    char comm[1024];

   comm[0]='\0';

   strcpy(comm,"load data\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',1024);

   strcpy(comm,"infile *\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',1024);

   sprintf(comm,"append into table call_ticket partition (CT%.2d)\n",(cur_month>6)?(cur_month-6):cur_month );

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',1024);

   strcpy(comm,"fields terminated by \",\"\n");

   /*strcpy(comm,"fields terminated by \",\" optionally enclosed by '\"'\n");*/

   if(fwrite(comm,strlen(comm),1,fp)<=0)

       return 0;

   memset(comm,'\0',1024);

   strcpy(comm,"(TICKET_ID,TICKET_TYPE,CALLING_AREA_CODE,CALLING_NBR,CALLED_AREA_CODE,CALLED_NBR,START_TIME date \"yyyymmddhh24miss\",DURATION,SOURCE_ID,DIRECTION,PARTNER_ID,TRUNK_ID,TRUNK_SEQ_NBR,STATE,CREATED_DATE date \"yyyymmddhh24miss\",END_TIME date \"yyyymmddhh24miss\",COUNTS,CUR_MONTH,ACCT_ITEM_TYPE_ID,CHARGE,DISCT_CHARGE,SERV_ID,SUM_CHARGE_ID)\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',1024);

   strcpy(comm,"begindata\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   return 1;

}





int writeCharge(FILE *fp,short cur_month)

{

   char comm[255];

   comm[0]='\0';

   strcpy(comm,"load data\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',255);

   strcpy(comm,"infile *\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',255);

   /*strcpy(comm,"append into table usage_charge\n");*/

   sprintf(comm,"append into table usage_charge partition (UC%.2d)\n",(cur_month>6)?(cur_month-6):cur_month );

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',255);

   strcpy(comm,"fields terminated by \",\"\n");

   /*strcpy(comm,"fields terminated by \",\" optionally enclosed by '\"'\n");*/

   if(fwrite(comm,strlen(comm),1,fp)<=0)

       return 0;

   memset(comm,'\0',255);

   strcpy(comm,"(USAGE_CHARGE_ID,TICKET_ID,ACCT_ITEM_TYPE_ID,TARIFF_TYPE,CHARGE,STATE,CREATED_TIME date \"yyyymmddhh24miss\",CALLING_NBR,CALLING_AREA_CODE,ORI_CHARGE,ADDON,CUR_MONTH)\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',255);

   strcpy(comm,"begindata\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   return 1;

}



int writeChargebk(FILE *fp,short cur_month)

{

   char comm[255];

   comm[0]='\0';

   strcpy(comm,"load data\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',255);

   strcpy(comm,"infile *\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',255);

   /*strcpy(comm,"append into table usage_charge\n");*/

   sprintf(comm,"append into table usage_charge partition (UC%.2d)\n",(cur_month>6)?(cur_month-6):cur_month );

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',255);

   strcpy(comm,"fields terminated by \",\"\n");

   /*strcpy(comm,"fields terminated by \",\" optionally enclosed by '\"'\n");*/

   if(fwrite(comm,strlen(comm),1,fp)<=0)

       return 0;

   memset(comm,'\0',255);

   strcpy(comm,"(USAGE_CHARGE_ID,TICKET_ID,METER_READING_ID,ACCT_ITEM_TYPE_ID,ACCT_ITEM_ID,TARIFF_TYPE,TARIFF_ID,CHARGE,STATE,CREATED_TIME date \"yyyymmddhh24miss\",ORI_CHARGE,ADDON,CALLING_AREA_CODE,CALLING_NBR,CUR_MONTH)\n");

      

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',255);

   strcpy(comm,"begindata\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   return 1;

}



int writeMeterbk(FILE *fp,short cur_month)

{

   char comm[255];

   comm[0]='\0';

   strcpy(comm,"load data\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',255);

   strcpy(comm,"infile *\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',255);

   /*strcpy(comm,"append into table usage_charge\n");*/

   sprintf(comm,"append into table meter_reading partition (MR%.2d)\n",(cur_month>6)?(cur_month-6):cur_month );

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',255);

   strcpy(comm,"fields terminated by \",\"\n");

   /*strcpy(comm,"fields terminated by \",\" optionally enclosed by '\"'\n");*/

   if(fwrite(comm,strlen(comm),1,fp)<=0)

       return 0;

   memset(comm,'\0',255);

   strcpy(comm,"(METER_READING_ID,CALLING_AREA_CODE,CALLING_NBR,SWITCH_ID,METER_TYPE,METER_READING,SOURCE_ID,STATE,CREATED_DATE date \"yyyymmddhh24miss\",CURRENT_READING,CUR_MONTH)\n");

      

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',255);

   strcpy(comm,"begindata\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   return 1;

}

int writeMeterbk_hf(FILE *fp,short cur_month)

{

   char comm[255];

   comm[0]='\0';

   strcpy(comm,"load data\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',255);

   strcpy(comm,"infile *\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',255);

   /*strcpy(comm,"append into table usage_charge\n");*/

   sprintf(comm,"append into table meter_reading partition (MR%.2d)\n",(cur_month>6)?(cur_month-6):cur_month );

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',255);

   strcpy(comm,"fields terminated by \",\"\n");

   /*strcpy(comm,"fields terminated by \",\" optionally enclosed by '\"'\n");*/

   if(fwrite(comm,strlen(comm),1,fp)<=0)

       return 0;

   memset(comm,'\0',255);

   strcpy(comm,"(METER_READING_ID,CALLING_AREA_CODE,CALLING_NBR,SWITCH_ID,METER_TYPE,METER_READING,SOURCE_ID,STATE,CREATED_DATE date \"yyyymmddhh24miss\",CURRENT_READING,CUR_MONTH,ACCT_ITEM_TYPE_ID,CHARGE,SERV_ID,SUM_CHARGE_ID)\n");

      

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',255);

   strcpy(comm,"begindata\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   return 1;

}



int writeCallbk(FILE *fp,short cur_month)

{

   char comm[401];

   comm[0]='\0';

   strcpy(comm,"load data\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',401);

   strcpy(comm,"infile *\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',401);

   sprintf(comm,"append into table call_ticket partition (CT%.2d)\n",(cur_month>6)?(cur_month-6):cur_month );

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',401);

   strcpy(comm,"fields terminated by \",\"\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

       return 0;

   memset(comm,'\0',401);

   strcpy(comm,"(TICKET_ID,TICKET_TYPE,CALLING_AREA_CODE,CALLING_NBR,CALLED_AREA_CODE,CALLED_NBR,START_TIME date \"yyyymmddhh24miss\",BEAR_SERV_TYPE_ID,TELE_SERV_TYPE_ID,DURATION,COUNTS,SOURCE_ID,DIRECTION,PARTNER_ID,TRUNK_ID,TRUNK_SEQ_NBR,STATE,CREATED_DATE date \"yyyymmddhh24miss\",END_TIME date \"yyyymmddhh24miss\",CUR_MONTH)\n");

      

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',401);

   strcpy(comm,"begindata\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   return 1;

}



int writeSettbk(FILE *fp,short cur_month)

{

   char comm[401];

   comm[0]='\0';

   strcpy(comm,"load data\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',401);

   strcpy(comm,"infile *\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',401);

   sprintf(comm,"append into table sett_ticket partition (ST%.2d)\n",(cur_month>6)?(cur_month-6):cur_month );

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',401);

   strcpy(comm,"fields terminated by \",\"\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

       return 0;

   memset(comm,'\0',401);

   strcpy(comm,"(SETT_ID,TICKET_TYPE,CALLING_AREA_CODE,CALLING_NBR,CALLED_AREA_CODE,CALLED_NBR,START_TIME date \"yyyymmddhh24miss\",DURATION,COUNTS,SOURCE_ID,DIRECTION,TRUNK_ID,TRUNK_SEQ_NBR,STATE,CREATED_DATE date \"yyyymmddhh24miss\",END_TIME date \"yyyymmddhh24miss\",SETT_CARRIER_ID,CHARGE,CALLING_HDR,CALLED_HDR,CUR_MONTH,TRUNK_CODE)\n");

      

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,'\0',401);

   strcpy(comm,"begindata\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   return 1;

}



extern int writeHeader(int fid)

{

   char comm[255];

   comm[0]='\0';

   strcpy(comm,"load data\n");

   if(write(fid,comm,strlen(comm))<=0)

      return 0;

   memset(comm,'\0',255);

   strcpy(comm,"infile *\n");

   if(write(fid,comm,strlen(comm))<=0)

      return 0;

   memset(comm,'\0',255);

   strcpy(comm,"append into  table acct_item\n");

   if(write(fid,comm,strlen(comm))<=0)

      return 0;

   memset(comm,'\0',255);

   strcpy(comm,"fields terminated by \",\"\n");

   /*strcpy(comm,"fields terminated by \",\" optionally enclosed by '\"'\n");*/

   if(write(fid,comm,strlen(comm))<=0)

       return 0;

   memset(comm,'\0',255);

   strcpy(comm,"(ACCT_ITEM_ID,SERV_ID, SERV_SEQ_NBR,ACCT_ID,ACCT_SEQ_NBR,ACCT_ITEM_TYPE_ID,CHARGE,BILLING_CYCLE_ID,created_date date \"yyyymmdd\",PARTNER_ID,STATE,state_date date \"yyyymmdd\" )\n");

   if(write(fid,comm,strlen(comm))<=0)

      return 0;

   memset(comm,'\0',255);

   strcpy(comm,"begindata\n");

   if(write(fid,comm,strlen(comm))<=0)

      return 0;

   return 1;	

}





int writeHeadMT(FILE *fp,short cur_month)

{

 

   char comm[255];

   comm[0]='\0';

   strcpy(comm,"load data\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,255,'\0');

   strcpy(comm,"infile *\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,255,'\0');

   sprintf(comm,"append into  table meter_reading partition (MR%.2d)\n",(cur_month>6)?(cur_month-6):cur_month);

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,255,'\0');

   strcpy(comm,"(meter_reading_id POSITION(01:10) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;    

      

   memset(comm,255,'\0');

   strcpy(comm,"calling_area_code POSITION(11:19) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;    

   memset(comm,255,'\0');

   strcpy(comm,"calling_nbr POSITION(20:35) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  



   memset(comm,255,'\0');

   strcpy(comm,"switch_id POSITION(36:39) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  

   memset(comm,255,'\0');

   strcpy(comm,"meter_type POSITION(40:41) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  

   memset(comm,255,'\0');

   strcpy(comm,"meter_reading POSITION(42:51) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  

   

   memset(comm,255,'\0');

   strcpy(comm,"source_id POSITION(52:61) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  



   memset(comm,255,'\0');

   strcpy(comm,"state POSITION(62:62) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  

   

   memset(comm,255,'\0');

   strcpy(comm,"created_date POSITION(63:76) date \"yyyymmddhh24miss\",\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

        

   memset(comm,255,'\0');

   strcpy(comm,"current_reading POSITION(77:86) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;    

   

   memset(comm,255,'\0');

   strcpy(comm,"cur_month POSITION(87:87) DECIMAL EXTERNAL)\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;     

   /*   

   memset(comm,255,'\0');

   strcpy(comm,"fields terminated by \",\" optionally enclosed by '\"'\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

       return 0;

   memset(comm,255,'\0');

   strcpy(comm,"(ACCT_ITEM_ID,SERV_ID, SERV_SEQ_NBR,ACCT_ID,ACCT_SEQ_NBR,ACCT_ITEM_TYPE_ID,CHARGE,BILLING_CYCLE_ID,created_date date \"yyyymmdd\",PARTNER_ID,STATE,state_date date \"yyyymmdd\" )\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0; */

   memset(comm,255,'\0');

   strcpy(comm,"begindata\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   return 1;

}



int writeHeadAMS(FILE *fp)

{

 

   char comm[255];

   comm[0]='\0';

   strcpy(comm,"load data\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,255,'\0');

   strcpy(comm,"infile *\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,255,'\0');

   strcpy(comm,"append into  table abnormal_meters\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,255,'\0');

   strcpy(comm,"(abnormal_meters_id POSITION(1:8) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;    

      

   memset(comm,255,'\0');

   strcpy(comm,"calling_area_code POSITION(9:17) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;    

   memset(comm,255,'\0');

   strcpy(comm,"calling_nbr POSITION(18:33) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  

   memset(comm,255,'\0');

   strcpy(comm,"meter_type POSITION(34:35) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  



   memset(comm,255,'\0');

   strcpy(comm,"source_id POSITION(36:45) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  



   memset(comm,255,'\0');

   strcpy(comm,"generate_time POSITION(46:59) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

    	return 0;  

    	

   memset(comm,255,'\0');

   strcpy(comm,"quantity POSITION(60:69) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

 	return 0;  

 	

   memset(comm,255,'\0');

   strcpy(comm,"switch_id POSITION(70:73) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  

   

   memset(comm,255,'\0');

   strcpy(comm,"state POSITION(74:76) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  

   

   memset(comm,255,'\0');

   strcpy(comm,"state_date POSITION(77:90) date \"yyyymmddhh24miss\",\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  

   

   memset(comm,255,'\0');

   strcpy(comm,"type POSITION(91:91) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  

  

   memset(comm,255,'\0');

   strcpy(comm,"current_reading POSITION(92:101) DECIMAL EXTERNAL)\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;    

 

   memset(comm,255,'\0');

   strcpy(comm,"begindata\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   return 1;

}



int writeHeadUC(FILE *fp,short cur_month)

{



   char comm[255];

   comm[0]='\0';

   strcpy(comm,"load data\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,255,'\0');

   strcpy(comm,"infile *\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,255,'\0');

   /*strcpy(comm,"append into table usage_charge\n");*/

   sprintf(comm,"append into  table usage_charge partition (UC%.2d)\n",(cur_month>6)?(cur_month-6):cur_month);   

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,255,'\0');

   strcpy(comm,"(usage_charge_id POSITION(1:10) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;    

   

   memset(comm,255,'\0');

   strcpy(comm,"meter_reading_id POSITION(11:20) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  

   memset(comm,255,'\0');

   strcpy(comm,"acct_item_type_id POSITION(21:26) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  



   memset(comm,255,'\0');

   strcpy(comm,"tariff_type POSITION(27:28) CHAR,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  



   memset(comm,255,'\0');

   strcpy(comm,"tariff_id POSITION(29:34) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

    	return 0;  

    	

   memset(comm,255,'\0');

   strcpy(comm,"charge POSITION(35:44) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

 	return 0;  

 	

   memset(comm,255,'\0');

   strcpy(comm,"state POSITION(45:47) CHAR,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  

   

   memset(comm,255,'\0');

   strcpy(comm,"created_time POSITION(48:61) date \"yyyymmddhh24miss\",\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  

   

   memset(comm,255,'\0');

   strcpy(comm,"ori_charge POSITION(62:71) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  

   

   memset(comm,255,'\0');

   strcpy(comm,"calling_area_code POSITION(72:80) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0; 

      

   memset(comm,255,'\0');

   strcpy(comm,"calling_nbr POSITION(81:96) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  

   

   memset(comm,255,'\0');

   strcpy(comm,"cur_month POSITION(97:97) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  

   

   memset(comm,255,'\0');

   strcpy(comm,"addon POSITION(98:107) DECIMAL EXTERNAL)\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  

         

   memset(comm,255,'\0');

   strcpy(comm,"begindata\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   

   return 1;

	

}



int writeHeadST(FILE *fp,short cur_month)

{



   char comm[255];

   comm[0]='\0';

   strcpy(comm,"load data\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,255,'\0');

   strcpy(comm,"infile *\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,255,'\0');

   /*strcpy(comm,"append into table sett_ticket\n");*/

   sprintf(comm,"append into table sett_ticket partition (ST%.2d)\n",(cur_month>6)?(cur_month-6):cur_month );

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   memset(comm,255,'\0');

   strcpy(comm,"(sett_id POSITION(1:10) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;    

   

   memset(comm,255,'\0');

   strcpy(comm,"ticket_type POSITION(11:12) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  

   memset(comm,255,'\0');

   strcpy(comm,"calling_area_code POSITION(13:21) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  



   memset(comm,255,'\0');

   strcpy(comm,"calling_nbr POSITION(22:37) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  



   memset(comm,255,'\0');

   strcpy(comm,"called_area_code POSITION(38:46) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

    	return 0;  

    	

   memset(comm,255,'\0');

   strcpy(comm,"called_nbr POSITION(47:66) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

 	return 0;  

 	

   memset(comm,255,'\0');

   strcpy(comm,"start_time POSITION(67:80) date \"yyyymmddhh24miss\",\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  

   

   memset(comm,255,'\0');

   strcpy(comm,"duration POSITION(81:86) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  

   

   memset(comm,255,'\0');

   strcpy(comm,"counts POSITION(87:92) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0; 

      

   memset(comm,255,'\0');

   strcpy(comm,"source_id POSITION(93:102) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  

   

   memset(comm,255,'\0');

   strcpy(comm,"direction POSITION(103:103) CHAR,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  

   

   memset(comm,255,'\0');

   strcpy(comm,"trunk_id POSITION(104:109) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  

   

   memset(comm,255,'\0');

   strcpy(comm,"trunk_seq_nbr POSITION(110:112) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  

      

   memset(comm,255,'\0');

   strcpy(comm,"state POSITION(113:115) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  

   

   memset(comm,255,'\0');

   strcpy(comm,"created_date POSITION(116:129) date \"yyyymmddhh24miss\",\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  

   

   memset(comm,255,'\0');

   strcpy(comm,"end_time POSITION(130:143) date \"yyyymmddhh24miss\",\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  

      

   memset(comm,255,'\0');

   strcpy(comm,"charge POSITION(144:153) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   

   memset(comm,255,'\0');

   strcpy(comm,"sett_carrier_id POSITION(154:157) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  

   

   memset(comm,255,'\0');

   strcpy(comm,"calling_hdr POSITION(158:159) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;   

      

   memset(comm,255,'\0');

   strcpy(comm,"called_hdr POSITION(160:161) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  

      

   memset(comm,255,'\0');

   strcpy(comm,"cur_month POSITION(162:162) DECIMAL EXTERNAL,\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0; 

      

   memset(comm,255,'\0');

   strcpy(comm,"trunk_code POSITION(163:168) DECIMAL EXTERNAL)\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;  

      

   memset(comm,255,'\0');

   strcpy(comm,"begindata\n");

   if(fwrite(comm,strlen(comm),1,fp)<=0)

      return 0;

   

   return 1;

	

}



void formatDate(char *str)

{

   int i=0,j=0,len=0;

   len=strlen(str);

   for(i=0;i<len;i++)

   {

   	if( *(str+i)<'0' || *(str+i)>'9' )

   	{

   		for(j=i;j<len-1;j++)

   		  *(str+j)=*(str+j+1);

   		*(str+j)='\0';

   		len-=1;

   	}

   }

   			

}







//add by zhoufj  

//from BaseFunc.pc move into 

int findProcessByName(const char * name)

{

   FILE *fp;

   int num=0;

   char comm[128];

   comm[0]='\0';

   sprintf(comm,"ps -ef|grep -v 'grep'|grep -c %s > on.lst\0\0",name);

   //printf("comm=%s\n",comm);

   if(system(comm)<0)

      return -1;

   fp=fopen("on.lst","rb");

   if(fp==NULL)

      return -1;

   fscanf(fp,"%d",&num);

   fclose(fp);

   remove("on.lst");

   return num;

}





int iFindWithHalf(const int *v_ListNumber,const int &v_ListLen,const int &v_FindNumber)

{

	int _head=0,_tail,_mid;

	if(v_ListLen<=0)

		return -1;

		

	_tail=v_ListLen;

	while(_head<_tail)

	{

		_mid=(_head+_tail)/2;

		if(v_ListNumber[_mid]==v_FindNumber)

			return _mid;

		else if(v_ListNumber[_mid]>v_FindNumber)

			_tail=_mid-1;

		else

			_head=_mid+1;

	}

	if(	_head == _tail && v_ListNumber[_head]==v_FindNumber)

		return _head;

	else

		return -1;	

}	

 

int lFindWithHalf(const long *v_ListNumber,const long &v_ListLen,const long &v_FindNumber)

{

	int _head=0,_tail,_mid;

	if(v_ListLen<=0)

		return -1;

		

	_tail=v_ListLen;

	while(_head<_tail)

	{

		_mid=(_head+_tail)/2;

		if(v_ListNumber[_mid]==v_FindNumber)

			return _mid;

		else if(v_ListNumber[_mid]>v_FindNumber)

			_tail=_mid-1;

		else

			_head=_mid+1;

	}

	if(	_head == _tail && v_ListNumber[_head]==v_FindNumber)

		return _head;

	else

		return -1;	

}







bool ExecSqlload(const char *_UserId,const char *_ControlFile)

{

	unsigned short _Len=0;

	char	_comm[1024];

	char	_OtherFile[121];

	char	cUserId[60];

	int		i=0;

	_Len=strlen(_ControlFile);

	strncpy(_OtherFile,_ControlFile,_Len-4);

	_OtherFile[_Len-4]='\0';

	if(strlen(_UserId)==0 )
	{
        string strTmp;
        if (!PublicLib::OCS_readConfig(strTmp,"OracleString","oracle"))
		{
			return false;
		}	

        strcpy(cUserId,strTmp.c_str());
    	stringTrim(cUserId);
	}
	else
		strcpy(cUserId,_UserId);

		

	sprintf(_comm,"sqlldr userid=%s control='%s' log='%s.log' bad='%s.bad' readsize=6553600 bindsize=6553600 rows=5000 silent=feedback direct=false",cUserId,_ControlFile,_OtherFile,_OtherFile);	

	if(system(_comm)!=0)
		return false;

	return true;
}			



bool ExecSqlloadTrue(const char *_UserId,const char *_ControlFile)
{
	unsigned short _Len=0;
	char	_comm[1024];
	char	_OtherFile[121];
	char	cUserId[60];

	_Len=strlen(_ControlFile);
	strncpy(_OtherFile,_ControlFile,_Len-4);
	_OtherFile[_Len-4]='\0';

	if(strlen(_UserId)==0 )
	{
        string strTmp;
        if (!PublicLib::OCS_readConfig(strTmp,"OracleString","oracle"))
		{
			return false;
		}	

        strcpy(cUserId,strTmp.c_str());
    	stringTrim(cUserId);
	}
	else
		strcpy(cUserId,_UserId);

	
	sprintf(_comm,"sqlldr userid=%s control=%s log=%s.log bad=%s.bad readsize=6553600 bindsize=6553600 rows=5000 silent=feedback direct=true",cUserId,_ControlFile,_OtherFile,_OtherFile);	

	if(system(_comm)!=0)
		return false;

	return true;
}			





long Power(const int &v_a,const int &v_b)

{

	long	sum_values=1;

	if(v_b==0)

		return 1;

	else if(v_b>0)

	{

		for(int i=1;i<=v_b;i++)

			sum_values=sum_values*v_a;

		return sum_values;	

	}	

	else

		return -1;	

}



bool CompareChar(const char *v_a,const char *v_b,const int &v_Operators)

{

	char	tmp_b[301];

	char    tmp_a[50];

	char *psr;

	switch (v_Operators)

	{

		case 10: //=

			if(strcmp(v_a,v_b)==0)

				return true;

			else

				return false;

			

			break;	

		case 20: //!=		

			if(strcmp(v_a,v_b)!=0)

				return true;

			else

				return false;

			

			break;	

		case 30: //like		

			if(strncmp(v_a,v_b,strlen(v_b))==0)

				return true;

			else

				return false;		

			

			break;	

		case 40: //>		

			if(strcmp(v_a,v_b)>0)

				return true;

			else

				return false;

				

			break;		

		case 41: //>=		

			if(strcmp(v_a,v_b)>=0)

				return true;

			else

				return false;

				

			break;		

		case 50: //<		

			if(strcmp(v_a,v_b)<0)

				return true;

			else

				return false;

			

			break;		

		case 51: //<=		

			if(strcmp(v_a,v_b)<=0)

				return true;

			else

				return false;

			

			break;		

		case 60: //当前变量值包含在值中

			if(v_b[strlen(v_b)-1]!=',')

				sprintf(tmp_b,"%s,\0",v_b);

			else

				strcpy(tmp_b,v_b);

				

			if( 0 == strlen(v_a) )

				return false;

			

			sprintf(tmp_a,"%s,",v_a);

				

				//printf("b = [%s],a = [%s]\n",tmp_b,tmp_a);

			psr=strstr(tmp_b,tmp_a);

			if(psr==NULL)

			{

				return false;

			}

			else

			{

				if(0 != (psr - tmp_b))

				{

					sprintf(tmp_a,",%s,",v_a);

					psr=strstr(tmp_b,tmp_a);

					if(NULL == psr)

					{

						return false;

					}

					else

					{

						return true;

					}

				}

				return true;

			}

			break;	

		case 61: //当前变量值不包含在值中

			if(v_b[strlen(v_b)-1]!=',')

				sprintf(tmp_b,"%s,\0",v_b);

			else

				strcpy(tmp_b,v_b);

				

			if( 0 == strlen(v_a) )

				return true;

			

			sprintf(tmp_a,"%s,",v_a);

				

			psr=strstr(tmp_b,tmp_a);

			if(psr==NULL)

				return true;

			else

				return false;	

			

			break;					

		default:

			return false;		

	}

}	









long GetInt(const double &v_f, const char &v_Mode)

{

    long lValues = v_f; 

    if ('1' == v_Mode) //向下圆整

    {

    	return lValues;

    }	

    else if ('2' == v_Mode) // 向上圆整

    {

        if (0.000000 < (v_f - lValues))

            lValues++;

    }

    else if ('3' == v_Mode) // 四舍五入

    {

        if (0.5 <= (v_f - lValues))

            lValues++;

    }

    

	return lValues;

}



int StartProg(const char *v_Command)

{	

	char sCommand[500];

    

    if( strlen(v_Command)==0 )

    		return 0;

    

    strcpy(sCommand,v_Command);

    strcat(sCommand," &\0");

    	

	if( system(sCommand)!=0 )

		return -1;

    else

    	return 1;

}    		



void nstrcpy(char *v_TrgStr,const char *v_OriStr,const int &v_Len)

{

	if( strlen(v_OriStr)>=v_Len )

	{

		strncpy(v_TrgStr,v_OriStr,v_Len-1);

		v_TrgStr[v_Len-1]='\0';

	}

	else

	{

		strcpy(v_TrgStr,v_OriStr);

	} 

	return ;

}



	





bool CompareValues(const char *v_a,const char *v_b,const char v_VarType,const int &v_Operators)

{

	char	tmp_b[301];

	char    tmp_a[50];

	char	szExpress[31];

	char *psr;

	switch (v_Operators)

	{

		case 10: //=

			if(v_VarType=='C' ) //字符比较

			{

				if(strcmp(v_a,v_b)==0)

					return true;

				else

					return false;

			}

			else //数值比较

			{

				if(atol(v_a)==atol(v_b))

					return true;

				else

					return false;

			}

				

		case 20: //!=

		case 94:	

			if(v_VarType=='C' ) //字符比较

			{

				if(strcmp(v_a,v_b)!=0)

					return true;

				else

					return false;

			}

			else //数值比较

			{

				if(atol(v_a) != atol(v_b))

					return true;

				else

					return false;

			}		

					

		case 30: //like

			szExpress[0] = 0;

			if((psr = strchr((char *)v_b,'[')))		

			{

				strcpy(szExpress,psr );

				*psr = 0;

			}

			if(strncmp(v_a,v_b,strlen(v_b))==0)

			{

				tmp_a[0] = *(v_a + strlen(v_b));

				if((0 != tmp_a[0]) && (psr = strchr(szExpress,'[')))

				{

					//printf("va=[%s],value=[%c],express=[%s]\n",v_a,tmp_a[0],szExpress);

					if(PublicLib::JudgeExpress(tmp_a[0],szExpress) >= 0)

					{

						return true;

					}

				}else

					return true;

				

				return false;

			}

			else

				return false;		

		case 40: //>

			if(v_VarType=='C' ) //字符比较

			{	

				if(strcmp(v_a,v_b)>0)

					return true;

				else

					return false;

			}	

			else //数值比较 	

			{

				if( atol(v_a)>atol(v_b) )

					return true;

				else

					return false;

			}

			

		case 41: //>=		

			if(v_VarType=='C' ) //字符比较

			{	

				if(strcmp(v_a,v_b)>=0)

					return true;

				else

					return false;

			}	

			else //数值比较 	

			{

				if( atol(v_a) >= atol(v_b) )

					return true;

				else

					return false;

			}

		case 50: //<		

			if(v_VarType=='C' ) //字符比较

			{	

				if(strcmp(v_a,v_b)<0)

					return true;

				else

					return false;

			}	

			else //数值比较 	

			{

				if( atol(v_a)<atol(v_b) )

					return true;

				else

					return false;

			}

		case 51: //<=		

			if(v_VarType=='C' ) //字符比较

			{	

				if(strcmp(v_a,v_b) <= 0)

					return true;

				else

					return false;

			}	

			else //数值比较 	

			{

				if( atol(v_a) <= atol(v_b) )

					return true;

				else

					return false;

			}

		case 60: //当前变量值包含在值中

			if(v_b[strlen(v_b)-1]!=',')

				sprintf(tmp_b,"%s,\0",v_b);

			else

				strcpy(tmp_b,v_b);

				

			if( 0 == strlen(v_a) )

				return false;

			

			sprintf(tmp_a,"%s,",v_a);

				

				//printf("b = [%s],a = [%s]\n",tmp_b,tmp_a);

			psr=strstr(tmp_b,tmp_a);

			if(psr==NULL)

			{

				return false;

			}

			else

			{

				if(0 != (psr - tmp_b))

				{

					sprintf(tmp_a,",%s,",v_a);

					psr=strstr(tmp_b,tmp_a);

					if(NULL == psr)

					{

						return false;

					}

					else

					{

						return true;

					}

				}

				return true;

			}

			break;	

		case 61: //当前变量值不包含在值中

			if(v_b[strlen(v_b)-1]!=',')
				sprintf(tmp_b,",%s,\0",v_b);

			if( 0 == strlen(v_a) )
				return true;

			sprintf(tmp_a,",%s,",v_a);

			psr=strstr(tmp_b,tmp_a);
			if(psr==NULL)
				return true;
			else
				return false;	
			break;

		case 91://时间非法

			if( !isTime(v_a) )

				tmp_a[0]='1';

			else

				tmp_a[0]='0';	

				

			if(	tmp_a[0]==v_b[0])

				return true;

			else

				return false;

			

			break;	

		case 92://非法数字

			if( !isStringCode(v_a,ALL_DIGITAL) )

				tmp_a[0]='1';

			else

				tmp_a[0]='0';	

				

			if(	tmp_a[0]==v_b[0])

				return true;

			else

				return false;

			

			break;	

			

		case 93://非法IP

		    if( !isIpAddress(v_a)) tmp_a[0]='1';

		    else tmp_a[0]='0';

		    if(tmp_a[0]==v_b[0]) return true;

		    else return false;

									

		default:

			return false;		

	}

}	



extern bool isIpAddress(const char *v_str)

{

    char tmp_str[80];

    char part_str[10];

    int parts,tmp_offset,tmp_int;

    char *ptr;

    if(strlen(v_str)>=80) return false;

    strcpy(tmp_str,v_str);

    stringTrim(tmp_str);

    strcat(tmp_str,".");

    tmp_offset=0;

    parts=0;

    for(ptr=tmp_str;*ptr;ptr++) {

        if(*ptr=='.') {

            if(tmp_offset==0) return false;

            part_str[tmp_offset]=0;

            tmp_int = atoi(part_str);

            if(tmp_int <0 || tmp_int>255) return false;

            if(parts==0 && tmp_int==0) return false;

            tmp_offset=0;

            if((++parts)>4) return false;

            continue;

        }

        if(*ptr>'9' || *ptr<'0') return false;

        part_str[tmp_offset++] = *ptr;

        if(tmp_offset>3) return false;

    }

    if(parts!=4) return false;

    return true;

}

/******************************************************************************************
Author          : guoxd
Founction       :char *strupr(char *str)
Return          :char*
Parameter List  : 
  Param1: char *str
Description     : 将字符串全部转换为大写字符
Calls           :
Called By       :
Other           :
Modify Record   :
******************************************************************************************/ 
char *strupr(char *str)
{
    
    ZASSERT(str);
    char *lstr=str;
    while(*lstr!='\0')
    {
        *lstr= static_cast<char> (toupper(*lstr));
		lstr++;
    }
    return str;
}



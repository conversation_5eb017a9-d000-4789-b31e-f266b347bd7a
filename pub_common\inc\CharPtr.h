//////////////////////////////////////////////////////////////////////
// CharPtr.h: interface for the CCharPtr class.
// 封装一个 char* 
// ANSI C/C++ 用于任何平台(source layer)
// 版权所有 bskay
// 你可以...但是必须... srclib
//////////////////////////////////////////////////////////////////////

#if !defined(__ANY_CharPtr_H__8085417D_0810_46E7_894C_7164F96A652D__INCLUDED_)
#define __ANY_CharPtr_H__8085417D_0810_46E7_894C_7164F96A652D__INCLUDED_

#ifndef LPSTR
typedef char* LPSTR;
#endif

#ifndef LPCSTR
typedef const char* LPCSTR;
#endif

#ifndef NULL
#define NULL 0
#endif

class CCharPtr
{
public:
    //构造函数
    CCharPtr(int nBlock=20);
    //拷贝构造函数
    CCharPtr(const CCharPtr& src);
    CCharPtr(LPCSTR szSrc, bool bCopy=true);
    CCharPtr(char ch, int nCount=1);
	bool Copy(LPCSTR str, int nLen);
	bool CopyToBuf(LPSTR pBuff, int& nBuffLen, bool bForce=false);
    virtual ~CCharPtr();

public:
	LPSTR Attach(LPSTR psz,int nLen=0);
	LPSTR Detach();
    int Format(LPCSTR szFormat, ...);
    int Length() const;
    int SetLen(int nLen);
    LPSTR GetBuffer(int nLen);
    // 子串
    CCharPtr Mid(int nStart, int nCount) const;
    CCharPtr Left(int nCount) const;
    CCharPtr Right(int nCount) const;
    // 查找
    int Find( char ch , int nStart=0 ) const;
    int Find( LPCSTR str , int nStart=0 ) const;
    int ReverseFind( char ch , int nStart=0 ) const;
    int ReverseFind( LPCSTR str , int nStart=0 ) const;

    // 替换
    int Replace( char chOld, char chNew );
    int Replace( LPCSTR lpszOld, LPCSTR lpszNew );

	// 插入
    int Insert( int nIndex, char ch );
    int Insert( int nIndex, LPCSTR src );

	// 删除
	int Delete( int nIndex, int nCount = 1 );

	void MakeUpper();
	void MakeLower();
public:
    //操作符
    // ptr = ptr
    CCharPtr& operator = (const CCharPtr& src);
    // ptr = str (fast)
    CCharPtr& operator = (LPCSTR str);
    // ptr + ptr
    friend CCharPtr operator + (const CCharPtr& szLeft, const CCharPtr& szright);
    // str == ptr
    bool operator == (const CCharPtr& src) const;
    bool operator != (const CCharPtr& src) const;
    // ptr == str
    bool operator == (LPCSTR str) const;
    bool operator != (LPCSTR str) const;
    // ch = ptr[n]
    char operator [] (int nIndex) const;
    // ptr[n] = ch
    char& operator [] (int nIndex);

    // ptr += ptr
    CCharPtr& operator += (const CCharPtr& src);
    // 兼容 char*
    operator LPCSTR () const;
public:
	// str + ptr
	friend CCharPtr operator + (LPCSTR str1, const CCharPtr& src2);
	// str == ptr
	friend bool operator == (LPCSTR str1, const CCharPtr& src2);
	friend bool operator != (LPCSTR str1, const CCharPtr& src2);
protected:
	static bool strlen(LPCSTR lpszStr, int& nLen);
	static bool strcpy(LPSTR lpszDst, LPCSTR lpszSrc, int nBuffLen, int& nCopyed);
	static bool memset(LPSTR lpszDst, int nFilled, int nBuffLen);
private:
    //实现
    bool PrepareBuffer(int nLen=0);
    LPSTR m_pointer;
    const int m_nBlock;
    int m_nMaxLen;

	int m_nRefCount;
};

#endif // !defined(__ANY_CharPtr_H__8085417D_0810_46E7_894C_7164F96A652D__INCLUDED_)

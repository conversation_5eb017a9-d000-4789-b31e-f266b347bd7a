/********************************************************************************
*Copyrights  2010       深圳天源迪科信息技术股份有限公司
*                       ABP项目组
*All rights reserved.
*
* Filename：    DCRDEventType.h	
* Description： 事件类型获取		
* Version：		V1.0
* Author:		
* Finished：
* History:		
********************************************************************************/ 
#ifndef __DC_RD_EVENT_TYPE_H__
#define __DC_RD_EVENT_TYPE_H__

//#include "common/DCAcctDataStruct.h"
#include "DCAtCondition.h"
#include "DCDBManer.h"

#define RENT_EVENT_TYPE_CONTROL_RENT        1       //主产品周期费
#define RENT_EVENT_TYPE_CONTROL_PRODUCT     2       //附属产品周期费

#define MAX_EVENT_STRATEGY              100     //同一事件触发的最多策略数
#define FEE_TYPE_RENT				2
#define FEE_TYPE_PRODUCT				3

struct STRentEventType
{
	char sEffTime[15];
	char sExpTime[15];
	long nEventTypeId;
	long nRentEventTypeId;
};

struct STRentEventTypeData
{
    int  iEffColId;
    int  iExpColId;
    int  iControlFlag;
    long nRentEventTypeId;
    long nUniteConditionId;
    long nEventTypeId;
    char sEffDateCode[31];
    char sExpDateCode[31];
    char sRealEffDateCode[31];
    char sRealExpDateCode[31];
    char sEffDate[16];          //生效时间
    char sExpDate[16];          //失效时间
};

class DCRDEventType
{
public:
    DCRDEventType();
	~DCRDEventType();
    int Init(DCDBManer *dbm);
    int JudgeEventType(long lnOfrInstId,long lnPrdInstId,std::string strEventEffDate,std::string strEventExpDate);
    bool GetEventType(STRentEventType **v_pRentEventType);
    int SetCurrTime(char *vi_sCurrTime);
	char *GetTimeFromDateCode(const char *v_sDateCode, char *v_sTime);
	int NextEventTypeData(long lnOfrInstId,UDBSQL* pQuery,bool i_hisflag = false);

	bool reloadCondition(){ return m_pCondition->LoadCondtion();}

	int SetTimeInfo(StInstElementValue stInfo);

private:
    DCAtCondition* m_pCondition;

	STRentEventType m_stRentEventType[MAX_EVENT_STRATEGY];

	int m_iCountResult;
	int m_iCurrResult;

	char m_sCurrMonFirstDay[15];		                                   //系统时间所在月第一天时间
	char m_sCurrTime[15];			                                       //当前时间 
	int m_iInstanceType;

	DCDBManer *m_dbm;
	char m_szInstall[32];
	char m_szCompleted[32];
	char m_szSubCompleted[32];
};

#endif
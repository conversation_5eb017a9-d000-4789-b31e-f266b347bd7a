<?xml version="1.0" encoding="ansi"?> 
<dcsql>
<!--db 属性字段解释-->
<!--字段name：		必选	db实例名字-->
<!--字段category：	必选	数据库实现名字，与imp.category对应-->
<!--字段env：		可选	数据库实现的附加参数-->
<!--字段master：	必选	主连接串-->
<!--字段standby：	可选	备连接串，可不配置-->

<!--module 属性字段解释-->
<!--字段name：		必选	module名字 -->
<!--字段db：		必选	module里面的sql所用的dbinst名字 -->
<!--字段policy：	必选	SQL加载策略：must 启动时初始化sql, demand 使用时初始化sql, none 不使用 -->

<!--sql 属性字段解释-->
<!--字段name：		必选	sql名字，若存在sub, 则对应名字为 "name|sub" -->
<!--字段bind: 		必选	绑定参数串，每一位代表一个绑定参数类型，1-int, 2-long, 3-char*, 4-blob -->
<!--字段sub: 		可选	sql子索引，以逗号分割，针对会话表、累帐表等场景，将sqltext中的"[@]"依次替换成子索引以获取多个sql语句 -->
<data>
	<dbimp>
		<imp category="UM2DB" version="1.0.0" >libm2dbutil.so</imp>
		<imp category="UDCA" version="2.0.0" >libdcautil.so</imp>
		<imp category="UDBDRIVER" version="1.0.0" >libdriverutil.so</imp>
		<!--<imp category="URMDB" version="1.0.0" >librmdbutil.so</imp>-->
	</dbimp>
    <dbinst>
		<db name="billingm2db" category="UM2DB" >
		        <master>0</master>
		</db>
		<db name="billingdca" category="UDCA" >
			<env>FMT=string;SPLIT=^</env>
			<master>IP=**************;PORT=17011;UUID=TEST;ACCTID=ahdx;USER=hb;PWD=123456</master>
		</db>	
		<db name="billingmysql" category="UDBDRIVER">
				<env>CONTYPE=1;DBTYPE=6;DBMAX=100;DRV=libmydrv.so </env>
				<master>odbc:mysql//***************:8896/ducc_bill_ah_test?user=ducc&amp;password=ducc@2021</master>
			</db>          
			<db name="crmmysql" category="UDBDRIVER">
				<env>CONTYPE=1;DBTYPE=6;DBMAX=100;DRV=libmydrv.so </env>
				<master>odbc:mysql//***************:8896/ducc_crm_ah_test?user=ducc&amp;password=ducc@2021</master>
			</db>	
			<db name="billingtt" category="UDBDRIVER" >
		 <env>DBTYPE=6;DBMAX=16;DRV=libmydrv.so</env>
           <master>odbc:mysql//**************:7080/dmdb?user=dmdb&amp;password=dmdb</master>           
      </db>		
	</dbinst>
</data>
<app>
	<module name="billingm2db" db="billingm2db" policy="must">
		<sql name="QueryOfrExtAttr" bind="22"> 
			select default_value from tb_ofr_ext_attr on index_ofr_ext_attr(offer_id=?,attr_id=?)
		</sql>
		<sql name="QuerySysParam" bind="33">
			select PARA_VALUE from SM_SYSTEM_PARAMETER on index_sm_system_parameter(PARA_GROUP=?,PARA_KEY=?)
		</sql>
		<sql name="data_prd_fileter_ofrinstid" bind="2" sub="553,554,555,556,558,562,563,564,566"> 
			select PRD_INST_ID from TB_PRD_STATE_UT_CT_[@] on idx_ut_ct_ofrinstid_[@](OFR_INST_ID=?)
		</sql>
		<sql name="data_prd_prd_main_inst_filter" bind="2" sub="553,554,555,556,558,562,563,564,566"> 
			select OFR_INST_ID from TB_PRD_STATE_UT_CT_[@] on idx_ut_ct_prdinstid_[@](PRD_INST_ID=?)
		</sql>
		<sql name="QUERY_PAR_STATUS_CHARGE_RULE" bind="3" > 
			select charge_status_cd  from PAR_STATUS_CHARGE_RULE on idx_par_status_rule(ori_status_cd=?)
		</sql>
		<sql name="param_unite_condition_all" bind="1"> 
			select group_id,compare_code_type,compare_code,inset_id,in_code_type,in_code_offset,in_code_length,compare_operator,compare_value,unite_condition_id from tb_bil_unite_condition on idx_tb_bil_unite_con_service_type(condition_service_type=?)
		</sql><!--PARAM_UNITE_CONDITION_ALL-->	

		<sql name="rent_event_type" bind="11"> 
			select rent_event_type_id, unite_condition_id, control_flag, eff_date_code, exp_date_code, event_type_id, real_eff_date_code, real_exp_date_code, eff_date, exp_date from tb_bil_rent_event_type where latn_id=? and control_type=? order by judge_properity desc
		</sql><!--RENT_EVENT_TYPE-->	

		<sql name="param_unite_condition" bind="21"> 
			select group_id,compare_code_type,compare_code,inset_id,in_code_type,in_code_offset,in_code_length,compare_operator,compare_value from tb_bil_unite_condition on idx_tb_bil_unite_condition(unite_condition_id=?,condition_service_type=?)
		</sql><!--PARAM_UNITE_CONDITION-->			
		  <sql name="pricing_plan_find" bind="2">
           select pricing_plan_id,ofr_type_id,offer_type,ofr_name,calc_priority   from TB_PRD_OFR on idx_tb_prd_ofr_ofr_id( ofr_id = ?)
		  </sql>
		<sql name="data_prd_get_priorty" bind="12"> 
			select value from tb_bil_par_data  where  (latn_id=888 or latn_id=?) and par_type='OFR_CALC_PRIORITY' on idx_tb_bil_par_data(ofr_id =?)
		</sql><!--DATA_PRD_GET_PRIORTY-->	

		<!--主产品套餐过滤-->
		<sql name="data_prd_prd_ofr_main_inst_filter" bind="12"> 
			select 1 from tb_bil_par_data  where (latn_id=-1 or latn_id=?) and par_type='DATA_RENT_INSTANCE' on idx_tb_bil_par_data(ofr_id =?)
		</sql><!---->	
		
		<!--附属产品套餐过滤-->
		<sql name="data_prd_prd_ofr_inst_filter" bind="12"> 
			select 1 from tb_bil_par_data  where (latn_id=-1 or latn_id=?) and par_type='DATA_PRODUCT_INSTANCE' on idx_tb_bil_par_data(ofr_id =?)
		</sql><!---->
     <!-- 附属产品实例过滤 -->
    <sql name="data_prd_prd_sub_inst_filter" bind="12"> 
 select 1 from tb_bil_sub_inst_filter  where (latn_id=-1 or latn_id=?) and par_type='DATA_SUB_PRODINST_INSTANCE' on idx_tb_bil_sub_inst_filter(prod_inst_id =?)    </sql>
		<!--查询群组类型-->	
		<sql name="data_prd_grp_user_type" bind="1" sub="">
			select GROUP_USER_TYPE_ID,GROUP_USER_TYPE_CODE,LATN_ID from TB_PRD_GROUP_USER_TYPE on idx_TB_PRD_GROUP_USER_TYPE(GROUP_USER_TYPE_ID=?)
		</sql>
		<sql name="CYCLE_OFR_REL" bind="">
		select main_ofr_id,ref_ofr_id,if_deduct,dict_value,const_value,PRICING_COMBINE_GROUP_ID,main_strategy_id,ref_strategy_id from cycle_ofr_relation
		</sql>

		<sql name="RouteServiceCfg" bind="" >
			SELECT ID, ParentId, PRIORITY, Subscriber, RouteCode, RouteValueA, RouteValueB, RouteStatus, ServiceGroupId FROM route_service_cfg where RouteStatus='1' ORDER BY ParentId, PRIORITY DESC
		</sql>
		
		<sql name="GreyUserGroupCfg" bind="" sub="">
			SELECT GroupId, Imsi, AccNum, AcctID, RouteStatus FROM grey_user_group_cfg where RouteStatus='1'
		</sql>

		<sql name="GrayServGroupCfg" bind="" >
			SELECT ServiceGroupId, RouteProcess, ServiceName FROM route_service_group where RouteStatus='1'
		</sql>
	</module>	
	<module name="crmmysql" db="crmmysql" policy="must">
		<!----老用户提取(后付费)---->
		<sql name="Query840" bind="111" dyn="A">
			select ACCT_ID,
			   BILL_XCHG_ID,
			   ACC_NUM,
			   USER_BILL_ID,
			   USER_TYPE_ID,
			   OFFER_ID,
			   '********' EFF_DATE,
         '********' EXP_DATE,
			   PROD_INST_ID,
			   OWNER_CUST_ID,
			   STATUS_CD,			   
			   ifnull(date_format(CREATE_DATE, '%Y%m%d%H%i%s'),'**************') CREATE_DATE,
				 ifnull(date_format(BILL_DATE,'%Y%m%d%H%i%s'),'**************') BILL_DATE,
				 ifnull(date_format(INSTALL_DATE,'%Y%m%d%H%i%s'),'**************') INSTALL_DATE,
			   PROD_ID,
			   AREA_CODE,
			   BASIC_STATE			   
		  from PROD_INST
		 where LATN_ID=?	
		  AND mod(ACCT_ID,?)=? 
			AND OFFER_ID &lt;&gt; 12976			
			AND INSTALL_DATE is not null
			AND BILL_DATE is not null
      AND ifnull(PAYMENT_MODE_CD, '1200') &lt;&gt; '2100'
			AND date_format(INSTALL_DATE, '%Y%m%d') &lt;= '[@A]'			
			AND ACC_NUM is not null    
		  order by ACCT_ID,PROD_INST_ID
		  limit ********
		</sql>
		<!----老用户提取(预付费)---->
		<sql name="Query8400" bind="111">
			select ACCT_ID,
         BILL_XCHG_ID,
         ACC_NUM,
         USER_BILL_ID,
         USER_TYPE_ID,
         OFFER_ID,
         '********' EFF_DATE,
          '********' EXP_DATE,
         PROD_INST_ID,
         OWNER_CUST_ID,
         STATUS_CD,
         ifnull(date_format(CREATE_DATE, '%Y%m%d%H%i%s'),'**************') CREATE_DATE,
		 ifnull(date_format(BILL_DATE,'%Y%m%d%H%i%s'),'**************') BILL_DATE,
		 ifnull(date_format(INSTALL_DATE,'%Y%m%d%H%i%s'),'**************') INSTALL_DATE,
         PROD_ID,
         AREA_CODE,
	BASIC_STATE         
      from PROD_INST a
     where LATN_ID=?
	  and mod(ACCT_ID,?)=? 
      AND OFFER_ID not in ( 12976,361)
      and payment_mode_cd = '2100' 
      and status_cd not in ('110000','130000','150000') 
      AND ACC_NUM is not null
     and (
      ((BASIC_STATE = 'F0B' or (BASIC_STATE = 'F0E' and offer_id not in (427, 429, 481, 5413, 581, 1481)and status_cd &lt;&gt; '120000'))) or
       (BASIC_STATE = 'F0L' and status_cd ='120000' and offer_id =481 and bill_date &lt; DATE_SUB(curdate(),interval day(curdate())-1 day) ))
	  limit ********
		</sql>
		<!--销售品实例变更表 任务预占 (11：业务预占；21：任务分发预占) -->
		<sql name="UpdateDealStateChange" bind="2" sub="553,554,555,556,558,562,563,564,566">   
			UPDATE interface_ofr_change_[@] 
			SET Acct_deal_state=11
			WHERE Interface_ofr_change_id = ?
			AND Acct_deal_state=21
		</sql>
		<!--查询销售品实例变更表(后付费)-->
		<sql name="QueryOfrInstChange" bind="11" sub="553,554,555,556,558,562,563,564,566">		
			select Interface_ofr_change_id,
			       ofr_inst_id,
			       date_format(Create_date,'%Y%m%d%H%i%s')		           
      from interface_ofr_change_[@] 
			where Acct_deal_state in (0,2) 	
			and date_format(create_date,'%Y%m%d')=date_format(sysdate(),'%Y%m%d')
			and mod(ofr_inst_id,?)=? limit 10000
		</sql>	
		<!--查询销售品实例变更表(预付费)-->
		<sql name="QueryOfrInstChangePre" bind="11" sub="553,554,555,556,558,562,563,564,566">		
			select Interface_ofr_change_id,
			       ofr_inst_id,
			       date_format(Create_date,'%Y%m%d%H%i%s')		           
      from interface_ofr_change_[@]
			where Acct_deal_state in (0,2) 				
			and mod(ofr_inst_id,?)=? and limit 10000
		</sql>
		<!--更新销售品实例变更表处理状态-->
		<sql name="UpdateOfrInstChange" bind="12" sub="553,554,555,556,558,562,563,564,566">		
			update interface_ofr_change_[@] set Acct_deal_state=? where Interface_ofr_change_id=?
		</sql>	
		<sql name="DeleteOfrInstChange" bind="2" sub="553,554,555,556,558,562,563,564,566">
			delete from interface_ofr_change_[@] where interface_ofr_change_id=? and accu_deal_state=1 and acct_deal_state in (1,2)
		</sql>
		<!----查询所有销售品实例---->
		<sql name="GetAllOfrInst" bind="111">			
			select /* ["sqlId":"GetAllOfrInst","appName":"RentLoad","Mode":"SC"] */ OFFER_INST_ID,ifnull(date_format(EFF_DATE,'%Y%m%d%H%i%s'),'20100101000000'),ifnull(date_format(EXP_DATE,'%Y%m%d%H%i%s'),'********235959') from OFFER_INST
			WHERE LATN_ID=? AND	mod(OFFER_INST_ID,?)=?  AND (exp_date > DATE_SUB(sysdate(),INTERVAL 90 day) or exp_date is null) AND OFFER_TYPE IN ('11', '12') and STATUS_CD in ('1100', '1097', '1000')  limit ******** 
		</sql>
	</module>
	<module name="billingdca" db="billingdca" policy="must">	
		<!--插入消息内容到缓存域，用于超大消息传输到acctbolt-->
		<sql name="InsertBigAcctMsg" bind="3212">
			insert into CACHE.BAM (cache) values(?) on key(acctid=?,messagesource=?,BatchNo=?) expired=259200
		</sql>

		<!--删除缓存域消息内容-->
		<sql name="DeleteBigAcctMsg" bind="212">
			delete from CACHE.BAM on key(acctid = ?,messagesource = ?,BatchNo = ?)
		</sql>		
	 	<sql name="data_ofr_detail_inst_o" bind="231" sub="">		
	select PROD_OFFER_ID@2,OFFER_INST_ID@3,ROLE_ID@6,RELA_OBJ_INST_TYPE@7,OBJ_ID@8, OFFER_PROD_INST_REL_ID@1,PROD_INST_ID@4,EFF_DATE@9,EXP_DATE@10 from BILLING.ODE on key (OBJ_ID=?,RELA_OBJ_INST_TYPE=?,LATN_ID=?) use method=hash
</sql>      
		<sql name="data_prd_ofr_inst" bind="21" sub="">		
	 		select OFFER_ID@2,OWNER_CUST_ID@10,EFF_DATE@6,EXP_DATE@7,STATUS_CD@9,UPDATE_DATE@11,OFFER_TYPE@3 from BILLING.OFRI on key (OFFER_INST_ID=?,LATN_ID=?) use method=hash
	 	</sql>
		
		<sql name="data_prd_grp_user_group" bind="21" sub="">		
	 		select GROUP_USER_GROUP_ID@1 from BILLING.GRUP on key (SERV_ID=?,LATN_ID=?) use method=hash
	 	</sql><!--查询用户的群组，用于查询F1G1套餐-->	
		
		<sql name="data_prd_prd_sub_inst" bind="21" sub="">		
			select EFF_DATE@4,EXP_DATE@5,BILL_XCHG_ID@14,USER_TYPE_ID@7,USER_BILL_ID@8,OFFER_ID@3,PROD_INST_ID@1,OWNER_CUST_ID@13,STATUS_CD@9,CREATE_DATE@10,FIRST_FINISH_DATE@11,INSTALL_DATE@12,PROD_ID@2  from BILLING.SUBI on key (ACC_PROD_INST_ID=?, LATN_ID = ?) use method=hash
		</sql>	<!---->
		
		<sql name="QueryTbPrdOfrStraInst" bind="21" sub="">
			select  GROUP_ID@12,EFF_DATE@6,EXP_DATE@7 from BILLING.OFRI on key (OFFER_INST_ID=?,LATN_ID=?) use method=hash
		</sql>
		
		<sql name="data_ofr_inst_detail_info" bind="21" sub="">		
	 		select PROD_OFFER_ID@2,OFFER_INST_ID@3,ROLE_ID@6,RELA_OBJ_INST_TYPE@7,OBJ_ID@8, OFFER_PROD_INST_REL_ID@1,PROD_INST_ID@4,EFF_DATE@9,EXP_DATE@10 from BILLING.OFE on key (OFFER_INST_ID=?,LATN_ID=?) use method=hash
	 	</sql>
		
		<sql name="QueryDynOfrInst" bind="21" sub="">
			select PRICING_PLAN_ID@4,OFR_TYPE_ID@5,CALC_PRIORITY@3,OFR_ID@1,OFR_DETAIL_ID@10,EFF_DATE@8,EXP_DATE@9,OFR_INST_ID@7,PRICING_COMBINE_GROUP_ID@6,CAT_OFFER_TYPE@11 from BILLING.DYN on key (OBJECT_ID=?,LATN_ID=?) use method=hash
		</sql><!--普选优惠资料提取-->	
		
		<sql name="info_acct" bind="21" sub="">		
			select PROD_INST_ID@3,ACCT_ID@1,CUST_ID@2,ACCT_NBR_97@4,PAY_METHOD@5  from BILLING.ACT on key (ACCT_ID = ?, LATN_ID = ?) use method=hash
		</sql>	
		
		<sql name="data_group" bind="21" sub="">
			select GROUP_USER_GROUP_ID@1,PRD_INST_ID@3,GROUP_USER_TYPE_ID@2  from BILLING.GRP on key(GROUP_USER_GROUP_ID=? , LATN_ID=?) use method=hash
		</sql>
		
		<sql name="data_serv" bind="21" sub="">		
			select ACCT_ID@3,EXCH_ID@9,ACC_NUM@12,USER_BILL_ID@24,USER_TYPE_ID@25,OFFER_ID@26,EFF_DATE@14,EXP_DATE@15,PROD_INST_ID@1,OWNER_CUST_ID@4,STATUS_CD@8,CREATE_DATE@16,BILL_DATE@28,INSTALL_DATE@27,BILL_XCHG_ID@29,BILL_DATE@28,PAYMENT_MODE_CD@7,PROD_ID@5,AREA_CODE@11,BASIC_STATE@31 from BILLING.SERV on key (PROD_INST_ID=?, LATN_ID = ?)  where STATUS_CD@8 != '150000' use method=hash
		</sql>	
		<!--查询群组成员-->	
		<sql name="data_group_user" bind="21" sub="">
			select SERV_ID@3  from BILLING.GRUG on key(GROUP_USER_GROUP_ID=?,LATN_ID=?) use method=hash
		</sql>
        <!--删除环节缓存-->
      <sql name="QueryCacheBill" bind="3">
			select cache@1 from CACHE.CHECKUID on key(uid=?)
		</sql>
		<sql name="DeleteCacheBill" bind="3">
			delete from CACHE.CHECKUID on key(uid=?)
		</sql>

		<!--查询账户下的所有用户-->
		<sql name="query_acct_prod_sum" bind="21" sub="">
			select PROD_INST_ID@1, STATUS_CD@3 from BILLING.ACCTID on key(ACCT_ID=?,latn_id=?) use method=hash
		</sql>
		<sql name="QueryOfrInstAttr" bind="21" sub="">
          select PROD_OFFER_ATTR_ID@9,PROD_OFFER_ATTR_ID@9,EFF_DATE@6, EXP_DATE@7,PARAM_VALUE@3 from BILLING.OIA on key (OFFER_INST_ID= ?,LATNID=?) use method=hash
    </sql>
    <sql name="QueryOfrInstAttr7Cancel" bind="21" sub="">
          select PROD_OFFER_ATTR_ID@9,PROD_OFFER_ATTR_ID@9,EFF_DATE@6, EXP_DATE@7,PARAM_VALUE@3 from BILLING.OIA7 on key (OFFER_INST_ID= ?,LATNID=?) use method=hash
       </sql>
	</module>
	<module name="billingtt" db="billingtt" policy="must">	
		<sql name="LoadImmedUser" bind="113" sub="551">
      select itemoutimmed_id,acct_id,acc_num from ItemOutImmed_user_[@] where mod(acct_id,?)=? and itemoutimmed_state = 0 and billing_cycle=? 
    </sql>
    <sql name="UpdateImmedState" bind="12" sub="551">		
			update ItemOutImmed_user_[@] set itemoutimmed_state=?,update_time=sysdate  where itemoutimmed_id=?
		</sql>
		<sql name="JudgeImmedAcct" bind="23" sub="551">
      select itemoutimmed_id,acct_id,acc_num from ItemOutImmed_user_[@] where acct_id = ?  and itemoutimmed_state in(1,9) and billing_cycle=? 
    </sql>

	</module>
	<module name="billingmysql" db="billingmysql" policy="must">	
		<!----更新处理时间---->
		<sql name="UpdateCycleEvent" bind="32" sub="">
			update CYCLE_EVENT_PROFILE SET PRESENT_LAST_TIME=str_to_date(?,'%Y%m%d%H%i%s') 
			where CYCLE_EVENT_PROFILE_ID=?
		</sql>

		<!----新的查询循环配置表---->
		<sql name="QueryCycleEvent" bind="11">			
			select CYCLE_EVENT_PROFILE_ID,SET_DEAL_HOUR,LATN_ID,ifnull(date_format(PRESENT_LAST_TIME,'%Y%m%d%H%i%s'),'20100101000000'),ifnull(date_format(PRESENT_START_TIME,'%Y%m%d%H%i%s'),'20100101000000') from CYCLE_EVENT_PROFILE where PROC_ID=? AND PROC_TYPE=?		
		</sql>
		<sql name="UpdateCycleEventStart" bind="32" sub="">			
			update CYCLE_EVENT_PROFILE SET PRESENT_START_TIME=str_to_date(?,'%Y%m%d%H%i%s') 
			where CYCLE_EVENT_PROFILE_ID=?	
		</sql>
		<sql name="UpdateCycleEventDcfServ" bind="3111" sub="">
			update CYCLE_EVENT_PROFILE SET PRESENT_LAST_TIME=str_to_date(?,'%Y%m%d%H%i%s') 
			where PROC_TYPE=? AND PROC_ID=? AND LATN_ID=?
		</sql><!----统一任务分发---->
		<sql name="UpdateCycleEventStartDcfServ" bind="3111" sub="">			
			update CYCLE_EVENT_PROFILE SET PRESENT_START_TIME=str_to_date(?,'%Y%m%d%H%i%s') 
			where PROC_TYPE=? AND PROC_ID=? AND LATN_ID=?
		</sql><!----统一任务分发---->
		<!-- 复机 任务预占 (11：业务预占；21：任务分发预占) -->
		<sql name="UpdateDealStateFrozen" bind="2">
			UPDATE tb_prd_frozen_user
			SET acct_deal_state=11
			WHERE Frozen_id=?
			AND acct_deal_state=21
		</sql>	
		<sql name="Query555" bind="111">
			select Frozen_id, 
			       Prd_inst_id, 			      
			       State_time, 
			       obversion_mode 
			  from tb_prd_frozen_user
			 where Acct_deal_state in (0,2) 
			   and change_type = 0  
			   and Latn_id = ?  
			   and mod(Frozen_id,?)=?		
			   limit ********	 
	  </sql>	  
	  <sql name="UpdateFrozenUser" bind="121" sub="">		
			update tb_prd_frozen_user set Acct_deal_state=? where Frozen_id=? and Latn_id=?
		</sql>	
		<sql name="QuerySpecialAcct" bind="32" sub="553,554,555,556,558,562,563,564,566">
			select GROUP_ID,OFR_INST_ID,MOD_GROUP_ID,ACCT_ID from TB_BIL_CROSS_ACCOUNT_[@] where (instr(? ,concat (',',acct_id,','))>0 OR OFR_INST_ID=?) and IS_MANUAL=0 order by mod_group_id  limit 100000
		</sql>
		
		<sql name="DeleteSpecialAcct" bind="122" sub="553,554,555,556,558,562,563,564,566">
			delete from TB_BIL_CROSS_ACCOUNT_[@] where GROUP_ID=? and OFR_INST_ID=? and ACCT_ID=? and IS_MANUAL=0
		</sql>
		<sql name="UpdateCrossAcctMerge" bind="221" sub="553,554,555,556,558,562,563,564,566">
		  update TB_BIL_CROSS_ACCOUNT_[@] set MOD_GROUP_ID =? where  MOD_GROUP_ID =? and GROUP_ID =? and IS_MANUAL=0
    </sql>
    <!--获取跨账户记录，用于跨账户用户提取-->
   		<sql name="LoadOldCrossAcct" bind="22" sub="553,554,555,556,558,562,563,564,566">
			select DISTINCT ACCT_ID,MOD_GROUP_ID from TB_BIL_CROSS_ACCOUNT_[@] WHERE GROUP_ID=2 and IS_MANUAL=0 and  mod(MOD_GROUP_ID,?)=?  order by MOD_GROUP_ID limit 100000
		</sql>
		<sql name="InsertSpecialAcct" bind="2122" sub="553,554,555,556,558,562,563,564,566">
			insert into TB_BIL_CROSS_ACCOUNT_[@](ACCT_ID,GROUP_ID,OFR_INST_ID,MOD_GROUP_ID,IS_MANUAL) values(?,?,?,?,0)
		</sql><!--增加账户插入特殊分流表 grou_id =1大账户 =2跨账户 =3特大账户  -->
		<sql name="UpdateCrossAcctModGroupId" bind="22122" sub="553,554,555,556,558,562,563,564,566">
  			update TB_BIL_CROSS_ACCOUNT_[@] set MOD_GROUP_ID =? where MOD_GROUP_ID =? and GROUP_ID =? and ACCT_ID =? and OFR_INST_ID =? and IS_MANUAL=0
		</sql><!--新增待修改逻辑删除  -->
		<sql name="LoadSpecialAcct" bind="" sub="553,554,555,556,558,562,563,564,566">
			select ACCT_ID,GROUP_ID,OFR_INST_ID,MOD_GROUP_ID from TB_BIL_CROSS_ACCOUNT_[@] WHERE GROUP_ID in (1,2,3) and IS_MANUAL=0 limit 100000
		</sql>		
        <sql name="GetSpecialAcctSeq" bind="" sub="553,554,555,556,558,562,563,564,566">
			select SEQ_CROSS_ACCT_MOD_ID.nextval
		</sql>
      
      <!----查询单独账户(后付费)---->
      <!--sql name="QueryNewOfrInstAcct" bind="2" dyn="ABC">
			select 
			   a.ACCT_ID,
			   a.BILL_XCHG_ID,
			   a.ACC_NUM,
			   a.USER_BILL_ID,
			   a.USER_TYPE_ID,
			   a.OFFER_ID,
			   '********' EFF_DATE,
         '********' EXP_DATE,
			   a.PROD_INST_ID,
			   a.OWNER_CUST_ID,
			   a.STATUS_CD,			   
			   ifnull(date_format(a.CREATE_DATE, '%Y%m%d%H%i%s'),'**************') CREATE_DATE,
				 ifnull(date_format(a.BILL_DATE,'%Y%m%d%H%i%s'),'**************') BILL_DATE,
				 ifnull(date_format(a.INSTALL_DATE,'%Y%m%d%H%i%s'),'**************') INSTALL_DATE,
			   a.PROD_ID,
			   a.AREA_CODE
		  from PROD_INST_[@A] a
		 where  a.OFFER_ID &lt;&gt; 12976
			AND (a.STATUS_CD NOT IN ('110000','120000','120063', '120069','150000','130000','140000') OR
				(a.STATUS_CD IN ('110000','120000','120063', '120069') AND date_format(a.BILL_DATE, '%Y%m%d') &gt;= [@B])  OR 
          		(a.STATUS_CD IN('120000','120063') AND date_format(a.BILL_DATE, '%Y%m%d') &lt; [@B] AND   a.OFFER_ID IN (427,429,430,481,361,362)) )
			AND a.INSTALL_DATE is not null
			AND a.BILL_DATE is not null
			AND  date_format(a.INSTALL_DATE, '%Y%m%d') &lt;= [@C]
			AND nvl(a.PAYMENT_MODE_CD, 1200) = 1200
			AND a.ACC_NUM is not null
			AND a.ACCT_ID=?         
		  order by a.PROD_INST_ID
		</sql--><!--@A[LATN_ID] @B[CYCLE_BEGIN_DATE] @C[CYCLE_END_DATE]-->
		<!----查询单独账户(预付费)---->
      <!--sql name="QueryNewOfrInstAcctPre" bind="2" dyn="ABC">
			select 
			   a.ACCT_ID,
			   a.BILL_XCHG_ID,
			   a.ACC_NUM,
			   a.USER_BILL_ID,
			   a.USER_TYPE_ID,
			   a.OFFER_ID,
			   '********' EFF_DATE,
         '********' EXP_DATE,
			   a.PROD_INST_ID,
			   a.OWNER_CUST_ID,
			   a.STATUS_CD,			   
			   ifnull(date_format(a.CREATE_DATE, '%Y%m%d%H%i%s'),'**************') CREATE_DATE,
				 ifnull(date_format(a.BILL_DATE,'%Y%m%d%H%i%s'),'**************') BILL_DATE,
				 ifnull(date_format(a.INSTALL_DATE,'%Y%m%d%H%i%s'),'**************') INSTALL_DATE,
			   a.PROD_ID,
			   a.AREA_CODE
		  from PROD_INST_[@A] a
		 where  a.OFFER_ID &lt;&gt; 12976
			AND (a.STATUS_CD NOT IN ('110000','120000','120063', '120069','150000','130000','140000') OR
				(a.STATUS_CD IN ('110000','120000','120063', '120069') AND date_format(a.BILL_DATE, '%Y%m%d') &gt;= [@B])  OR 
          		(a.STATUS_CD IN('120000','120063') AND date_format(a.BILL_DATE, '%Y%m%d') &lt; [@B] AND   a.OFFER_ID IN (427,429,430,481,361,362)) )
			AND a.INSTALL_DATE is not null
			AND a.BILL_DATE is not null
			AND date_format(a.INSTALL_DATE, '%Y%m%d') &lt;= [@C]
			AND nvl(a.PAYMENT_MODE_CD, 2100) =2100
			AND a.ACC_NUM is not null
			AND a.ACCT_ID=?        
		  order by a.PROD_INST_ID
		</sql--><!--@A[LATN_ID] @B[CYCLE_BEGIN_DATE] @C[CYCLE_END_DATE]-->
      
      	<sql name="UpdateRentSendLog" bind="111321" sub="553,554,555,556,558,562,563,564,566">		
	 		update LOG_RENT_LOADUSER_[@] set CALLBACK_PRD_COUNT=?,CALLBACK_SUB_COUNT=?,CALLBACK_OFR_COUNT=?,MOD_DATE=str_to_date(?,'%Y%m%d%H%i%s') where DEAL_PID=? AND DEAL_DAY=?
	 	</sql><!--callback后更新日志表-->
		<sql name="InsertRentSendLog" bind="21111" sub="553,554,555,556,558,562,563,564,566">		
	 		insert into LOG_RENT_LOADUSER_[@](LOG_RENT_LOADUSER_ID,DEAL_PID,DEAL_DAY,SEND_PRD_COUNT,CALLBACK_PRD_COUNT,SEND_SUB_COUNT,CALLBACK_SUB_COUNT,SEND_OFR_COUNT,CALLBACK_OFR_COUNT,MOD_DATE) values(SEQ_LOG_RENT_LOADUSER_ID.nextval,?,?,?,0,?,0,?,0,sysdate())
	 	</sql><!--发送消息后插入日志表-->	
		<sql name="AppendRentSendLog" bind="111321" sub="553,554,555,556,558,562,563,564,566">		
	 		update LOG_RENT_LOADUSER_[@] set SEND_PRD_COUNT=?,SEND_SUB_COUNT=?,SEND_OFR_COUNT=?,MOD_DATE=str_to_date(?,'%Y%m%d%H%i%s') where DEAL_PID=? AND DEAL_DAY=?
	 	</sql><!--发送消息后更新日志表-->
		<!--账户变更 任务预占 (11：业务预占；21：任务分发预占) -->
		<sql name="UpdateDealStateTransfer" bind="2" sub="553,554,555,556,558,562,563,564,566">		
			UPDATE PROD_INST_TRANSFER_[@] 
			SET ACCT_DEAL_STATE=11
			WHERE PRD_TRANSFER_ID=?
			AND ACCT_DEAL_STATE=21
		</sql>
        <sql name="LoadTransferPrdAcct" bind="" sub="553,554,555,556,558,562,563,564,566">		
	 		select ACCT_ID,PRD_INST_ID,PRD_TRANSFER_ID from PROD_INST_TRANSFER_[@] where ACCT_DEAL_STATE=0
	 	</sql><!--加载账户变更信息-->
        <sql name="UpdateTransferPrdAcct" bind="12" sub="553,554,555,556,558,562,563,564,566">		
			update PROD_INST_TRANSFER_[@] set ACCT_DEAL_STATE=? where PRD_TRANSFER_ID=?
		</sql>      
      	<sql name="GetAllCrossMiddle" bind="" sub="553,554,555,556,558,562,563,564,566">
			select acct_id,ofr_inst_id from tb_bil_cross_account_mid_[@] order by ofr_inst_id,acct_id
		</sql>
		<sql name="InsertCrossMiddle" bind="22" sub="553,554,555,556,558,562,563,564,566">
			insert into tb_bil_cross_account_mid_[@](acct_id,ofr_inst_id) values(?,?)
		</sql>
		<sql name="DeleteOneCrossMiddle" bind="22" sub="553,554,555,556,558,562,563,564,566">
			delete from tb_bil_cross_account_mid_[@] where ACCT_ID=? and OFR_INST_ID=?
		</sql>
		<!-- 中间表任务数据预占(11：业务预占；21：任务分发预占) -->
		<sql name="UpdateDealStateMid" bind="2" sub="553,554,555,556,558,562,563,564,566">
			UPDATE tb_bil_cross_account_mid_[@] 
			SET deal_state=11 
			WHERE pkid=? 
			AND deal_state=21
		</sql>
      	<sql name="UpdateSpecialAcct" bind="1222" sub="553,554,555,556,558,562,563,564,566">
			update TB_BIL_CROSS_ACCOUNT_[@] set GROUP_ID=?,MOD_GROUP_ID=? where OFR_INST_ID=? and ACCT_ID=? and IS_MANUAL=0
		</sql>
         
	</module>
</app>
</dcsql>

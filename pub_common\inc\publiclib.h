/******************************************************************************************
Copyright           : 2004-5-16, Shenzhen Tianyuan DIC  Co.,Ltd
FileName            : publiclib.h
Author              : guoxd
Version             : 
Date Of Creation    : 2004-5-16
Description         : 公共函数头文件
Others              : 
Function List       : 
    1.  ......
Modification History:
    1.Date  :
      Author  :
      Modification  :
******************************************************************************************/
#ifndef _PUBLICLIB_H_
#define _PUBLICLIB_H_

#include <map>
#include <list>
#include <set>
#include <time.h>
#include <string.h>
#include <algorithm>
#include "predefine.h"
#include "ace/Singleton.h"

using namespace std;

#ifndef WIN32
	#include <pthread.h>
#endif

#ifdef WIN32
	#define  DIR_SPLITTER      '\\' 
#else
	#define  DIR_SPLITTER 		'/'
#endif

#define DC_SWAP_WORD(L) ((((L) & 0x00FF) << 8) | (((L) & 0xFF00) >> 8))

#define DC_SWAP_DWORD(L) ((DC_SWAP_WORD ((L) & 0xFFFF) << 16) \
            | DC_SWAP_WORD(((L) >> 16) & 0xFFFF))

#define DC_SWAP_DDWORD(L) ((DC_SWAP_DWORD ((L) & 0xFFFFFFFF) << 32) \
            | DC_SWAP_DWORD(((L) >> 32) & 0xFFFFFFFF))

#ifndef MAX_PATH
#define MAX_PATH 256
#endif

#define YYYYMMDDHHMMSSssss 0
#define YYYYMMDDHHMMSS 	   1
#define YYYYMMDD  		   2
#define YYMMDDHHMMSS	   3
#define YYYYMM			   4
#define YYMMDD			   5
#define YYYYMMDDHHMMSSssssUs 6
//#define SQLNOTFOUND		1403
#define BUFFER			4096 //文件系统块长，可以根据机器的实际配置设定

#define zero(buf)       memset(buf, 0, sizeof(buf))

//配置文件变量名称最大长度
static const int  MAX_CONFIG_ITEM_LEN = 50;  

//配置文件注释符号
static const char  COMMENT_SIG = '#'; 

namespace PublicLib
{
    //清除字符串右边的空格
    void RTrim(char *pszStr,char ch = ' ');		
    
    //清除字符串左边的空格
    void LTrim(char *pszStr,char ch = ' ');		
    
    //清除字符串两边的空格
    void Trim (char *pszStr,char ch = ' ');	
    
    //int转换成字符串
    void itoa(int n,string &str);
    
    //获得当前日期
    void GetTime(char *pszTime, int nType);		
    
    //判断是否是日期类型
    int IsDate(char* pcDate,int nDateType);		
    
    //判断是否是数字
    int IsNumber(char *pcNumber,int nLen);
    
    void Change24(char* pTime);
    
    //判断是否是闰年
    int IsLeapYear(int nYear);			
    
    //由nRec得到时间
    void GetDateTime(char *pszTime, time_t nRec);
    
    //从配置文件中读取配置项
    void getLBSConfig(char* result,const char* flag,int *status);
    
    //从配置文件中读取配置项
    int GetCfg(char *pszFileName, 
        char *pszVarName, 
        char *pszData,
        char *pszErr);
    
    //从配置文件中读取配置项值
    int GetVarValue(char *pszBuff, 
        char *pszTitle, 
        char *pszData,
        char *pszErr);
    
    //从字符串转换时间函数，只转换年月日
    time_t GetTimeFromShortStr(const char *strtm);
    
    //从字符串转换时间函数，转换年月日时分秒
    time_t GetTimeFromLongStr(const char *strtm);
    
    //将字符串全部转换为大写字符
    char *strupr(char *str);
    
    //将字符串全部转换为小写字符
    char *strlwr(char *str);
    
    //字符串比较，忽视大小写
    int stricmp(const char *string1,const char *string2);
    
    //字符串定长比较，忽视大小写
    int strnicmp(const char *string1, const char *string2, size_t maxlen);
    
    //按照指定分隔符拆分字符串
    int SplitStr(const char *pszStr,const char cSplit,list<string>& ltStr);
    
    //创建守护进程
#ifndef WIN32
    void StartDaemon();
#endif	
    
    //删除空文件
    int MoveEmptyFile(const char *pszFileName);
    
    //按指定文件长度，截断文件
    int TruncateFile(const char *pszFileName,const long lnFileLen);
    
    //时间自加函数
    void selftimeAdd(char* begin_time,int seconds);
    
    //设置文件读锁
    int ReadLock(int fd,int cmd,off_t offset,off_t len);
    int ReadLock(int fd,int cmd,short whence,off_t offset,off_t len);
    
    //设置文件写锁
    int WriteLock(int fd,int cmd,off_t offset,off_t len);
    int WriteLock(int fd,int cmd,short whence,off_t offset,off_t len);
    
    //释放文件锁
    int FreeLock(int fd,int cmd,off_t offset,off_t len);
    int FreeLock(int fd,int cmd,short whence,off_t offset,off_t len);
    
    //文件长度
    size_t GetFileSize(const char *pszFileName);
    
    //对文件进行备份、删除和移动操作
    int SumFile(const char *pszFileName,const char bak_type);
    
    //文件备份
    int BackupFile(const char *pszFileName,const char *pszBakFileName);
    
    //文件删除
    int RemoveFile(const char *pszFileName);
    
    //文件恢复
    int RestoreFile(const char *pszFileName,const char *pszBakFileName);
    
    //把文件名拆分为路径名和文件名
    void SplitFileName(const char *pszPath,char *pszDir,  char *pszFile);
    
    //查看程序是否已运行
    int pcount(char szProgName[]);
    
    //取得下一个Tag的相关信息，包括Tag本身占的字节数和Tag的值
    int GetNextTag(unsigned char * pbInBuffer, long& wTag, unsigned short & wTagLen);
    
    //取得Length的相关信息，包括Length本身占的字节数和Length值
    int GetTagLenInfo(const BYTE* pbInBuffer, WORD& wLenLen, long& wConLen);
    
    //转换二进制数(一个字节中的内容)到十进制字串
    int BitToDecstring(BYTE *pbInput, 
        int nStartBitPos, 
        int nBitLen, 
        char *pszOutput,  
        int nOutputLen);
    
    //时间校对函数
    int TimeCheck(const char *pszOldTime,char *pszNewTime,int nDuration);
    
    //返回微秒级时间:hh:mi:ss.000000
    char *GetUtime(void);
    
    //微秒累计开始
    int StartTime(); 
    
    double StopTime(); //微秒累计结束
    
    //加进程互斥锁
    //	void Pthread_mutex_lock(pthread_mutex_t *mptr);
    //释放互斥锁
    //	void Pthread_mutex_unlock(pthread_mutex_t *mptr);
    
    //结束时间和开始时间的时间间隔
    time_t secondsBetween(const char* pszStartTime,const char* pszEndTime);
    
    int SplitStrInt(const char *pszStr,const char cSplit,list<int>& ltStr);
    
    char* nsprintf(char *pszString,const char* pszFormat,const long nValue,const int nLen);
    
    char* nssprintf(char *pszString,const char* pszFormat,const char* pszValue,const int nLen);
    
    //数值类型的比较,转换成数字来比较
    bool  CompareValue(const char *v_a, const char *v_b, int v_Operators);

    //add by zengmf
    //字符串类型的比较,按字符串形式比较
    bool CompareString(const char *v_a, const char *v_b, int v_Operators);
    
    char* zsprintf(const char* pszString,const char* pszFormat,const int nLen,char* pszOut);
    
    //create by wangds
    //begin add by zhoufj 20050623 create by wangds
    unsigned char getLow4b(unsigned char read_c);
    unsigned char getHigh4b(unsigned char read_c);
    unsigned char setLow4b(unsigned char read_c);
    unsigned char setHigh4b(unsigned char read_c);
    
    void BcdToAsc(unsigned char *bcd_rec, unsigned char *asc_rec, int start_byte, int byte_num);
    
    int AscToBcd(unsigned char *asc_rec, unsigned char *bcd_rec, int start_byte, int byte_num);
    
    //begin add by zhoufj 20050623
    void output_16(void *ptr,int length);
    int zatoi(const char* pszValue,int nLen);
    long zatol(const char* pszValue,int nLen);
    int JudgeExpress(char cValue,const char* pszExpress);
    
    
    //获取上一个年月
    void GetBeforeMonth(const char* Now, char* Before);
    
    //获取当前月的上/下几个月
    void ChangeMonth(char* month, int offset);
    
    void ChangeDay(char* day, int offeset);

    //用于得到二个时间之间的天数:例如 szBeginDay = "20090922", szEndDay = "20090925" 则返回结果为3
    int CheckDay(char* szBeginDay,char* szEndDay);
    
    //王斌添加于2006-03-16
    char * strtoupper(const char *v_sSrc,char *v_sDes);
    char * strtolower(const char *v_sSrc,char *v_sDes);
    char * strreplace(const char *v_sSrc,char *v_sDes,char *v_sRep,size_t v_pos,size_t v_len);
	
	bool InCompare(const char* vi_pszLeft, const char* vi_pszRight);
	
	string int_to_string(int nIN);	 
	string long_to_string(long lnIN);   
    
    //王斌添加于2006-06-12 上海
    //bool CompareNew(const char *v_a,char *v_b,int v_Operator);
    //modify by zengmf
    bool CompareNew(const char *v_a,const char *v_b,int v_Operator,char v_type='C');
    
     // 读取没有子Section的配置
    bool OCS_readConfig(string& strValue,const char* szpName,const char* szpSectionPath, int nDecryptFlag=0);

    // 读取有一个子Section的配置
    bool OCS_readConfig(string& strValue,const char* szpName,const char* szpSubSectionPath,const char* szpSectionPath, int nDecryptFlag=0);
    bool Decrypt_Des(char* szDecryptBUffer, const char* szSrcBuffer, const char* szDecryptKey, int nDecryptFlag);
    void SplitConnStr(const char* pszConnStr, char*  pszUser, char* pszPassword, char* pszServiceName);

	// 获取本机ip
	void GetHostAddrIp(char *pszAddrIp);

	// 网络字节序转主机字节序(24bit整型)
	int DC_NTOH3Byte(int nOrg);
	
	// 主机字节序转网络字节序(24bit整型)
	int DC_HTON3Byte(int nOrg);

	// 网络字节序转主机字节序(64bit整型)
	long DC_HTON8Byte(long lOrg);

	// 主机字节序转网络字节序(64bit整型)
	long DC_NTOH8Byte(long lOrg);

    // 字符串中寻找子串
    char * FindSubStr(char* pszOrg,char* szpTarget);

    // 输出16进制字符串,pszDest为内存块,nLen为要输出的长度
    void PrintHex(const char* pszDest,int nLen,char* pszResult);

    // add by yangdy 20070626
    // 将数字串转换成int64类型
#ifdef WIN32
    __int64 GetInt64FromString(const char* pszString);
#endif

	void Reverse(char* pszData,int nLen);
    
    //add by zengmf 20080523
    //获取当前时间的微秒值
    long getTimeUsec();

    long GetSecTime();

    bool CheckEffDate(const char* pszTime, const char* pszBegin, const char* pszEnd);
    
    //获取某年某月的最后一天
    int getMaxDay(const int nYear, const int nMonth);

    //将日期转换为星期,注意星期天用0标识，需要和中国使用习惯区分开
    int dateToWeek(const char* pszDate,int& nWeek);
    int GetDynamicDay(const char * startTime,const char * currentTime,int off_set,char *BillingCycleStart) ;
    int GetDynamicMonth(const char * startTime,const char * currentTime,int off_set,char *BillingCycleStart);

    int SetSecTimeToDate(long lnSecTime, char* pszDate);
    long dateToSec(const char* pszDate);

    long GetInt(const double &v_f, const char &v_Mode);
	
	//带有舍入模式的整数除法
	long Div(long f, long v, char v_Mode);
	
    string& TrimString(string& str, char sp=' ');

    // 拆分特定字符分隔的串, 拆分结果追 insert 到集合中去
    int SplitParamList(const string& strValue, set<string>& setParams, char sep = ',');
    
    // 拆分特定字符分隔的串, 拆分结果 push_back 到数组中去
    int SplitParamList(const string& strValue, vector<string>& vParams, char sep = ',');

    //
    //  带通配符的字符串比较, 通配符作用域限定在一个段内
    //  注意, 只接受后缀形式的通配符. 在一个段内, 通配符之后的字符都会被忽略
    //
    // input sep: 段分隔符, 默认为'|'
    // input  wc: 通配字符, 默认为'*', 匹配任意串(包括空串)
    // returns: 串的比较结果, 与strcmp相似. 匹配成功时返回零.
    //
    int StringCompareEx(const char* src, const char* dst, int sep = '|', int wc = '*');

    // 字串形式的分数解析出分子分母的数值
    // num:  返回分子的值
    // den:  返回分母的值
    // returns:  解析成功返回0, 解析失败返回负值
    //
    int StringToFraction(const char* a, int& num, int& den, int s = '/');
	//获取下个日的开始时间
	void  GetNextDay(const char * current_time,char * next_time);	

    //判断目录是否存在。不存在则创建，返回 0 创建成功
    int MkdirIfNotExist(char *sPath);
}; 

#endif

// Language:    C++
//
// OS Platform: UNIX
//
// Authors: <AUTHORS>
//
// History:
// Copyright (C) 2001 by <PERSON><PERSON><PERSON>, Linkage. All Rights Reserved.
//
// Comments:
//

#ifndef SYSPARAM_H_INCLUDED_C44BBB89
#define SYSPARAM_H_INCLUDED_C44BBB89


#include "config-all.h"

#include <map>
#include <list>
#include <string>


#if defined(STDIOSTREAM_DISABLE)
#include <fstream.h>
#else
#include <fstream>
#endif


USING_NAMESPACE(std)

const char SECTDELIM = '\\';

//##ModelId=3B5D33A4013C
//##Documentation
//## 系统参数访问
//##
//## 系统参数组织由三部分构成：节（section）、参数名（name）和参数值（value）。
//##
//## 节包含多对参数名和参数值，同时节可以包含多个子节，形成树状结构。
//##
//## 类提供根据节、参数名查询参数值的方法；提供指定参数名，遍历其下的参数、
//##  值和子节名的方法。
//##

class SysParam
{
 public:
  SysParam();
  virtual ~SysParam() {;}

  //##ModelId=3BCBD1020001
  virtual bool initialization(const char *filename = 0);

  //##ModelId=3B5D36E60073
  virtual bool end();

  //##ModelId=3B8B4FFF002B
  //##Documentation
  //## 根据节名和参数名查询参数值
  virtual bool getValue(const string& sectionPath, const string& name,
                        string& value);

  //##ModelId=3B5D35650386
  //##Documentation
  //## 根据节名和参数名查询参数值
  bool getValue(const char *sectionPath, const char *name, char *value,
                int& length);

  //##ModelId=3B8B980A017F
  //##Documentation
  //## 设置当前节
  virtual bool setSectionPath(const string& sectionPath);

  //##ModelId=3B8B994C02E0
  //##Documentation
  //## 获取当前节的参数或子节名
  //##
  virtual int getSectionValue(string& name, string& value);

  //##ModelId=3BB3F2CB00D8
  virtual int getSubSection(string& subsection);

  //##ModelId=3B8BA009033B
  //##Documentation
  //## 打开指定节
  //##
  //## 打开的节的内容会缓存到内存，在参数较多时能加速查询速度。
  virtual bool openSection(const string& sectionPath);

  //##ModelId=3B8BA01500B7
  //##Documentation
  //## 关闭指定节
  virtual bool closeSection(const string& sectionPath);

  //##ModelId=3B8BA01E010A
  //##Documentation
  //## 关闭所有节
  virtual bool closeAllSection();

  //##ModelId=3BC2E2D8020B
  void dump();

 protected:
  //##ModelId=3BB2D21A037B
  struct PARAM
  {
    //##ModelId=3BB2D21B02BE
    string name;

    //##ModelId=3BB2D21C0036
    string value;

  };

  //##ModelId=3BB2F2D6012C
  virtual bool addValue(const string& sectionPath, const string& name,
                        const string& value);

  //##ModelId=3BB2F2D703E0
  virtual bool addSubSection(const string& sectionPath,
                             const string& subsection);

  //##ModelId=3BB3E6D60361
  bool extractSectionPath(const string& pathFull, string& pathParent,
                          string& sectionSub);

  //##ModelId=3B8B9E8D0316
  string m_currentSection;

  //##ModelId=3BC2A70C0317
  typedef multimap<string, PARAM> PARAMMAP;

  //##ModelId=3BC2A70D0106
  typedef multimap<string, string> SECTIONMAP;

  //##ModelId=3BC26CAD022A
  typedef PARAMMAP::iterator PARAMMIter;

  //##ModelId=3BC289760136
  typedef SECTIONMAP::iterator SECTIONIter;

  //##ModelId=3BC26DBF013E
  PARAMMAP m_paramMapCurrent;

  //##ModelId=3BC26DBF01AC
  SECTIONMAP m_subsectionMapCurrent;

  //##ModelId=3BC28F8B0012
  PARAMMIter m_currentParamIter;

  //##ModelId=3BC28F8001B1
  SECTIONIter m_currentSectIter;

 private:
  //##ModelId=3BB4256B00BF
  PARAMMAP m_paramMap;

  //##ModelId=3BB425EB01E6
  SECTIONMAP m_subsectionMap;

  //##ModelId=3BC2BE97001B
  typedef map<string, int> SECTIONCOUNT;

  //##ModelId=3BC2BE97036E
  typedef SECTIONCOUNT::iterator SECTIONCOUNTItr;

  //##ModelId=3BC2BE9902DA
  SECTIONCOUNT m_sectionCount;
};


#endif /* SYSPARAM_H_INCLUDED_C44BBB89 */

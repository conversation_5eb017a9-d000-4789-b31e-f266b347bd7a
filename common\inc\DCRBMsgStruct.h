

#ifndef _DC_RB_MSG_STRUCT_H_
#define _DC_RB_MSG_STRUCT_H_

#include <string>
#include <list>
#include <vector>
#include <map>
#include <string.h>
//#include "DCRentData.h"

using namespace std;


#define STD_STRING std::string


class     DCServAcct;

enum OfrType
{
    OFR_BASE = 0,
    OFR_FAVORABLE = 1,
    OFR_BELOW = 2,  //保底类型
    OFR_GROUP = 3,
    OFR_SUB = 4,
    OFR_SPE = 5,   //增值类销售品
	OFR_IVPN = 6,	//IVPN    
    OFR_OTHER = 9
};


//用户级保底套餐信息
struct T_GlobleOfrInfo
{    
    long lnGlobalOfrId;
    long lnCalcPriority;    
    long lnGlobalRatableId;
    int nBaodiFee;  //保底费
    long lnGlobalRentFee;  //保底套餐自己的租费
    long lnGlobalPlanId;
    long lnGlobalOfrInstId;
    bool bFindGlobalResource;
    int nGlobalResouceAmount; //当前累积值
    long lnUpdateRatableValue; //全局累积量需要更新的值
    long lnFavorRatableValue;  //实际全局累积量减免的值
    long lnOverRatableValue;  //超出保底部分的累积量
    std::string strRatableResourceType;
    std::string strResourceOwnerType;
    std::string strBaseResourceCode;
	T_GlobleOfrInfo()
	{
		lnGlobalOfrId = 0;
		lnCalcPriority = 0;
		lnGlobalRatableId = 0;
		nBaodiFee = 0;	
		lnGlobalOfrInstId = 0;
		lnGlobalPlanId = 0;
		bFindGlobalResource = false;
		nGlobalResouceAmount = 0;
		lnUpdateRatableValue = 0;
		lnFavorRatableValue = 0;
		lnOverRatableValue = 0;
		lnGlobalRentFee = 0;
	}
	void init()
	{
        lnGlobalOfrId = 0;
		lnCalcPriority = 0;
		lnGlobalRatableId = 0;
		nBaodiFee = 0;	
		lnGlobalOfrInstId = 0;
		lnGlobalPlanId = 0;
		bFindGlobalResource = false;
		nGlobalResouceAmount = 0;
		lnUpdateRatableValue = 0;
		lnFavorRatableValue = 0;
		lnOverRatableValue = 0;
		lnGlobalRentFee = 0;
	}
	//结构体作升序排序需重载<
	bool operator<(T_GlobleOfrInfo tGlobleOfrInfo) const
	{
	    return lnCalcPriority < tGlobleOfrInfo.lnCalcPriority;		
	}
	//结构体作降序排序需重载>
	bool operator> (T_GlobleOfrInfo tGlobleOfrInfo) const
	{
	    return lnCalcPriority > tGlobleOfrInfo.lnCalcPriority;		
	}
};

class T_ratableInfo
{
public:
    long m_nRatableOwnerId;             //L001 累积量属主
    STD_STRING m_strRatableOwerType;    //L002 累积量属主类型
    long m_nRatableResourceId;          //L003 累积量标识
    long m_nRatableCycleId;             //L004 帐期
    long m_nRatableBalance;             //L005 累积量更新数量
    int  m_nRatableResourceType ;      //L006累积量类型
    long m_nRatableTotalBalance;       //L007累积量总量

    
    

    T_ratableInfo()
    {
        m_nRatableOwnerId = -1;
    }
};


class T_baseMsg
{
public:
    STD_STRING m_strMsgType;                //message type
    STD_STRING m_strEvtTimeStamp;           //EventTimeStamp
    STD_STRING m_strSessionId;              //会话ID
    STD_STRING m_strRequstId;               //请求序列号
    STD_STRING m_strCallingHomeAreaCode;    //主叫(或连接)号码归属费率区
    STD_STRING m_strCallingVisitAreaCode;   //主叫(或连接)号码拜访费率区
    STD_STRING m_strCallingPartner;     //主叫(或连接)号码归属运营商
    STD_STRING m_strCallingNbr;         //主叫号码
    STD_STRING m_strCalledHomeAreaCode; //被叫号码归属费率区
    STD_STRING m_strCalledVisitAreaCode;    //被叫号码拜访费率区
    STD_STRING m_strCalledPartner;          //被叫号码归属运营商
    STD_STRING m_strCalledNbr;              //被叫号码
    STD_STRING m_strAccessNbr;              //接入号码
    STD_STRING m_strStartTime;              //会话开始时间
    STD_STRING m_strCallingType;            //话单类型
    STD_STRING m_strThirdNbr;               //第三方号码
    STD_STRING m_strPayFlag;                //用户付费属性标识
    STD_STRING m_strValidityTime;           //Validity Time
    STD_STRING m_strRatingGroup;            //rating group
    STD_STRING m_strChargedNbr;             //计费方号码,RB接口中的R01
    STD_STRING m_strChargedHomeAreaCode;    //计费方号码归属费率区,RB接口中的R5012
    STD_STRING m_strEffDate;                //用户的生效日期
    STD_STRING m_strExpDate;                //用户的失效日期
    long       m_lnShortNbr;                 //被叫短号
    int        m_nFeeType;                  //计费类型
    bool       m_bRatingQueryFlag;          //费率查询标示B20

    STD_STRING m_strCallingCell;            //主叫号码基站
    STD_STRING m_strCalligMscId;            //主叫号码交换机ID
    int m_ISMPFeeFlag;

    int m_strOffLineMsgType;       //离线消息类型
    STD_STRING m_strGroupNbr;      //集团号R1117
    STD_STRING m_strGroupVpnType;       //VPN类型R1118    
    int m_nPrePostPaid; //R621
    int m_nCellId;
    void Init()
    {
        m_strMsgType              = "";
        m_strEvtTimeStamp         = "";
        m_strSessionId            = "";
        m_strRequstId             = "";
        m_strCallingHomeAreaCode  = "";
        m_strCallingVisitAreaCode = "";     
        m_strCallingPartner       = "";
        m_strCallingNbr           = "";         
        m_strCalledHomeAreaCode   = "";
        m_strCalledVisitAreaCode  = "";     
        m_strCalledPartner        = "";
        m_strCalledNbr            = "";     
        m_strAccessNbr            = "";
        m_strStartTime            = "";     
        m_strCallingType          = "";
        m_strThirdNbr             = "";     
        m_strPayFlag              = "";
        m_strValidityTime         = "";         
        m_strRatingGroup          = "";
        m_strChargedNbr           = "";
        m_strChargedHomeAreaCode  = "";
        m_nFeeType                = -1;
        m_bRatingQueryFlag        = false;
        m_strCallingCell          = "";
        m_strCalligMscId          = "";
		m_ISMPFeeFlag=-1;
        m_strGroupNbr             = "";
        m_strGroupVpnType         = "";
	m_nPrePostPaid = 0;
	m_nCellId = 0;
	}
};

struct STTimeInfo
{
	long lnTime1;
	long lnTime2;
	long lnTime3;
	long lnTime4;
	long lnTime5;
	long lnTime6;
	long lnTime7;
	long lnTime8;

	STTimeInfo()
	{
		lnTime1 = 0;
		lnTime2 = 0;
		lnTime3 = 0;
		lnTime4 = 0;
		lnTime5 = 0;
		lnTime6 = 0;
		lnTime7 = 0;
		lnTime8 = 0;
	}

	const STTimeInfo& operator=(const STTimeInfo& stInfo)
	{
		lnTime1 = stInfo.lnTime1;
		lnTime2 = stInfo.lnTime2;
		lnTime3 = stInfo.lnTime3;
		lnTime4 = stInfo.lnTime4;
		lnTime5 = stInfo.lnTime5;
		lnTime6 = stInfo.lnTime6;
		lnTime7 = stInfo.lnTime7;
		lnTime8 = stInfo.lnTime8;

		return *this;
	}
};

struct STTariffInfo
{
	long lnTariffID;
	long lnAmount;

	STTariffInfo()
	{
		lnTariffID = -1;
		lnAmount = -1;
	}
};


//累积量信息组
struct STRatableInfoGroup
{
    long lnOfrInstId;  //累积量所属套餐的ofrInstId
	long lnRatableId;  //累积id
	long lnUsedValue;  //本次累积量更新值
	long lnTotalUsedValue;  //到达量:ABM返回的已累计值+本次更新值

	STRatableInfoGroup()
	{
	    lnOfrInstId = -1;
		lnRatableId = -1;		
		lnUsedValue = -1;
		lnTotalUsedValue = -1;
	}
};



class T_BalanceContent
{
public:
    long m_nAcctItemTypeID;              //账目类型
    int m_nUnitTypeId;                  //单位类型
    int m_nDosage;                    //实际的数量(时长或流量)
    int m_nCount;                       //实际的次数
    long m_lnAmount;                    //金钱
    long m_lnDisountAmount;            //全局累积量优惠费用

    int m_iDataType; //数据来源类型 1来自计费,2 来自帐务优惠  
    long m_nServerId;//帐务优惠中用的用户标识
    //用于陕西用户账务关系
    long m_lnAcctId;                    //账户
	//用于标识月租保底
	bool m_bCycleBelow;                 
	int m_iAcctDataType; // 0-账务优惠分摊费用, 1账务优惠回滚费用

	list<STRatableInfoGroup> lstRatableInfoGroup;  //用户扣费日志中添加累积量信息组

	T_BalanceContent()
	{
		m_iDataType = 1;
		m_nServerId = 0;
		m_iAcctDataType = 0;
		m_lnAcctId = 0;
		m_lnDisountAmount = 0;	
		lstRatableInfoGroup.clear();
	}
};


struct T_PrdOfr
{
    int iOfrID;//ofr_id
    int iOfrTypeID;//ofr_type_id
    int iPricingPlanID;//定价计划
    int iLatnID;//本地网标识
    OfrType iFlag;//销售品类型0:基础 1:优惠2:附属3:保底
    //long iOfrInstID;//销售品实例id
    list<long> ltOfrInstID; //由于西藏存在一个OFR-iD对应二个不同的销售品实例(一个是用户级A1,一个是集团级F1)
    int iPricingSectionID;//定价段落，只针对附属销售品
	std::list<long> ltStategyID;     //定价策略，只针对附属销售品
    std::list<long> ltSubPrdID;      //附属产品ID，只针对附属销售品
    bool bHirstoryFlag;//更新记录是否存在标识 false不存在 true存在
	int nDisctValue;	//IVPN折扣率，默认为100
    char szEffDate[15]; //生效时间
    char szExpDate[15]; //失效时间
  	char szDeductList[1024];
	char szRataUpdateList[1024];
  	list<int> ltGroup;
	bool bNeedEraseOfr;
	long lnSubPrdInstId;
	string strOfrDetailTypeId;
	long lnOfrDetailInstId;
	long lnOfrDetailId;
	int  nDiscountValue;  //主套餐折扣率0-100
	long lnCalcPriority;
    T_PrdOfr()
    {
        iOfrID = -1;
        iOfrTypeID = -1;
        iPricingPlanID = -1;
        iLatnID = -1;
		lnSubPrdInstId = -1;
		lnOfrDetailInstId = -1;
		lnOfrDetailId = -1;
        iFlag = OFR_OTHER;
        //iOfrInstID = -1;
        ltOfrInstID.clear();
        iPricingSectionID   = -1;
        bHirstoryFlag   = true;
        ltStategyID.clear();
        ltSubPrdID.clear();
		ltGroup.clear();
		nDisctValue = 100;
		nDiscountValue = 100;
		bNeedEraseOfr = false;
		lnCalcPriority = -1;
        memset(szEffDate,0,sizeof(szEffDate));
        memset(szExpDate,0,sizeof(szExpDate));
		memset(szDeductList,0,sizeof(szDeductList));
		memset(szRataUpdateList,0,sizeof(szRataUpdateList));
    }
};

//套餐属性
struct STOfferAttr
{
	int nOfrId;
	long lnOfrInstId;
	int nOfrAttrId;
	char szOfrAttrValue[32];
	char szEffDate[16];
	char szExpDate[16];
	STOfferAttr()
	{
		nOfrId = -1;
		lnOfrInstId = -1;
		nOfrAttrId = -1;
		memset(szOfrAttrValue, 0x0, sizeof(szOfrAttrValue));
		memset(szEffDate, 0x0, sizeof(szEffDate));
		memset(szExpDate, 0x0, sizeof(szExpDate));
	}

	void operator = (const STOfferAttr &r)
	{
		this->nOfrId = r.nOfrId;
		this->lnOfrInstId = r.lnOfrInstId;
		this->nOfrAttrId = r.nOfrAttrId;
		strcpy(this->szOfrAttrValue, r.szOfrAttrValue);
		strcpy(this->szEffDate, r.szEffDate);
		strcpy(this->szExpDate, r.szExpDate);
	}
};

struct STRentABMInfo
{
public:
    STD_STRING m_strSessinoID;
    STD_STRING m_strAreaCode;
    STD_STRING m_strServNbr;
    STD_STRING m_strTime;
    STD_STRING m_strThirdNbr;
    STD_STRING m_strOfrEffTime;
    STD_STRING m_strBasicState;//用户状态
    STD_STRING m_strUserType;
	STD_STRING m_strMVNOID;
	int  nUserMode;
    long lnServID;
    long lnAcctID;
    long lnCustID;
    long lnOfrID;    
    long lnInstChangeID;
	long lnPrdID;

    int  nLatnID;
    int  nDeductType;
    int nEvtID;
    int  nOfrType;
    int nNewUserFlag;
    int nDeductDay;
	int nDisctValue;	//折扣率
    int iReductFlag;    //自选补扣标记，-1：不是自选补扣，0：不发送扣费请求，1：发送扣费请求 add by huanghua 20101118

    bool  bNegaFlag;
	bool  bFrozenUserFlag;//标识是否为冷冻用户
	bool  bFrozenLimit;   //标识是否是用户冷冻期首月扣保底费

    int  nABMReqMessageTypeID;  //ABM请求消息类型(当超时时要判断该消息是什么消息超时,需要用到)

    std::list<long> ltOfrInstID;
	std::list<STTariffInfo> ltTariffInfo;
	std::list<long> ltSubStrategy;//用户订购附属产品策略
    std::list<long> ltSubPrdId;//用户订购附属产品策略
    std::list<int> ltPrsGrpId;//定价计划组ID
    std::list<int> ltEventType;
    long lnStrategyInstGroupId;
	STTimeInfo stTimeInfo;

	long lnGlobalRatableId;    //全局累积量ID
	int  nBaodiFee;             //全局累积量保底费用
	long lnGlobalOfrId;
	long lnGlobalOfrInstId;

    list<T_GlobleOfrInfo> lstGlobleOfrInfo;   
	
	list<T_PrdOfr> ltUserOfrInfo;
	STRentABMInfo()
    {
        m_strSessinoID = "";
        m_strAreaCode = "";
        m_strServNbr = "";
        m_strTime = "";
        m_strThirdNbr = "";
        m_strOfrEffTime = "";
        m_strBasicState = "";
		m_strUserType = "";
		m_strMVNOID = "";

        lnServID = -1;
        lnAcctID = -1;
		lnPrdID = -1;
        lnCustID= -1;
        lnOfrID= -1;   
        lnInstChangeID= -1;
		lnStrategyInstGroupId = -1;
		nABMReqMessageTypeID = 0;
		nUserMode = -1;
		nDeductType = -1;

        nLatnID= -1;
        nEvtID= -1;
        nOfrType= -1;
        bNegaFlag= true;
        nNewUserFlag= -1;
		bFrozenUserFlag = false;
		bFrozenLimit = false;
        nDeductDay= -1;
		nDisctValue = 100;
        iReductFlag = -1;//标记默认为-1，表示不为自选扣费add by huanghua 20101118

        ltOfrInstID.clear();
		ltTariffInfo.clear();
		ltSubStrategy.clear();
        ltSubPrdId.clear();
        ltPrsGrpId.clear();
		ltUserOfrInfo.clear();
		ltEventType.clear();
		lnGlobalRatableId = -1;
		lnGlobalOfrInstId = -1;
		nBaodiFee = 0;
		lnGlobalOfrId = -1;

		lstGlobleOfrInfo.clear();
		
    }

};


struct STArrearABM 
{
    STD_STRING strSessinoID;
    STD_STRING strAreaCode;
    STD_STRING strServNbr;
    STD_STRING strRechargeTime;    

    long lnServID;
    long lnAcctID;
    long lnLatnID;
    long lnRecharge;
    long lnArrear;
    long lnArrearID;
    long lnRequestID;
    bool  bNegaFlag;
};

class T_varMsg
{
public:
    STD_STRING m_strResendFlag;         //重发标记
    STD_STRING m_strBillingFlag;            //计费类型
    STD_STRING m_strLastTime;               //会话上次实际扣费开始时间（计算实际扣费）
    STD_STRING m_strCurrTime;               //本次计费请求开始时间（计算预扣费用）
    STD_STRING m_strRatableFlag;            //是否进行使用量累计标识
    STD_STRING m_strProductID;              //ISMP R401
    STD_STRING m_strProSpecID;               //DSL R904
    int   m_nActiveUserFlag;               //是否需要激活，1－需要，其它－不需要

    int m_nOfrType;                      //销售品类型
    int   m_nVPNType;                      //vpn类型,0.网内;1.网间;2.非VPN,默认值2
    int   m_nRelationNbr;                  //是否是亲情号码，0:否,1:是,默认值0
    long        m_nAcctItemType;   //ISMP 根据R402 转换过来的帐目类型
    int m_nDiscFlag;//1：参与日账优惠，2不参与日账优惠
public:
    void Init()
    {
        m_strResendFlag  = "";
        m_strBillingFlag = "";
        m_strLastTime    = "";
        m_strCurrTime    = "";
        m_strRatableFlag = "";
        m_strProductID   = "";
        m_nActiveUserFlag = 0;
		m_strProSpecID ="";

        m_nVPNType     = 2;
        m_nRelationNbr = 0;
        m_nAcctItemType = -1;

        m_nOfrType = 0;
        m_nDiscFlag = 1;
    }
};
struct S_OFF_Ratint_info
{
    int m_nMeasure;//R57组解析
    long m_lnDosage;
    long m_lnOfr_Inst_id;
    long m_lnOfr_id;
	long m_lnBillingDosage;
};
class T_CommandMsg
{
public:
    //请求组(B01)，预占使用
    STD_STRING m_strReqDuration;            //请求时长
    STD_STRING m_strReqTimes;               //请求使用次数
    STD_STRING m_strReqUpVolume;            //请求预占上行流量
    STD_STRING m_strReqDownVolume;          //请求预占下行流量
    STD_STRING m_strReqTotalVolume;         //请求预占总流量
    //实扣组(B03)
    STD_STRING m_strRealDuration;           //实际使用通话时长
    STD_STRING m_strRealTimes;              //实际使用次数
    STD_STRING m_strRealUpVolume;           //实际上行流量
    STD_STRING m_strRealDownVolume;         //实际下行流量
    STD_STRING m_strRealTotalVolume;        //实际总流量
    //更新使用组(B30)
    STD_STRING m_strUsedDuration;           //使用时长
    STD_STRING m_strUsedTimes;              //使用次数
    STD_STRING m_strUsedUpVolume;           //使用上行流量
    STD_STRING m_strUsedDownVolume;         //使用下行流量
    STD_STRING m_strUsedTotalVolume;        //使用总流量

    STD_STRING m_strUsedUpVolumeFeeLast;           //使用上行流量费率切换点之后的使用量
    STD_STRING m_strUsedDownVolumeFeeLast;         //使用下行流量费率切换点之后的使用量
    STD_STRING m_strUsedTotalVolumeFeeLast;        //使用总流量费率切换点之后的使用量

    STD_STRING m_strReqProductID;           //请求的ProductID
    STD_STRING m_strRealProductID;          //实扣的ProductID
    STD_STRING m_strUsedProductID;          //使用的ProductID

    S_OFF_Ratint_info m_sctOff_Rating_info;
public:
    void Init()
    {
        m_strReqDuration     = "";
        m_strReqTimes        = "";
        m_strReqUpVolume     = "";
        m_strReqDownVolume   = "";
        m_strReqTotalVolume  = "";

        m_strRealDuration    = "";
        m_strRealTimes       = "";
        m_strRealUpVolume    = "";
        m_strRealDownVolume  = "";
        m_strRealTotalVolume = "";

        m_strUsedDuration    = "";
        m_strUsedTimes       = "";
        m_strUsedUpVolume    = "";
        m_strUsedDownVolume  = "";
        m_strUsedTotalVolume = "";

        m_strUsedUpVolumeFeeLast ="";
        m_strUsedDownVolumeFeeLast ="";
        m_strUsedTotalVolumeFeeLast="";

        m_strReqProductID    = "";
        m_strRealProductID   = "";
        m_strUsedProductID   = "";
        memset(&m_sctOff_Rating_info,0x00,sizeof(S_OFF_Ratint_info));
    }
};



//用与会话管理用，保存上一次批价后的结果
class T_SessionInfo
{
public: 
    //累计未实扣的预占量
    long m_lnReserveMoney;          //金钱
    long m_lnReserveDuration;       //时长
    long m_lnReserveTimes;          //次数
    long m_lnReserveUpVolume;       //上行流量
    long m_lnReserveDownVolume;     //下行流量
    long m_lnReserveTotalVolume;    //总流量

    //累计扣费信息
    long m_lnUsedMoney;         
    long m_lnUsedDuration;
    long m_lnUsedTimes;
    long m_lnUsedUpVolume;
    long m_lnUsedDownVolume;
    long m_lnUsedTotalVolume;

    //未实扣信息
    long m_lnUnUsedMoney;         
    long m_lnUnUsedDuration;
    long m_lnUnUsedTimes;
    long m_lnUnUsedUpVolume;
    long m_lnUnUsedDownVolume;
    long m_lnUnUsedTotalVolume;

    //CCG费率切换信息
    long m_lnCCGSPStart;
    long m_lnCCGSPEnd;

    int m_nEventType;

    char m_sModifyTime[21];//addby zhuhy 20110816
    //上次批价的资源类型
    int m_nMeasure;
    void Init()
    {
        m_lnReserveMoney = 0;
        m_lnReserveDuration    = 0;             
        m_lnReserveTimes       = 0;             
        m_lnReserveUpVolume    = 0;             
        m_lnReserveDownVolume  = 0;             
        m_lnReserveTotalVolume = 0;    
        m_lnUsedMoney = 0;
        m_lnUsedDuration       = 0;             
        m_lnUsedTimes          = 0;             
        m_lnUsedUpVolume       = 0;             
        m_lnUsedDownVolume     = 0;             
        m_lnUsedTotalVolume    = 0;   
        m_lnUnUsedMoney    = 0;         
        m_lnUnUsedDuration    = 0;
        m_lnUnUsedTimes    = 0;
        m_lnUnUsedUpVolume    = 0;
        m_lnUnUsedDownVolume    = 0;
        m_lnUnUsedTotalVolume    = 0;
        m_lnCCGSPStart = 0;
        m_lnCCGSPEnd = 0;
        m_nEventType = -1;
	 m_nMeasure = 0;
        memset(m_sModifyTime,0x00,sizeof(m_sModifyTime));
    }
};

class T_StartValue
{
public:
    long m_lDuration;
    long m_lTimes;
    long m_lUpVolume;
    long m_lDownVolume;
    long m_lTotalVolume;
    T_StartValue()
    {
        m_lDuration = 0;
        m_lTimes = 0;
        m_lUpVolume = 0;
        m_lDownVolume = 0;
        m_lTotalVolume = 0;
    }
};


class T_BalanceInMsg
{
public:      
    std::list<T_BalanceContent> m_ltFree;       //释放  
    std::list<T_BalanceContent> m_ltBack;       //返回，解析B04组金钱类的保存位置
    std::list<T_BalanceContent> m_ltQuery;      //查询
    std::list<T_BalanceContent> m_ltDeduct;     //实扣，解析B03组金钱类的保存位置
    std::list<T_BalanceContent> m_ltReserve;    //预占，解析B01组金钱类的保存位置
    std::list<T_BalanceContent> m_ltRecharge;   //充值
    std::list<T_BalanceContent> m_ltUsed;       //更新使用金额，解析B30金钱的保存位置

    bool m_IsQueryBalance;              //ture:返回余额查询结果  false:不查询
    bool m_IsNegativeBalance;           //是否可以扣成负数表示  ture:可以  false:不可以
    bool m_IsMoneyRequest;              //金钱类型请求
    int  m_nRealCtrlType;               //实扣附加控制类型 1:附加月封顶控制 2:附加月查重控制
    int  m_nRealCtrlPara;               //实扣附加控制参数
    int  m_nISMPCnt;                    //ISMP直接扣费/事件补款组个数。
public:
    void Init()
    {
        m_ltFree.clear();
        m_ltBack.clear();
        m_ltQuery.clear();
        m_ltDeduct.clear();
        m_ltReserve.clear();
        m_ltRecharge.clear();
        m_ltUsed.clear();
        m_IsQueryBalance = false;
        m_IsNegativeBalance = false;
        m_IsMoneyRequest = false;
        m_nRealCtrlType = -1;
        m_nRealCtrlPara = -1;
        m_nISMPCnt      = 0;
    }
};

class T_baseOutMsg
{
public:
    int m_nResultCode;
};

class T_BalanceOutMsg : public T_baseOutMsg
{
public:
    std::list<T_BalanceContent> m_ltQuery;

public:
    void Init()
    {
        m_nResultCode = 0;
        m_ltQuery.clear();
    }
};

class T_RatingOutMsg : public T_baseOutMsg
{
public:
    STD_STRING m_strAthDuration;            //授权时长
    STD_STRING m_strAthTimes;               //授权使用次数
    STD_STRING m_strAthUpVolume;            //授权预占上行流量
    STD_STRING m_strAthDownVolume;          //授权预占下行流量
    STD_STRING m_strAthTotalVolume;     //授权预占总流量

    STD_STRING m_strRatingSpanPoint;       //费率切换点

    int m_nOneAcct;                         //授权标识
    int m_nMeasure;
    long m_lnRatingTotalDosage;
    long m_lnRatingRealTotalDosge;
public:
    void Init()
    {
        m_strAthDuration     = "";
        m_strAthTimes        = "";
        m_strAthUpVolume     = "";
        m_strAthDownVolume   = "";
        m_strAthTotalVolume  = "";
        m_strRatingSpanPoint = "";
        m_nOneAcct           = 0;
        m_nMeasure = 0;
        m_lnRatingTotalDosage = 0;
        m_lnRatingRealTotalDosge = 0;
    }
};
class T_DeductInfo
{
public:
    long m_lnAcctBalanceID;             //B081  账本标识
    int m_nBalanceUnitType;             //B082  账本类型
    long m_lnAmount;                    //B083  扣除金额
    long m_lnBalance;                   //B084  账本余额
};

class T_extMsg
{
public:
    STD_STRING m_strCallingAreaId;          //主叫营业区
    STD_STRING m_strCalledAreaId;           //被叫营业区
    int    m_nEvtTypeId;                    //事件类型              
	STD_STRING m_strGroupId;        //20消息定价计划组
    STD_STRING m_strCallingSpeNbrState;     //主叫特服计费状态
    STD_STRING m_strCalledSpeNbrState;      //被叫特服计费状态
    STD_STRING m_strBillingAreaCode;        //计费号码区号
    STD_STRING m_strBillingNbr;             //计费 号码
    bool   m_bFreeFlag;                     //释放标识,false:不需要释放
    bool   m_bReChargeFlag;                 //充值标识,false:不需要充值

    int    m_nPoCIMServerRole;              //POC,IM服务器角色 R701 R801
    int    m_nPoCIMSessionType;             //POC,IM会话类型   R702 R802
    int    m_nSessionParticipateNum;        //参与者人数       R703 R803
    int    m_nIMFeatureType;                //IM业务特征类型   R804 


public:

    void Init()
    {
        m_strCallingAreaId      = "";
        m_strCalledAreaId       = "";
        m_strCallingSpeNbrState = "";
        m_strCalledSpeNbrState  = "";
        m_strBillingAreaCode    = "";
        m_strBillingNbr         = "";
		m_strGroupId           ="";
        m_nEvtTypeId            = -1;
        m_bFreeFlag             = false;
        m_bReChargeFlag         = false;

        m_nPoCIMServerRole      = -1;
        m_nPoCIMSessionType     = -1;
        m_nSessionParticipateNum= -1;
        m_nIMFeatureType      = -1;           
    }
};

class T_userMsg
{
public:

    long m_lnServId;
    long m_lnCustId;
    long m_lnAcctId;
    int  m_nOfrId;
    int  m_nLatnId;
    int  m_nCallingLatnId;
    int  m_nCalledLatnId;
    int m_nIsAccountDis;
    int  m_nRegionId;
    long m_lnPrdId;

    //用户状态
    STD_STRING m_strBasicState; 
    STD_STRING m_strPushInQueueTime;        
    STD_STRING m_strPopInQueueTime;
    STD_STRING m_strAttachNum;
	
public:
    void Init()
    {
        m_lnServId = -1;
        m_lnCustId = -1;
        m_lnAcctId = -1;
        m_nOfrId   = -1;
        m_nLatnId  = -1;
	m_nRegionId = -1;
        m_nCallingLatnId = -1;
        m_nCalledLatnId  = -1;
	m_nIsAccountDis = 0;
	
        m_strBasicState = "";
        m_strPushInQueueTime      = "";
        m_strPopInQueueTime       = "";
        m_lnPrdId = -1;
	m_strAttachNum ="-1";	
    }
};

//支持第三方付费,计费方信息 
class T_chargedPartyMsg
{
public:
	T_chargedPartyMsg()
	{
		m_lnChargedServId = 0;
		m_lnChargedCustId = 0;
		m_lnChargedAcctId = 0;
		m_nChargedLatnId = 0;
		m_nIF_IVPN = 0;
	}
    long m_lnChargedServId;
    long m_lnChargedCustId;
    long m_lnChargedAcctId;
    int  m_nChargedLatnId;
    int  m_nIF_IVPN;
public:
    void Init()
    {
        m_lnChargedServId = -1;
        m_lnChargedCustId = -1;
        m_lnChargedAcctId = -1;
        m_nChargedLatnId  = -1;
        m_nIF_IVPN = -1;
    }   
};

class T_ratingExtMsg
{
public:
    STD_STRING m_strDuration;           //时长
    STD_STRING m_strTimes;              //使用次数
    STD_STRING m_strUpVolume;           //上行流量
    STD_STRING m_strDownVolume;     //下行流量
    STD_STRING m_strTotalVolume;        //总流量
	long  m_nlCrossDayTime;
	long m_nlReaptTimes;

    STD_STRING m_strUpVolumeFeeLast;			    //上行流量费率切换点之后的使用量
    STD_STRING m_strDownVolumeFeeLast;		    //下行流量费率切换点之后的使用量
    STD_STRING m_strTotalVolumeFeeLast;		       //总流量费率切换点之后的使用量

    STD_STRING m_strExtStartTime;       //会话开始时间
    STD_STRING m_strExtLastTime;        //会话上次实际扣费开始时间（计算实际扣费）
    STD_STRING m_strExtCurrTime;        //本次计费请求开始时间（计算预扣费用）
    long   m_lnMoney;                
    int    m_nExtFeeType;               //标识消息业务 0:仅有预占 1:仅有实扣 2:既有预占又有实扣

public:
    void Init()
    {
        m_strDuration.clear();
        m_strTimes.clear();
        m_strUpVolume.clear();
        m_strDownVolume.clear();
        m_strTotalVolume.clear();
        m_lnMoney         = 0;
        m_strExtStartTime.clear();
        m_strExtLastTime.clear();
        m_strExtCurrTime.clear();
        m_strUpVolumeFeeLast.clear();
        m_strDownVolumeFeeLast.clear();
        m_strTotalVolumeFeeLast.clear();
        m_nExtFeeType     = 0;
		m_nlCrossDayTime =0;
		m_nlReaptTimes =0;
    }
};

class T_rateInfo
{
public:
    int m_nRateBeginTime;
    int m_nRateType; 
    int m_nRateUnit;
    float m_fRateValue;
    long m_lSectionId;  //新增section_id作为资费剔重的条件
};

// add by cuiye at 2007-06-22
class T_ratable
{
public:
    T_ratable()
    {
        m_nRatableCodeID = -1;        //B061  累计量代码
        m_nRatableType = -1;                 //B062  累计量类型
        m_nValue = 0;                       //B063  本次累计数值
        m_nRatableValue = 0;                //B064  累计后数值
        memset(strOwnerType,0x00,sizeof(strOwnerType)) ;//属主类型ID
        m_lnOwnerID = -1;//属主类型ID
        m_lnCycleID = -1;//属主类型ID
    }

    int m_nRatableCodeID;        //B061  累计量代码
    int m_nRatableType;                 //B062  累计量类型
    long m_nValue;                       //B063  本次累计数值
    long m_nRatableValue;                //B064  累计后数值
    char strOwnerType[4];//属主类型ID
    long m_lnOwnerID;//属主类型ID
    long m_lnCycleID;//账期
};

class T_FeeInfo
{
public:
    long m_lnCurrOfrId;                 //B071  商品ID
    int m_nMoney;                       //B072  商品费用

public:
    void Init()
    {
        m_lnCurrOfrId = -1;
        m_nMoney = 0;
    }

};

struct TariffInfo
{
	long m_lnTariff_ID;
	long m_lnTariff;
};

class T_BalanceInfo
{
public:
    long m_nAcctItemTypeID;              //B211  账目类型
    int m_nUnitTypeId;                  //B212  账本类型,批注错误，单位类型
    long m_lnAmount;                    //B213  数量
};



//CM请求消息待处理数据
struct DCCMReqMsgDisposerData
{
    //@{ 为了减少内存的使用,所有处理器公用一份数据   
    T_baseMsg       baseMsg;
    T_CommandMsg    CommandMsg;
    T_varMsg        varMsg;
    T_BalanceInMsg  BalInMsg;
    T_extMsg        extMsg;
    STRentABMInfo  stRentMsg;
    STArrearABM   stArrearMsg;

    int m_nOffRepeatFlag; //R200 0-不剔重1-剔重
    int m_nBillingFlag;     //R602取值 1:Initial 2:Update 3:Term 4:Event 5:EvtBack -1:error
    bool m_bUpdateAbnormalFlag;//update时是否带B03，有带则为异常该值为true
    //需要替换的字段集
    std::list<STD_STRING>* m_pParameter;
    //}@
};

struct T_OfflineRatingInfo
{
	long m_lnUnRatingMount;	//未扣量
	int  m_nUnit;						//	单位
	long m_lnUsedRatingMount;  //已经扣的量
	long m_lnAcctItem;
};
//rating & balance 输出处理数据
struct DCOutMsgDisposerData
{
    T_ratingExtMsg  RatingExtMsg;
    T_ratingExtMsg  RatingRsqMsg;   //实际的授权信息
    T_BalanceOutMsg balOutMsg;
    T_RatingOutMsg  ratingOutMsg;
    T_StartValue startValue; 
    //累计量信息保存 用于离线接口
    std::list<T_ratableInfo> m_ltRatableInfo;
	std::vector<T_OfflineRatingInfo> m_ltofflineRatingInfo;
	
    std::vector<T_BalanceContent>  vReal_fee;
	std::vector<T_BalanceContent>  vWriteAeer_fee;
    std::vector<T_BalanceContent>  vReq_fee;
    std::vector<T_rateInfo>        vRateInfo;
    std::vector<T_ratable>         m_vecRatable;
    T_FeeInfo                      m_feeInfo;
    std::vector<T_BalanceInfo>     m_vecBalInfo;
    std::vector<T_DeductInfo>      m_vecDeductInfo;
	std::vector<TariffInfo> m_TariffInfo;
    std::vector<T_FeeInfo>        m_VecfeeInfo;//多度量下的资费信息(销售品，费用)
    std::vector<long> m_VecPricingGroupId;
    long m_lnRatingDosage;
	int m_nMeasure;
    long m_nOfr_ID;	
    long m_nOfrInstID;
	long m_lnBillingDosage;

};

//session 信息
struct DCSessionInfo
{
    T_SessionInfo  sessionInfo;     //用于保存上次会话信息，数据查询用
    T_SessionInfo  sessionUpdate;   //用于保存本次会话信息，数据更新用
};


//信息增强相关
struct DCRBMsgEnhance
{
    
    //动态属性  
    DCServAcct*    m_pServAcct;                //用户账务关系
};


#endif


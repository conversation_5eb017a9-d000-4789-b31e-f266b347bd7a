/*******************************************
*Copyrights ? 2016，深圳天源迪科计算机有限公司
*					ocs项目组
*All rights reserved.
*
*Filename：
*		DCRentLoadUser.h
*Indentifier：
*
*Description：
*		月租用户数据加载类
*Version：
*		V1.0
*Author:
*		
*Finished：
*		
*History:
********************************************/
#ifndef __DC_COMBOADAPTPRDINST_H__
#define __DC_COMBOADAPTPRDINST_H__
#include <list>
#include <map>
#include <vector>
#include <string>
#include "DataDef.h"
#include "rentThreeUserInfoJson.h" 
#include "DCDBManer.h"
#include "DCLogMacro.h"
#include "publiclib.h"
#include "DCDataCenter.h"
#include "DCRDEventType.h"
#include "DCRentData.h"
#include "DCRentErr.h" 



//using namespace std;

//typedef map<long,DCEventTime>* T_MapDate;//DCDataCenter.h中已定义

struct ST_ShiftOfrInst
{
    int64_t lnOfrInstId;
    int64_t lnOfrId;
    int64_t lnSelectGroupID;
    std::vector<int64_t > vGroupId;
    int32_t nEventTypeId;
    int32_t nFeeType;
    int32_t nDynInstFlag;
    std::string szEffDate;
    std::string szExpDate;
    std::string szEventEffDate;
    std::string szEventExpDate;
    int32_t nCalcPriority;
	bool b7Cancel;
    int64_t lnPrdInstId;	
    ST_ShiftOfrInst() :
        lnOfrInstId(int64_t(0)),
        lnOfrId(int64_t(0)),
        lnSelectGroupID(int64_t(0)),
        vGroupId(std::vector<int64_t >()),
        nEventTypeId(int32_t(0)),
        nFeeType(int32_t(0)),
        nDynInstFlag(int32_t(0)),
        szEffDate(std::string()),
        szExpDate(std::string()),
        szEventEffDate(std::string()),
        szEventExpDate(std::string()),
        nCalcPriority(int32_t(0)),
        b7Cancel(int32_t(0)),
        lnPrdInstId(int64_t(0))
        { }
	bool operator <(const ST_ShiftOfrInst& rhs ) const
      {
		 bool bdown = true;
		 if ( nCalcPriority > rhs.nCalcPriority)  //按优先级从高到低排序
		 {
			 return bdown;
		 }
		 else if ( nCalcPriority == rhs.nCalcPriority )
		 {
		 	 if ( lnPrdInstId < rhs.lnPrdInstId )
			 {
				 return bdown;
			 }
			 else if (lnPrdInstId == rhs.lnPrdInstId)
			 {
				 if ( lnOfrInstId < rhs.lnOfrInstId )
				 {
					 return bdown;
				 }
				 else if (lnOfrInstId == rhs.lnOfrInstId)
				 {
					 if (lnOfrId < rhs.lnOfrId)
					 {
						 return bdown;
					 }
					 else if (lnOfrId == rhs.lnOfrId)
					 {
						 if (nEventTypeId < rhs.nEventTypeId)
						 {
							 return bdown;
						 }
						 else if (nEventTypeId == rhs.nEventTypeId)
						 {
							 if (strcmp(szEffDate.c_str(), rhs.szEffDate.c_str()) < 0)
							 {
								 return bdown;
							 }
							 else if ( szEffDate== rhs.szEffDate)
							 {
								 if (strcmp(szExpDate.c_str(), rhs.szExpDate.c_str()) < 0)
								 {
									 return bdown;
								 }
							 }
						 }
					 }
				 }
			 }
		 }
		 return false;
	  }

};

struct STCrossKey {
	set<long> stOfrInstList;
	set<long> stAcctIdList;
	vector<STSpecialAcctKey> stCrossAcctList;

	STCrossKey& operator+=(const STCrossKey& that)
	{
		stOfrInstList.insert(that.stOfrInstList.begin(), that.stOfrInstList.end());
		stAcctIdList.insert(that.stAcctIdList.begin(), that.stAcctIdList.end());
		stCrossAcctList.insert(stCrossAcctList.end(), that.stCrossAcctList.begin(), that.stCrossAcctList.end());
		return *this;
	}
};

class DCComboAdaptPrdInst
{
	public:	
		DCComboAdaptPrdInst();		
		~DCComboAdaptPrdInst();

		int init(DCDBManer* pDbm);
        void setInputPara(const STInputPara &inputPara,const STConfigPara &cfgpara);
        
		void releaseUserMap(bool isChangeAcct);
		int ComboAdapt(ocs::StRentMainInfo& tPrdInst, int nLatnId);
		int GetSubPrdInfo(long lnServId, int nLatnId, long lnAcctId);
		int getAllUserOfrfInstance(ocs::StRentMainInfo& tPrdInst,int nLatnId);
		bool filterOfrInstId(long lnOfrInstId,int nLatnId);		
		int QueryPlan(long lnOfrId,int& nOfferType,long& lnPlanId,string& strOfferName,int& nPriority);		
		int QueryOfrExtAttr(long lnOfrId,long lnAttrId,string& strDefaultValue);
		int QueryChargeStatusCd(string strCRMStatusCd,string& strChargetatusCd);
		int getPriority(long lnOfrId,int nLatnId);		
		int getPriorityFromTbOffer(long lnOfrId,int& tmp_priority);
		int GetOfrDetailInst(long RoleId,const char* RoleType,int nLatnId,long lnServId);
		int pushOfrInstWithCombineGroup(ST_ShiftOfrInst ofrInst, int nLatnId);
		int SetHeadInfo(int nLatnId,int nBillingCycleId,long lnAcctId,int nAcctType,T_UserInfo tUserInfo);		
		int getAllAcctOfrfInstance(long lnAcctId,int nLatnId,long lnLastServId);
		int GetOfrInstanceInfo(long lnOfrInstId,int nLatnId,long& lnOfrId);
		int GetOfrInstDetailInfo(long lnOfrInstId,int nLatnId);
		int GetDynOfrInst(long lnServId, int nLatnId );
		int CheckSpecialCrossAcct(int nLatnId, long lnOfrInstId, bool isNewOfrDealFlew ,long &lnSpecialId,set<long> &setOfrInstAcct);
		int SaveSpecialAcct(set<long> setAcctList, int nGroupId, int nLatnId,long lnOfrInstId);
		int LoadSpecialAcct(int nLatnId);
		bool InCompare(const char* vi_pszLeft, const char* vi_pszRight);
		int CheckSpecialBigAcct(long lnAcctId, int nLatnId, int nCurAcctPrdCount,long &lnSpecialId);
		bool LoadUserByAcctList(set<long> setAcctList,int nLatnId,long lnSpecialAcctId,int nAcctType,set<long> setFilterPrd);
		int LoadTransferPrdAcct(int nLatnId,vector<long>& vecInvaildTransfer);
		int LoadTransferPrdAcctDcfServ(int nLatnId,std::vector<string> &vecSQLFields,vector<long>& vecInvaildTransfer);
		int DeleteInvalidCrossAcct(const STSpecialAcctKey& stInfo,int nLatnId);
		bool CheckNewCrossOfrInst(const set<long>& setOfrInstAcct,const long& lnOfrInstId, long &lnSpecialId);
		int WaitNewCrossFinish(const set<long>& setOfrInstAcct,const long& lnOfrInstId,const int& nLatnId, long &lnSpecialId);
		int SaveMiddleCrossAcct(set<long> setAcctList, long lnOfrInstId, int nLatnId);
		int SaveSpecialBigAcct(long lnAcctId, int nGroupId, int nLatnId);
		int SaveSpecialCrossAcct(const long lnAcctId,const long lnOfrInstId, int nLatnId, bool bChange = false);
		int UpdateDealState(const long lnTransferId, const int nLatnID);
        bool FilterStatusCd(StPrdInstInfo& tmpPrdInfo,bool isTrial,bool isDCA);
		int loadOneAcctUser(int nLatnId,long lnAcctId,multimap<long,ocs::StRentMainInfo> &oneAcctUser);   		
		int Query7CancelOfrInstAttr(long lnOfferInstId, int nLatnId, long lnAttrId, bool& bFind);		
		int QuerySysParam(std::string strParmGroup,std::string strParmKey,std::string& strParaValue,bool& bFind);		
		int JudgeImmedAcct(long lnAcctId,int nLatnId,bool& bIsImmed);
	public:
		
		DCRDEventType* m_pEventType;
        
		ocs::UFmtHead		m_stFmtHead;
		ocs::IAcctHead		m_stAcctHead;
		 
		std::map<ST_ShiftOfrInst,int> m_multimapOfrInstPri;//存放套餐实例，用于排序
		std::map<STSpecialAcctKey,long> m_mapSpecialAcctId;  //保存TB_BIL_CROSS_ACCOUNT_[@]表中查出的大账户、超大账户、跨账户记录
		std::set<long> m_setOfrInstId;
		std::vector<long> m_vOfrInstId;
		std::vector<long> m_vtmpOfr;//存放中间主套餐为非A1的
		std::set<long> m_setA1OfrInstId;  //存放所有A1级的offer_inst_id(不限制offer_type是否为11)

		std::multimap<long,ocs::DetailInst> m_mmapOfrDetail;
		std::vector<ocs::OfrInst>      m_vecOfrInstOutput;
		std::multimap<long,STTransferAcctPrd> m_multimapTransferAcct;//账户变更信息，新账户+用户
		std::vector<long> m_vecGroupPrdInstId;//分组账户下所有用户实例ID

		bool m_isGetAcctOfrInst;//是否取过E1套餐		
		std::map<long,set<long> > m_mapModGroupAcct;//key为mod_group_id,value为acct_id集合
		
		std::map<long,STSpecialAcctInfo> m_mapSpecialAcctType;//特殊账户类型,以acctid为key，因为跨账户时acctid可能有多条，这里仅能保存一条，专用于获取账户分流信息
		std::multimap<long,ocs::StRentMainInfo> m_multimapAcctUser;  //保存账户下的所有用户
	private:

		DCDBManer* m_pDbm;

		int m_nBillingCycleId;
		std::vector<ocs::StRentMainInfo> 	m_vecOfrInstTmp;

		std::vector<ocs::DetailInst> m_vOfrDetailDyn;

		int m_nBigAcctThreshold;
		std::multimap<long,ocs::DetailInst> m_multimapOfrInstTmp;//存放套餐明细，用于判断跨账户

		int m_nGetDetail;
		std::map<long,ST_ShiftOfrInst> m_mapOfrInstTmp;//明细与实例KEY合并后用于存放实例信息

		int m_nMostBigAcctThreshold;
		int m_nContralGetOfrInst;
        int m_nProType;
		STConfigPara m_cfgpara;

		std::map<long,long> m_mapNewModGroup;//存需要变更的Mod

};
#endif


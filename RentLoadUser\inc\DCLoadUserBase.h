/****************************************************************************************
*Copyrights  2016，深圳天源迪科计算机有限公司
*						OCS项目组
*All rights reserved.
*
* Filename：	DCLoadUserBase.h		
* Indentifier：		
* Description：	用户加载基类
* Version：		V1.0
* Author:		zsh
* Finished：	2016年11月19日
* History:
******************************************************************************************/



#ifndef __DC_LOADUSERBASE_H__
#define __DC_LOADUSERBASE_H__

#include "DCLogMacro.h"
#include "DCDBManer.h" 
#include "DCRentErr.h" 
#include "DataDef.h" 
#include "TThread.h"
#include "DCRDEventType.h"
#include "DCComboAdaptPrdInst.h"
#include "DCCallZKServerMaker.h"
#include "DCConf.h"

class DCLoadUserBase: public TThread
{
	public:	
		DCLoadUserBase()
		{
		    p_dbm = NULL;
            m_pComboAdpt = NULL;
		    m_pairManager = NULL;
            m_pairList = NULL;
            m_uidCacheManager = NULL;
			m_nRunState = THREAD_AVAILABLE;
			m_bDcfServMode = false;
            m_nLatnId = 0;
	        m_nBillingCycleId = 0;
            m_nTaskState = THREAD_FREE;
		}

		virtual ~DCLoadUserBase()
		{
			Clear();
		}

		//判断数据库错误是否需要重连
		bool IfReConnect(int nErrorcode);
		//重连数据库
        int ReConnect();
		//初始化
		int InitBase(const STInputPara inputPara,const STConfigPara cfgPara,const int nProcId);
		int InitBase(const map<string, STConfigPara> &mapLatnCfg,const map<int,string> &mapLatnList, const int nProcId, const bool bNeedCall);

		//线程处理函数
		virtual void routine();
		//设置线程的运行控制状态
		void SetRunState(int nState);
		//获取线程的运行控制状态
		int GetRunState();
		pthread_t GetThreadNo();
		
		//处理函数入口，由子类实现
		virtual int process() = 0;
		virtual int processDcfs() = 0;
        
		//其他初始化函数入口，由子类实现
		virtual int InitSub() = 0;
		int setParams(const STInputPara inputPara);
		void Clear();

		void setStartMode(bool bMode) { m_bDcfServMode = bMode; }
        void SetTaskState(int nState);
        int GetTaskState();
        
	protected:
		
		STConfigPara m_cfgPara;//配置文件参数
		DCDBManer* p_dbm;//数据库链接
		
		STInputPara m_inputParam;//程序启动参数
		int m_nProcId;//线程号，用于sql分模
		int m_nRunState;//线程运行控制状态
		int m_nLatnId;
	    int m_nBillingCycleId;//此账期时间到天YYYYMMDD

        DCComboAdaptPrdInst* m_pComboAdpt;
        map<int,std::string> m_mapLatnList;
		map<std::string, STConfigPara> m_mapLatnCfg;
		map<std::string, DCCallZKServerMaker*> m_mappCallServer;
        DCPairManager *m_pairManager;
        DCPairList *m_pairList;
        DCUidCacheManager *m_uidCacheManager;

		bool m_bDcfServMode;//true统一任务分发前台分发任务；false原逻辑程序处理定时任务和SQL任务
		int m_nTaskState;
};
#endif



/*******************************************
*Copyrights 2005  深圳天源迪科计算机有限公司
*                   OCS项目组
*All rights reserved.
*
*Filename：
*       DCDataStruct.h
*Indentifier：
*       这里填入该文件的标识（参见软件配置管理）
*Description：
*       计费模块的结构
*Version：
*       V1.0
*Author:
*       lisg
*Finished：
*       2005年10月02日
*History:
*       
*       
********************************************/
#ifndef __DC_DATASTRUCT_H__
#define __DC_DATASTRUCT_H__

#include <map>
#include <string>
#include <list>

#include "DCRBMsgStruct.h"

using namespace std;


class DCRatingPSet;

//@{    IVPN各种资费类型使用宏
//I1：闭合群拨打群外（主叫，被叫）
#define                         GROUPTOOUT                                  "I1"
//F1：闭合群内
#define                         SELFGROUPIN                                 "F1"
//G1：缺省闭合群内
#define                         DEFGROUPIN                                  "G1"
//H1：闭合群间
#define                         AMONGGROUP                                  "H1"
//Q1：群组间
#define                         AMONGGROUPTEAM                              "Q1"
//J1：集团亲情号码
#define                         GROUPFAMILY                                 "J1"
//K1：集团间（即IVPN之间的拨打）
#define                         GROUPTOGROUP                                "K1"
//}@

// 1.IVPN 2.虚拟网 3.亲情号码 4.集团流程 5.普通优惠 6.基础
#define                 IVPN_OFR_TYPE                      1
#define                 NET_OFR_TYPE                        2
#define                 FN_OFR_TYPE                          3
#define                 VPN_OFR_TYPE                        4
#define                 FAV_OFR_TYPE                        5
#define                 BASE_OFR_TYPE                      6


//累计量信息
struct UPRatableInfo
{
public:
    long lnRatableID;
    long lnValue;
    long lnRatableType;
    long lnFee;//费用,以厘为单位
    bool operator == (const UPRatableInfo& r )const
    {
        if (this->lnRatableID == r.lnRatableID && this->lnRatableType == r.lnRatableType)
        {
            return true;
        }
        return false;
    }
};

struct stUpdateRatable
{
public:
	long lnRatableID;
	long nRatableType;

	stUpdateRatable()
	{
	    lnRatableID = -1;
	    nRatableType = 0;
	}
};

//返回给CM的B120组累计量信息
struct  stPresentCycleRatableInfo          
{
public:
    int nOfrId;    //销售品ID
    long lnRatableID;    //累计量标识
    long lnTotalValue;   //本月当前累计量总值
    long lnRatableType;    //累计量类型
    long lnPresentCycleValue;    //赠送值

    stPresentCycleRatableInfo()
    {
        lnRatableID = -1;
        lnTotalValue = 0;
        lnRatableType = 1;
        lnPresentCycleValue = -1;
        nOfrId = -1;
    }
};
class DCMultFeeType
{
public:
    DCMultFeeType()
    {
        m_nSingleEventID   = -1;
        m_nFeeType        = -1;
        m_nEventType      = 0;//
    }
    int m_nSingleEventID ;
    int m_nFeeType ;
    int m_nEventType;//是否为复合事件  0 复合事件 1单一事件
};
//返回给CM的B12组,本次免费使用信息
struct stPricingFreeInfo
{
    //免费剂量
    long lnFreeValue;

    //免费使用量的单位
    int nFreetype;

    stPricingFreeInfo()
    {
        lnFreeValue = 0;
        nFreetype = 0;
    }
};

//批价过程中最后一个段落批价信息
struct stPricingSectionInfo
{
    int lnPricingSectionId;
    int lnPricingSectionDisctId;
    int nTarrifee;
    int nTariffId;
	
    //批价过程中最后一个段落的资源类型，时长，次数等
    int m_nMeasure;
    int nOfrId;    //销售品ID
    int nPricingDiscSeq;    //定价段落打折免费所在的序列
    
    stPricingSectionInfo()
    {
        lnPricingSectionId = -1;
        lnPricingSectionDisctId = -1;
        nTarrifee = -1;
        nTariffId = -1;
        m_nMeasure = 0;
        nOfrId = -1;
        nPricingDiscSeq = -1;
    }
};

struct STBalanceInfo{
    long acct_balance_id;
    long free_balance;
    long use_order;
    string cycle_begin;
    string cycle_end;
    long cycle_upper;
    long cycle_lower;
    long cycle_used;
    long cycle_date;
    long acct_balance_obj_id;
    long balance;
    int spe_payment_id;
    int unit_type_id;
    int obj_type;
    long obj_id;
    long amount;
};

struct IUMValue
{
public:
    void Clear()
    {
        nRatingGroupId = 0;
        nR06 = 0;
        nR08 = 0;
        nR10 = 0;
        nR12 = 0;
        nR14 = 0;
        nR16 = 0;
        nR17 = 0;
        nR18 = 0;
        nR19 = 0;
        nR20 = 0;
    }
    
    int nRatingGroupId;
    int nR06;
    int nR08;
    int nR10;
    int nR12;
    int nR14;
    int nR16;
    int nR17;
    int nR18;
    int nR19;
    int nR20;
};

struct Fee
{
    long tariff;                        //计费费用
    long  acct_item_type_id;             //帐目类型
    int  unit_type;                     //单位类型  
};

struct stMoneyRatable
{
    long lnRatableId;               //累积量ID
    long lnUnUsedMount;             //多圆整的金钱 
};

struct TotalBalance
{   
    TotalBalance()
    {
        tariff = 0;
        pre_money = 0;
    }
    
    int acct_balance_id;            //账本id
    int acct_balance_latn_id;       //账本对应的latnId
    int acct_item_type_id;          //帐目类型
    int rate_unit_type;             //单位类型
    int tariff;                     //余额                   
    int pre_money;                  //可用余额
};

//ABM授权余额信息
class AuthorizeBalance
{
public: 
    AuthorizeBalance()
    {
        m_ltAcctItemType.clear();
        m_nUnitTypeId = 2;
        m_nDosage = 0;
        m_lnAmount = -1;
    }
    ~AuthorizeBalance()
    {
        m_ltAcctItemType.clear();
    }

public:
    list<int> m_ltAcctItemType; //本余额可以使用的账目类型列表,主要处理反算时有多个账目类型的情况,如IP国内长途
    int  m_nUnitTypeId;         //余额单位类型
    int m_nDosage;                    //实际的数量(时长或流量)
    long m_lnAmount;            //数量
};

struct PlanEvtStrategy
{
    int plan_id;
    int event_id;
    int strategy;
};

struct stStrategyIF
{
    int m_nStrategy_id;
    int m_nFeeType;
    long m_nPricingCombinGroupId;
    stStrategyIF & operator =(const stStrategyIF & st)
    {
        this->m_nFeeType = st.m_nFeeType;
        this->m_nStrategy_id = st.m_nStrategy_id;
        this->m_nPricingCombinGroupId = st.m_nPricingCombinGroupId;
        return *this;
    }
};
struct STRatableInfo
{
    STRatableInfo()
    {
        memset(szOwerType,0x0,sizeof(szOwerType));
        lnOwerID = -1;
        lnValue =0;
    }
    char szOwerType[4];
    long lnOwerID;
    long lnValue;//累积量的值
};

struct Ref_ValueSData
{
	int m_lnparam_id;
	int m_lnValue;
	//std::map<long,long> m_mapInstValueData;
};
struct RateInfo
{
    long acct_item_type_id;
    int rate_unit;
    //int rate_value;
    float rate_value;
    int rate_type; 
};


struct STServDisct
{
    int  disct_rule_id;
    int  group_flag;        //-1:不享受集团优惠 0:属于同一集团 1:不属于同一集团
    int  operator == (const STServDisct& right)
    {
        return (disct_rule_id == right.disct_rule_id);
    }
};

class PlanDisct
{
public:
    PlanDisct()
    {
        plan_id = 0;
        bDisct = false;
        bDerived = false;
        flag = -1;
        offer_id = -1;
        nGroupFlag=3;
        nFamilyNbrFlag = 0;
        product_offer_instance_id=-1;
        product_offer_detail_id = -1;
        object_type = 'C';
        element_type[0] = '\0'; 
        nOfrInstLatnId = -1;
        nDetailLatnId = -1;
        nSource_type = -1;
        memset(szInstEffDate,0x0,sizeof(szInstEffDate));
        memset(szInstExpDate,0x0,sizeof(szInstExpDate));
        nOfrDefineType = 0;
        inst_ref_id=0;
        inst_ref_type=0;
        nOfrInstLatnId=0;
        nDetailLatnId=0;
        bDerived=0;
        nIsTransition = 0;
        //memset(szEffDate,0x00,sizeof(szEffDate));
        memset(szOfrEffDate,0x00,sizeof(szOfrEffDate));
        nOfrType = 6;
        nMultFeeType = 0;
        m_ratable_ids.clear();
    }

    PlanDisct(const PlanDisct& r)
    {
        flag= r.flag;
        offer_id = r.offer_id;
        plan_id=r.plan_id;
        product_offer_instance_id=r.product_offer_instance_id;
        product_offer_detail_id=r.product_offer_detail_id;
        object_type =r.object_type;
        strcpy(element_type,r.element_type);
        bDisct = r.bDisct;
        nGroupFlag = r.nGroupFlag;
        nFamilyNbrFlag =r.nFamilyNbrFlag;
        m_ltStrategy= r.m_ltStrategy;
        nSource_type=r.nSource_type;
        strcpy(szInstEffDate,r.szInstEffDate);
        strcpy(szInstExpDate,r.szInstExpDate);
        nOfrDefineType = r.nOfrDefineType;
        inst_ref_id=r.inst_ref_id;
        inst_ref_type=r.inst_ref_type;
        nOfrInstLatnId=r.nOfrInstLatnId;
        nDetailLatnId=r.nDetailLatnId;
        bDerived=r.bDerived;
        m_ratable_ids=r.m_ratable_ids;
        nIsTransition = r.nIsTransition;
        //strcpy(szEffDate , r.szEffDate);
        strcpy(szOfrEffDate , r.szOfrEffDate);
        nOfrType = r.nOfrType;
        nMultFeeType = r.nMultFeeType;
    }

    /************************************************************************/
    /* 重载赋值运算 =                                                                     */
    /************************************************************************/
    PlanDisct& operator=(const PlanDisct& r)
    {
        flag= r.flag;
        offer_id = r.offer_id;
        plan_id=r.plan_id;
        product_offer_instance_id=r.product_offer_instance_id;
        product_offer_detail_id=r.product_offer_detail_id;
        object_type =r.object_type;
        strcpy(element_type,r.element_type);
        bDisct = r.bDisct;
        nGroupFlag = r.nGroupFlag;
        nFamilyNbrFlag =r.nFamilyNbrFlag;
        m_ltStrategy= r.m_ltStrategy;
        nSource_type=r.nSource_type;
        strcpy(szInstEffDate,r.szInstEffDate);
        strcpy(szInstExpDate,r.szInstExpDate);
        nOfrDefineType = r.nOfrDefineType;
        inst_ref_id=r.inst_ref_id;
        inst_ref_type=r.inst_ref_type;
        nOfrInstLatnId=r.nOfrInstLatnId;
        nDetailLatnId=r.nDetailLatnId;
        bDerived=r.bDerived;
        m_ratable_ids=r.m_ratable_ids;
        nIsTransition = r.nIsTransition;
        //strcpy(szEffDate , r.szEffDate);
        strcpy(szOfrEffDate , r.szOfrEffDate);
        nOfrType = r.nOfrType;
        nMultFeeType = r.nMultFeeType;
         m_nAccountFlag = 0;
        return *this;
    }

    bool operator == (const PlanDisct& r )const
    {
        if (this->offer_id == r.offer_id && this->plan_id == r.plan_id)
        {
            return true;
        }
        return false;
    }

    void Init()
    {
        plan_id = 0;
        bDisct = false;
        bDerived = false;
        flag = -1;
        offer_id = -1;
        nGroupFlag=3;
        nFamilyNbrFlag = 0;
        product_offer_instance_id=-1;
        product_offer_detail_id = -1;
        object_type = 'C';
        element_type[0] = '\0'; 
        nOfrInstLatnId = -1;
        nDetailLatnId = -1;
        nSource_type = -1;
        memset(szInstEffDate,0x0,sizeof(szInstEffDate));
        memset(szInstExpDate,0x0,sizeof(szInstExpDate));
        nOfrDefineType = 0;
        inst_ref_id=0;
        inst_ref_type=0;
        nOfrInstLatnId=0;
        nDetailLatnId=0;
        bDerived=0;
        nIsTransition = 0;
        nOfrType = 6;
        m_ratable_ids.clear();
        m_nAccountFlag = 0;
    }

    
    int flag;
    int offer_id;
    int plan_id;
    long product_offer_instance_id;
    int product_offer_detail_id;
    char object_type;
    char element_type[8];
    bool bDisct;    
    int nGroupFlag;         //0:网内, 1:网间, 2:VPN, 3:计费号码不在集团
    int nFamilyNbrFlag;     //0:非亲情号码  1:亲情号码
    list<stStrategyIF> m_ltStrategy; //定价计划和当前原始事件和派生事件对应的定价策略链表
    int nOfrInstLatnId;
    int nDetailLatnId;
    bool bDerived;          //销售品是否为参考对象和计算对象分开的
    std::list<UPRatableInfo>   m_ratable_ids;   //需要累积的累积量
    int nSource_type;
    ///dzw add
    long inst_ref_id;     //实例参考ID，可以是销售品实例明细ID、用户亲情号码ID、群关系ID
    char inst_ref_type;   //实例参考类型 G:IVPN 3 D:销售品实例明细 1 ，F：亲情号码 2   S：个性化实例 5 I：销售品实例 6 虚拟网 Q 4

   // std::map<long, long> strategy_inst_ref;  //“定价策略―个性化实例ID”
    //add end
    char szInstEffDate[24]; //实例明细的生效时间
    char szInstExpDate[24]; //实例明细的失效时间
    int nOfrDefineType;      //0:不参与最优资费1:参与最优资费
    int nIsTransition;         //是否过渡期
    //char szEffDate[24];//实例生效时间，包括集团优惠，普通优惠，个性化优惠用于动态月实例级别的
    char szOfrEffDate[24];//销售品级别的

    int nOfrType; // 销售品类型1.IVPN 2.虚拟网 3.亲情号码 4.集团流程 5.普通优惠 6.基础

    int nMultFeeType;//0 标识复合事件的 1标识单一事件的多度量表示，表示本销售品是否需要多度量批价，只要一个单一事件就可以走通的，则不需要
    int m_nAccountFlag;//是否有帐务优惠
};

/****************************************************************************************
*@class name        OperTypeItemStruct  
*@description       确定事件类的结构体，用以存放临时数据。
******************************************************************************************/
class OperTypeItemStruct
{
public:
    OperTypeItemStruct()
    {
        EventTypeId = 0;
        GoupId      = 0;
        ComType     = 0;
        ComOper     = 0;
        
        memset(ItemCode, 0, sizeof(ItemCode));
        memset(ItemValue, 0, sizeof(ItemValue));
        memset(BillingObj, 0, sizeof(BillingObj));
    }
    
    OperTypeItemStruct(const OperTypeItemStruct& obj)
    {
        EventTypeId = obj.EventTypeId;
        GoupId      = obj.GoupId;
        ComType     = obj.ComType;
        ComOper     = obj.ComOper;
        
        strcpy(ItemCode, obj.ItemCode);
        strcpy(ItemValue, obj.ItemValue);
        strcpy(BillingObj, obj.BillingObj);
    }
    
public:
    int     EventTypeId;
    int     GoupId;
    int     ComType;
    int     ComOper;
    
    char    ItemCode[64];
    char    ItemValue[64];
    char    BillingObj[4]; 
};

//定价段落输入参数
class DCPricingSectionInParam
{
public:
    DCPricingSectionInParam()
    {
        m_cBillingObject = '9';
        m_nGroupFlag = 3;
        m_lnOriCharge = 0;
              m_lnOriChargeLast = 0;
        m_nPricingPlanId = -1;
        m_nPrdInstId=-1;
        m_nSource_type = -1;
        memset(m_szInstEffDate,0x0,sizeof(m_szInstEffDate));
	m_nCellFlag = 0;
    }
    
    char m_cBillingObject;              //计费对象 1：主叫计费 2：被叫计费 3：三方计费
    //int  m_nPrdInstId;                    //商品实例ID 用户查找个性化策略
    long  m_nPrdInstId;                 //商品实例ID 用户查找个性化策略
    /***以下是参考累积量优惠相关参数***/
    list<stStrategyIF> m_ltStrategy;             //根据定价计划和事件类型查到的策略
    /***以下为优惠入口参数***/
    int  m_nGroupFlag;                  //集团标识：-1：不参与集团优惠 0：集团内 1：集团外
    long m_lnOriCharge;                 //一次批价费用,用于单条话单的优惠，如当前话单的原始费用达到10元以上，话单打8折优惠
    long m_lnOriChargeLast;           //流量费率切换点之后的使用量
    int  m_nPricingPlanId;               //优惠定价计划

   //由于累积量可以在不同销售品之间累积量ID相同，但累积量宿主类型不同，或者不同销售品之间累积量ID相同，但值不同
 //   map<int,long> m_mapRatable;     //累积量
    std::multimap<long,STRatableInfo> m_mapRatable;     //累积量
    int  m_nOfrInstLatnId;              //销售品实例的本地网
    int  m_nDetailLatnId;               //销售品明细本地网
    int  m_nSource_type;                //SID表引用标识
    char m_szInstEffDate[24];           //优惠实例明细生效时间
    int m_nCellFlag;
};


struct STCharge
{
    STCharge()
    {
        m_Fee = 0;  
        tariff = 0;
        dosage = 0;
        acct_item_type_id = 999;
        measure[0] = '\0';
        unit_type = -1;
        m_nOfr_id = -1;
        m_lnUnsedMondey = 0;
        m_cModeTail = '3';
        m_nPricingSectionId = -1;
    }
    
    long counts;                        //计费次数
    long tariff;                        //计费费用
    long dosage;                        //实际计费剂量
    long  acct_item_type_id;             //帐目类型
    long m_lnUnsedMondey;    //段落批价出的费用, 圆整费用与原始费用的差值
	
    int  m_nPresentAcctItemTypeId;
    int  m_nDiscount_id;
    int  unit_type;                     //单位类型
    int  m_Fee;                         //0-普通计算；1-赠送的
    int  tariff_id;//字符ID
    int m_nOfr_id;
    int  m_nPlan_id;//定价计划
    int m_nPricingSectionId;    //定价段落ID

    char measure[3];                    //计费资源
    char m_cModeTail;//金钱的圆整方式
};


//定价段落数据出参数
class DCPricingSectionOutParam
{
public:
    /****************************************************************************************
    *@input             
    *@output        
    *@return            
    *@description       初始化
    *@frequency of call 构造DCPricingSectionOutParam时调用
    ******************************************************************************************/ 
    DCPricingSectionOutParam()
    {
        m_lnTariff = 0;
        m_lnCounts = 0;
        m_lnDosage = 0;
        m_nAcctItemTypeId = 999;
        m_UnitType = 2;    //add by zhangpeng
        m_szMeasure[0] = '\0';
        m_bFreeFlag = true;
        m_nFreeValue = 0;
        m_nFreeType = 0;
        m_nPricingStrategyId = -1;
        m_nOfr_id = -1;
        memset(m_SpanTime,0x00,sizeof(m_SpanTime));
        nGroupFlag = 3;         //0:网内, 1:网间, 2:VPN, 3:计费号码不在集团
        nFamilyNbrFlag = 0;     //0:非亲情号码  1:亲情号码
        m_TmpStrategy.clear();
        m_vecPricingGroupId.clear();
		
        m_lnUnusedMoney = 0;
    }
    DCPricingSectionOutParam & operator +=( DCPricingSectionOutParam & ps)
    {
  
        this->m_lnCounts += ps.m_lnCounts;
        this->m_lnTariff += ps.m_lnTariff;

        //获取总资费
        list<STCharge>::iterator iter;
        for (iter = ps.m_ltCharge.begin(); iter != ps.m_ltCharge.end(); ++iter)
        {
            this->m_ltCharge.push_back(*iter);
        }
        //获取多余的量 
        if (this->m_lnUnuseDosage ==0 || this->m_RateUnit >ps.m_RateUnit)
        {
            this->m_lnUnuseDosage = ps.m_lnUnuseDosage;
            this->m_lnRealUsedRatingDosage = ps.m_lnRealUsedRatingDosage;
        }
        //设置本次批价总时长 保留一个大的
        if (this->m_lnRealUsedRatingDosage < ps.m_lnRealUsedRatingDosage)
        {
            this->m_lnRealUsedRatingDosage = ps.m_lnRealUsedRatingDosage;
        }
        //获取总的资费信息
        std::vector<T_rateInfo>::iterator iter_Rate;
        for (iter_Rate = ps.m_vecRateInfo.begin();iter_Rate!=ps.m_vecRateInfo.end();++iter_Rate)
        {
            this->m_vecRateInfo.push_back(*iter_Rate);
        }

        //获取每个定价计划需要累积的累积量
        std::list<UPRatableInfo>::iterator RatableIter;
        for (RatableIter = ps.m_ratable_ids.begin();RatableIter!=ps.m_ratable_ids.end();++RatableIter)
        {
            this->m_MultRatable_ids.insert(multimap<int ,UPRatableInfo> ::value_type(ps.m_Plan_ID.plan_id,*RatableIter));
        }
        ////设置本次免费的累积量
        //std::list<UPRatableInfo>::iterator RatableIter;
        //for (RatableIter = ps.m_ltRatableFree.begin();RatableIter!=ps.m_ltRatableFree.end();++RatableIter)
        //{
        //    this->m_ltMultRatableFree.insert(multimap<int,UPRatableInfo> ::value_type(ps.m_Plan_ID.offer_id,*RatableIter));
        //}
        //保存所有的定价计划
        std::vector<PlanDisct>::iterator tempiter;
        for (tempiter = this->m_TmpPlanDisct.begin();tempiter != this->m_TmpPlanDisct.end();++tempiter)
        {
            if (tempiter->plan_id == ps.m_Plan_ID.plan_id)
            {
                break;
            }
        }
        if (tempiter == this->m_TmpPlanDisct.end())
        {
            this->m_TmpPlanDisct.push_back(ps.m_Plan_ID);
        }
         //保存所有的批价事件
        std::vector<DCMultFeeType>::iterator EventIter;
        for (EventIter =this->m_TmpMultFeeType.begin(); EventIter != this->m_TmpMultFeeType.end();++EventIter)
        {
            if (EventIter->m_nSingleEventID == ps.tmpMultFeeType.m_nSingleEventID)
            {
                break;
            }
        }
        if (EventIter == this->m_TmpMultFeeType.end())
        {
            this->m_TmpMultFeeType.push_back(ps.tmpMultFeeType);
        }

        //保存所有的批价走过的最后一个段落
        std::list<stPricingSectionInfo>::iterator ltPricSectInfoiter = ps.m_ltPricingSectionInfo.begin();
        for(; ltPricSectInfoiter!=ps.m_ltPricingSectionInfo.end(); ltPricSectInfoiter++)
        {
            this->m_ltPricingSectionInfo.push_back(*ltPricSectInfoiter);
        }
        
		
        //保存费率切换点 按照小的时间上报
        if (strcmp(ps.m_SpanTime,"NO")!= 0 && (strcmp(this->m_SpanTime,"NO") == 0 || strcmp(this->m_SpanTime,ps.m_SpanTime)>0))
        {
            strcpy(this->m_SpanTime,ps.m_SpanTime);
        }
        //设置小的费率
        if (this->m_RateUnit < ps.m_RateUnit )
        {
            this->m_RateUnit = ps.m_RateUnit;
        }
        //设置VPN标识
        if (ps.nGroupFlag != 3)//打上标识，则需要保留
        {
            this->nGroupFlag = ps.nGroupFlag;
        }
        //设置亲情号码
        if (ps.nFamilyNbrFlag != 0)
        {
            this->nFamilyNbrFlag = ps.nFamilyNbrFlag;
        }

        //保存总策略
        std::vector<long>::iterator iter_Strategy;
        for (iter_Strategy = ps.m_TmpStrategy.begin();iter_Strategy!=ps.m_TmpStrategy.end();++iter_Strategy)
        {
            this->m_TmpStrategy.push_back(*iter_Strategy);
        }
        return *this;
    }

    /****************************************************************************************
    *@input             
    *@output        
    *@return            总的费用
    *@description       计算总的费用
    *@frequency of call 需要查询总的费用时调用
    ******************************************************************************************/ 
    long GetTotalCharge()
    {
        list<STCharge>::iterator it;
        long nRet = 0;
        for(it = m_ltCharge.begin();it != m_ltCharge.end();it++)
            nRet += it->tariff;
            
        return nRet;
    }
    void Clear()
    {
        m_ltCharge.clear();
        m_ratable_ids.clear();
        m_ltRatableFree.clear();
        m_vecRateInfo.clear();
        m_ltPricingSectionInfo.clear();
		
	 m_nPricingStrategyId = -1;
        m_lnUnuseDosage = 0;
        m_RateUnit = 0;
        m_lnRealUsedRatingDosage = 0;
        m_MultRatable_ids.clear();
        m_TmpMultFeeType.clear();
       // m_ltMultRatableFree.clear();
        m_TmpPlanDisct.clear();
        m_lnCounts = 0;
        m_nOfr_id = -1;
        nGroupFlag = 3;         //0:网内, 1:网间, 2:VPN, 3:计费号码不在集团
        nFamilyNbrFlag = 0;     //0:非亲情号码  1:亲情号码
        m_nFreeValue = 0;
        m_TmpStrategy.clear();
        m_vecPricingGroupId.clear();
        memset(m_SpanTime,0x00,sizeof(m_SpanTime));
        m_nHVPN = 0;
    }
    //费率信息先保存，与RBOutMsgFactory中的类似
    void SetRateInfo(int nBeginTime,int nType,int nUnit,float fValue,long lSectionId)
    {
        int nSize = m_vecRateInfo.size();
        for(int i=0; i < nSize;++i)
        {
            if(m_vecRateInfo[i].m_nRateBeginTime==nBeginTime 
                && m_vecRateInfo[i].m_nRateUnit==nUnit 
                && m_vecRateInfo[i].m_nRateType==nType
                && m_vecRateInfo[i].m_fRateValue==fValue
                && m_vecRateInfo[i].m_lSectionId == lSectionId)
            {
                return;
            }
        }

        T_rateInfo tmpRateInfo;
        tmpRateInfo.m_nRateBeginTime = nBeginTime;
        tmpRateInfo.m_nRateType      = nType;
        tmpRateInfo.m_nRateUnit      = nUnit;   
        tmpRateInfo.m_fRateValue     = fValue;
        tmpRateInfo.m_lSectionId     = lSectionId;
        m_vecRateInfo.push_back(tmpRateInfo);
        return;
    }

    int m_nEventType;//事件类型，0 复合事件 1 单一事件，单一事件，需要合并
    int m_nPricingStrategyId;       //批价成功的策略,包括最优资费的输出
    int  m_nPresentAcctItemTypeId;//无
    int  m_nDiscount_id;//无
    int  m_UnitType;                    //计费资源的单位
    int m_RateUnit;                     //费率
    //免费资源类型
    int m_nFreeType;//对应于数据库的类型
    //免费值，在一个策略下面，该值会累加
    int m_nFreeValue;
    int m_nOfr_id;
    int nGroupFlag;         //0:网内, 1:网间, 2:VPN, 3:计费号码不在集团
    int nFamilyNbrFlag;     //0:非亲情号码  1:亲情号码
    int m_nHVPN; 
	
    long m_lnCounts;                    //计费次数 无
    long m_lnTariff;                    //计费费用
    long m_lnDosage;                    //实际计费剂量
    long  m_nAcctItemTypeId;             //帐目类型  w无
    long m_lnUnusedMoney;//多圆整出的金钱。单位厘
    long m_lnUnuseDosage;		//没有使用的步长
    long m_lnRealUsedRatingDosage;//实际批价总计量

    char m_szMeasure[3];                //计费资源
    char m_SpanTime[24];//费率切换点m_pRatingSet->pRatingData->m_SpanTime
    
    bool m_bFreeFlag;                   //免费标识


    //记录批价过程中的最后一个段落信息，在这里定义对象
    //是解决最优资费批价取值错误
    stPricingSectionInfo  m_stPricingSectionInfo;
    DCMultFeeType tmpMultFeeType;
    PlanDisct m_Plan_ID;//当前等价计划


    list<STCharge> m_ltCharge;          //可能输出多种费用，定义为list  
    std::list<UPRatableInfo>   m_ratable_ids;   //需要累积的累积量
    
    //每个销售品批价走过的最后一个段落信息
    std::list<stPricingSectionInfo> m_ltPricingSectionInfo;
	
    //std::list<UPRatableInfo>   m_ratable_Not_Free;   //非免费更新累计量
    std::list<UPRatableInfo>   m_ltRatableFree;  //免费的累积量
    
    //用户暂时保存费率信息B20，因为最优资费的时候可能会出现多个B20
    std::vector<T_rateInfo>    m_vecRateInfo;
    std::multimap<int ,UPRatableInfo> m_MultRatable_ids;   //需要累积的累积量(定价计划，累积量)
//    std::multimap<int ,UPRatableInfo> m_ltMultRatableFree;//多度量情况下，免费累积量的使用情况（销售品ID，累积量ID）
    
    std::vector<PlanDisct>  m_TmpPlanDisct;     //用于保存批价信息
    std::vector<DCMultFeeType> m_TmpMultFeeType;
    std::vector<long> m_TmpStrategy;
    std::vector<long> m_vecPricingGroupId;  //定价组ID
	
};

//实体pricing_section
struct  STPricingSection
{
    STPricingSection()
    {
        m_nPricingSectionId = 0;
        m_nTariffId         = 0;
        m_nEventPricingStrategyId   = 0;
        m_nSectionType      = 0;
        m_szSectionCalcType[0]      = '\0';
        m_nParentSectionId          = 0;
        m_nPricingRefObject         = 0;
        m_nZoneItemId               = 0;
        m_nAcctItemTypeId           = 0;
        m_nPresentAcctItemTypeId    = 0;
        m_nDiscount_id                  = 0;
        m_nConditionId              = 0;
        m_szMeasureDomain[0]        = '\0';
        m_nCycleSectionBegin        = 0;
        m_nCycleSectionDuration     = 0;
        m_nCalcPriority             = 0;
        m_szPricingType[0]          = '\0';
        start_ref_value[0]          = '\0';
        end_ref_value[0]            = '\0';
        resource_code_id = 0;
        m_UnitType                  = 2;    //add by zhangpeng,金钱类型默认为钱
        FeeType = -1;
        m_cTailMode = '3';
        //m_szPricingSectionName[0] = '\0';
    }
    
    int  m_nPricingSectionId;
    int  m_nTariffId;
    int  m_nEventPricingStrategyId;
    int  m_nSectionType;
    int  m_nParentSectionId;
    int  m_nPricingRefObject;
    int  m_nZoneItemId;
    int  m_nPresentAcctItemTypeId;
    int  m_nDiscount_id;
    int  m_nConditionId;
    int  m_nCycleSectionBegin;
    int  m_nCycleSectionDuration;
    int  m_nCalcPriority;
    int resource_code_id;
    int m_UnitType;
    int FeeType;

    long  m_nAcctItemTypeId;
    
    char m_szSectionCalcType[4];
    char m_cTailMode;                 //段落最后结果的位数处理模式 1:向下圆整 2:向上圆整 3:四舍五入圆整
    char m_szPricingType[2];
    char start_ref_value[14];
    char end_ref_value[14];
    char m_szMeasureDomain[4];

};


/*
    标准资费入口参数
*/
class DCTariffInParam
{
public:
	DCRatingPSet* m_pRatingSet;        //新增数据成员，调用DCPricingInstParam接口获取参数的具体值
	int m_nTariffId;            //标准资费标识
	int m_nRefObject;           //定价参考对象 1：按度量单位 2：按事件的开始时间
       char m_cTailMode;           //资费结果的位数处理模式 1:向下圆整 2:向上圆整 3:四舍五入圆整    
	char m_szMeasure[7];        //计费资源 01：时长 02：流量  03：按次 04：上行流量 05：下行流量
	long m_lnDosage;            //计费剂量
	long m_lnDosageLast;        //流量切换点之后的使用量
	char m_szStartTime[16];     //通话开始时间 YYYYMMDDHHDDSS
	char m_szValidityTime[16];  //Validity-Time，RB根据当前实践+Validity-Time是否跨越费率切换点来判断是否返回费率切换点（时间点）给CM。 单位是秒。
	long m_lnStartValue;//之前的计费量

	DCTariffInParam()
	{
		m_nTariffId = -1;
		m_nRefObject = -1;
		m_lnDosage = 0;
		m_lnDosageLast =0;
		m_lnStartValue = 0;
		memset(m_szMeasure,0,sizeof(m_szMeasure));
		memset(m_szStartTime,0,sizeof(m_szStartTime));
		memset(m_szValidityTime,0,sizeof(m_szValidityTime));
	}
};

/*
    标准资费出口参数
*/
class DCTariffOutParam
{
public:
    long m_lnCounts;            //计费次数
    long m_lnTariff;            //计费费用
    long m_lnDosage;            //实际计费剂量
    long m_lnUnuseDosage;		//没有使用的步长
 //   int m_RateUnit;             //费率

    //圆整后多出或者少出来的费用,
    long m_lnUnusedMoney;
};

/*
    标准资费实体
*/
class STTariff
{
public:
    STTariff()
    {
        start_ref_value[0] = 0;
        end_ref_value[0] = 0;
        date_type[0] = 0;
        disct_value = 100;
        disct_value_base = 100;
	 nTariffID = 0;
	 m_lstResourceId.clear();
    }
    
    char start_ref_value[32];
    char end_ref_value[32];     
    long rate_unit;             //计费单元
    long scaled_rate_value;     //费率 单位为粒
    long min_counts;            //最小单元次数 
    char tail_mode;             //尾数模式 1:向下圆整 2:向上圆整 3:四舍五入
    long  disct_value;           //折扣率，如15%的折扣率为15
    long  disct_value_base;      //折扣率基数，如15%的折扣率基数为100，
    char date_type[8];
    int  holiday_id;            //节假日ID
    //2008-9-25 modify by fanbin
    //int resource_id;//累计量ID
    //modify by zhangpeng ,更新累计量可以配置多组,解决"双限需求"
    list<long> m_lstResourceId; 
    
    //2008-9-25 modify by fanbin
    int nTariffID;

	char Oper_Switch; //0 原来的流程 N走定价计划
	long Fixed_Rate_Value;
};

//add by zhangpeng at 2009.8.20 --------赠送资源信息结构
class DCPresentResource
{
public:
    DCPresentResource()
    {
        lnPricingSectionId = 0;
	 lnPricingSectionDisctId = 0;
        lnResourceId = 0;
	 nSectionDisctSeq = 0;	
        nResourceUnit = 0;
	 nPresentValue = 0;	
    }

    //段落ID
    int lnPricingSectionId;

    //段落打折ID
    int lnPricingSectionDisctId;

    //累计量ID
    long lnResourceId;
	
    //段落打折序列
    int nSectionDisctSeq; 

    //累计量单位
    int nResourceUnit;

    //该累计量对应的赠送值
    int nPresentValue;
    
};

class DCRateConditionInParam
{
public:
    /****************************************************************************************
    *@input             
    *@output        
    *@return        
    *@description       初始化时间信息
    *@frequency of call 创建一个DCRateConditionInParam的实例时
    ******************************************************************************************/
    DCRateConditionInParam()
    {
        memset(m_szYYYY,0,sizeof(m_szYYYY));
        memset(m_szMM,0,sizeof(m_szMM));
        memset(m_szDD,0,sizeof(m_szDD));    
        memset(m_szHH,0,sizeof(m_szHH));
        memset(m_szMI,0,sizeof(m_szMI));
    };

    int m_nMeasure; //段落类型

    DCRatingPSet* m_pRatingSet;        //新增数据成员，调用DCPricingInstParam接口获取参数的具体值
    std::multimap<long,STRatableInfo> m_mapRatable;      //累积量
    map<string,string> m_mapServAttr;   //用户静态属性
    char m_cBillingObject;              //计费对象 1：主叫计费 2：被叫计费 3：三方计费
    int  m_nGroupFlag;                  //集团标识：-1：不参与集团优惠 0：集团内 1：集团外      
    long m_lnOriCharge;                 //一次批价费用,用于单条话单的优惠，如当前话单的原始费用达到10元以上，话单打8折优惠
    int  m_nBillingFlag;                //会话状态，1:Inital 2:Update 3:Term 4:Event 5:EventBack
    char m_szYYYY[8];
    char m_szMM[4];
    char m_szDD[4]; 
    char m_szHH[4]; 
    char m_szMI[4]; 
    long m_lnLimitDosage;//限制额，比如20G受限，则本次授权的m_lnlimitDosage为多少
    int  m_nlimitType;
    char m_szLimitCode[12];//限制码

    long m_RatableMoney;   //封顶业务累计消费的金额,add by 20110905

    int m_nHVPN;
	int m_nCellFlag;
};

//实例参数
struct DCInstParamStruct
{
public:
    DCInstParamStruct()
    {
        inst_ref_id =0;
        param_id=0;
        memset(param_value,0,sizeof(param_value));
        memset(param_code,0,sizeof(param_code));
    }
    ~DCInstParamStruct()
	{

	}
    DCInstParamStruct(const DCInstParamStruct& oldDCInstParamStruct)
    {
        inst_ref_id = oldDCInstParamStruct.inst_ref_id;
        param_id = oldDCInstParamStruct.param_id;
        strcpy(param_value,oldDCInstParamStruct.param_value);
        strcpy(param_code,oldDCInstParamStruct.param_code);
    }
    long inst_ref_id;            //表TB_PRD_INST_PARAM中的REF_ID
    long param_id;               //参数标识
    char param_value[256];       //参数具体值
    char param_code[256];        //关联RECORD_TYPE_CODE. RECORD_TYPE 
};


//---------------------------------------------------------------------------------

//-------------------------累计量模块---------------------------------------------

/****************************************************************************************
*@class name        DCPresentIn 
*@description       用以存放赠送费用处理的输入数据。
******************************************************************************************/
class DCPresentIn
{
public:
    DCPresentIn()
    {
        area_code[0]    = '\0';
        acc_nbr[0]      = '\0';
        event_type_id   = 0;
        before          = 0;
    }
    
    void Clear()
    {
        area_code[0]    = '\0';
        acc_nbr[0]      = '\0';
        event_type_id   = 0;
        before          = 0;    
    }
    
    char area_code[16];         //区号
    char acc_nbr[16];           //号码
    int event_type_id;          //事件
    int before;                 //已经占用了多少个单位
};


/****************************************************************************************
*@class name        DCPresentOut    
*@description       用以存放赠送费用处理后的输出数据。
******************************************************************************************/
class DCPresentOut
{
public:
    DCPresentOut()
    {
        acct_item_type_id   = 0;
        unit_type_id        = 0;
        value               = 0;
        left                = 0;
        offer_id            = 0;
    }
    
    void Clear()
    {
        acct_item_type_id   = 0;
        unit_type_id        = 0;
        value               = 0;
        left                = 0;
        offer_id            = 0;
    }
    
    int acct_item_type_id;      //帐目类型
    int unit_type_id;           //单位类型
    int value;                  //现在申请可用的单位数目
    int left;                   //申请之后剩余的可用单位数目
    int offer_id;               //产品id
};
//多度量类型,用来保存多度量事件类


class DCRatableResourceConditionInParam
{
public:
    /****************************************************************************************
    *@input             
    *@output        
    *@return        
    *@description       初始化时间信息
    *@frequency of call 创建一个DCRatableResourceConditionInParam的实例时
    ******************************************************************************************/
    DCRatableResourceConditionInParam()
    {
        memset(m_szYYYY,0,sizeof(m_szYYYY));
        memset(m_szMM,0,sizeof(m_szMM));
        memset(m_szDD,0,sizeof(m_szDD));    
        memset(m_szHH,0,sizeof(m_szHH));
        memset(m_szMI,0,sizeof(m_szMI));            
    }

    DCRatingPSet* m_pRatingSet;        //新增数据成员，调用DCPricingInstParam接口获取参数的具体值
    map<int,long> m_mapRatable;      //累积量
    map<string,string> m_mapServAttr;   //客户静态属性
    char m_cBillingObject;              //计费对象 1：主叫计费 2：被叫计费 3：三方计费
    long m_lnTariff;                    //费用      
    int  m_nGroupFlag;                  //集团标识：-1：不参与集团优惠 0：集团内 1：集团外  
    char m_szYYYY[8];
    char m_szMM[4];
    char m_szDD[4]; 
    char m_szHH[4]; 
    char m_szMI[4];         
};


typedef struct
{
    char ratable_resource_code[4];
    int  group_id;
    int  group_seq;
    char com_type[3];
    char record_type[4];
    int  com_operators;
    char item_value[121];
}STRATABLERESOURCECONDITION;

typedef struct 
{
    char ratable_resource_code[4];      //累计量代码
    char ratable_resource_type;         //累计量类型
    char ratable_date_type[3];          //累计量日期类型
    char can_be_negative;               //1:允许为负 0:不允许为负
}STRATABLERESOURCE;


//累计量信息表－－－用于累计量赠送的输入信息
struct RatableInfo
{
public:
    char ratable_resource_code[4];      //累计量代码
    char billing_cycle_id[8];           //帐期ID，可以为－1（针对历史的） 
    long value;                         //目前累计量的值
    long present;                       //目前针对累计量应赠送的单位数目
    int pricing_plan_id;                //定价计划
    char serv_id[13];                   //serv_id
};


//累计量信息表－－－用于累计量赠送的输出信息
struct RatablePresent
{
public:
    int lower_value;                    //下限
    int upper_value;                    //上限
    char flag;                          //分循环的方式和跨段的方式1、循环：每100条赠送2条；2、跨段：在0～xxx 赠送多少
    int acct_item_type_id;              //帐目类型
    int unit_type_id;                   //赠送的单位类型，1-秒（时长）；2-分（金额）；3-kb总流量；4-kb上行流量；5-kb下行流量；6-次数
    int present_value;                  //赠送的数量（总的），例如：在0――100条，送4条；在101－200条，送10条。这里10条已经包含了第一段的4条。如果是循环的方式，这里就是2

};



struct RatableCodeType
{
public:
    string ratable_code;                //累计量代码
    char ratable_type;                  //累计量类型
    char can_be_negative[4];            //是否可以冲减
    char usage_type[4];                 //使用方式
};

struct STBaseRatable 
{
    int nRatable_resource_code_id;  //主累计量代码
    char szResource_owner_type[4];   //累计量属主类型
    int  nBase_life_type;                   //日期类型  0：按天 1：按帐期
    int  nStart_value;                        //开始偏移
    int  nEnd_value;                          //结束偏移
    int  nRatable_resouce_type;  //累积量类型 1－时长(秒)； 2－时长(分钟)；3－次数；4－总流量(k)；5－分(金额)；7－上行流量按K；8－下行流量
    int  nLife_type;                     //日期类型  0：按天 1：按帐期
    int  nRef_offset;                   //参考偏移
    char szRef_type[4];              //参考类型
    long nRatable_Cycle_Type_id;         //累计周期类别标识
    
    STBaseRatable()
    {
        memset(szResource_owner_type,0,sizeof(szResource_owner_type));
        memset(szRef_type,0,sizeof(szRef_type));
    }
};

//用于保留发送到ABM查询累计量的信息.
struct STRatableQueryReq
{
    long lnOwnerId;                     //累积量属主标识
    char OwnerType[4];                  //0:lnOwerId acct_id  1:lnOwerId serv_id  2:lnOwerId cust_id
    long lnRatable_resource_code;       //累积量代码
    long lnBilling_cycle_id;            //帐期ID，可以为－1（针对历史的）  
    long lnPlan_id;//定价计划ID
    long lnOfrInstId; 
};

//用于保留收到到ABM查询累积量应答的信息.
struct STRatableQueryRsp
{
    long lnOwnerId;
    char OwnerType[4];                  //0:lnOwerId表示acct_id  1:lnOwerId表示serv_id  2:lnOwerId表示cust_id
    long lnRatable_resource_code;       //累积量代码
    long lnBilling_cycle_id;            //帐期ID，可以为－1（针对历史的）
    long lnRatable_balance;               //累积量数量
};

//用于保留发送到ABM更新累计量的信息.
struct STRatableUpdateReq
{
    long lnOwnerId;                     //累积量属主标识
    char OwnerType[4];                  //0:lnOwerId表示acct_id  1:lnOwerId表示serv_id  2:lnOwerId表示cust_id
    long lnRatable_resource_code;       //累积量代码
    long lnBilling_cycle_id;            //帐期ID，可以为－1（针对历史的）
    long lnRatable_balance;               //累积量数量
    long nRatable_resouce_type;         //累积量类型
    long lnOfrInstId;
    long lnOfrId;
    long lnServId;  //填写prd_inst_id
    long lnRatableTotalValue;

    STRatableUpdateReq()
    {
        lnRatableTotalValue = -1;
    }
};

//自定义账期类型累积量
struct STRatableCycle
{
	STRatableCycle()
	{
		nRatable_Cycle_Type_id = 0;	
		nRatable_Cycle_Id = 0;

		memset(sCycle_Begin_Date,0x00,sizeof(sCycle_Begin_Date));
		memset(sCycle_End_Date,0x00,sizeof(sCycle_End_Date));
	}

	long nRatable_Cycle_Type_id;
	long nRatable_Cycle_Id;	
	char sCycle_Begin_Date[24];
	char sCycle_End_Date[24];
};

struct RatableResource
{
	RatableResource()
	{
		life_type[0] = 0;
		ref_type[0] = 0;
		resource_owner_type[0] = 0;
	}

	char life_type[4];
	char ref_type[4];
	int  ref_offset;
	int  usage_type;
	int base_resource_code_id;       //主累积量标识
	char ratable_resource_type[4];    //累积量类型标识  1－时长(秒)； 2－时长(分钟)；3－次数；4－总流量(k)；5－分(金额)；7－上行流量按K；8－下行流量 
	char resource_owner_type[4];      //累积量属主标识 80A用户、80I客户、80J账户
	long nRatable_Cycle_Type_id;         //累计周期类别标识
};




//用于保留收到ABM更新累积量应答的信息.
struct STRatableUpdateRsp
{
    long lnOwnerId;
    char OwnerType[4];                  //0:lnOwerId表示acct_id  1:lnOwerId表示serv_id  2:lnOwerId表示cust_id
    long lnRatable_resource_code;       //累积量代码
    long lnBilling_cycle_id;            //帐期ID，可以为－1（针对历史的）
    long lnRatable_balance;               //累积量数量
};

//用于保留收到ABM更新累积量应答的信息.
struct STStrategyInstID
{
    long lnTemp_inst_ref_id;     //实例参考ID，可以是销售品实例明细ID、用户亲情号码ID、群关系ID
    char cTempinst_ref_type;   //实例参考类型 G:IVPN 3 D:销售品实例明细 1 ，F：亲情号码 2   S：个性化实例 5 I：销售品实例 6 虚拟网 Q 4

};

//------------------------------累计量模块-----------------------------------------

struct STBalanceQueryRsp
{
    long m_lnAcctBalanceID;             // 账本标识
    int m_nBalanceUnitType;             // 账本类型
    long m_lnBalance;                   // 账本余额
    long m_lnAcctItemId;                // 账目类型
    STBalanceQueryRsp()
    {
        m_lnAcctItemId = 0;
    }
};//DCDataStruct.h

struct ExpiredOfr
{
	long lnRenewId;
	long lnPrdInstId;
	long lnOfrId;
	long lnOfrInstId;
	long lnAcctId;
	int nLatnId;
	long lnOfrDetailInstId;
	int nSourceType;
	long lnCustId;
	long lnPrdId;
	char szDataSource[2];
	char szServiceNum[24];
	
	ExpiredOfr()
	{
		lnRenewId = -1;
		lnPrdInstId = -1;
		lnOfrId = -1;
		lnOfrInstId = -1;
		lnAcctId = -1;
		nLatnId = -1;
		lnOfrDetailInstId = -1;
		nSourceType = -1;
		lnCustId =-1;
		lnPrdId= -1;
		memset(szDataSource,0,sizeof(szDataSource));
		memset(szServiceNum,0,sizeof(szServiceNum));
	}
	
};



//------------------------------定价段落-------------------------------------------

//段落关系输入参数
class DCPricingSectionRelationInParam
{
public:
    int  m_nEventPricingStrategyId;     //定价策略标识
    int  m_nPricingSectionId;           //定价段落标识
    int m_nOfr_id;
    int  m_nPlan_id;//定价计划
    int  m_nPresentAcctItemTypeId;
    int  m_nDiscount_id;    
    int  m_unit_type;                   //计费资源的单位
    
    long m_lnCounts;                    //计费次数
    long m_lnTariff;                    //计费费用
    long m_lnDosage;                    //实际计费剂量
    long  m_nAcctItemTypeId;             //帐目类型
    long m_nTariffId;
    long m_lnUnsedMoney;		//保持对圆整出的钱

    char m_szMeasure[3];      //计费资源
    char m_cModeTail;           //金钱的圆整方式
    
};

//段落关系输出参数
class DCPricingSectionRelationOutParam
{
public:
    list<STCharge>  m_ltCharge;
    
    /****************************************************************************************
    *@input             
    *@output        
    *@return            总费用
    *@description       获取总费用
    *@frequency of call 需要获取总费用时调用
    ******************************************************************************************/
    long GetTotalCharge()
    {
        list<STCharge>::iterator it;
        long nRet = 0;
        for(it = m_ltCharge.begin();it != m_ltCharge.end();it++)
            nRet += it->tariff;
        return nRet;        
    }
};


//段落关系实体
typedef struct
{
    char relation_type[4];      //定价段落间的关系
    char section_list[640];      //定价段落列表
    int  event_pricing_strategy_id; //事件定价策略标识
}STPricingSectionReleation;

//------------------------------定价段落-------------------------------------------

//------------------------------封顶优惠 begin---------------------------------
class CheckTopValueInParam
{
public:
    long lnPrd_inst_id;     //主产品实例id
    int  nLatn_id;          //本地网标识
    int  nBilling_cycle_id;    //自然月
    char szProduct_id[30];  //ISMP订购产品ID
    long lnBalance;         //查询累积金额
    long lnUpdate_balance;  //需要更新的金额
    char szService_nbr[20]; //用户号码
    char szArea_code[10];   //用户归属区号

    int nCtrlType;      //控制类型1:查询无记录，需要插入 0:查询有记录，需要更新 2：查询有记录,不需要更新

    CheckTopValueInParam()
    {
        lnPrd_inst_id   = -1;
        nLatn_id        = -1;
        nBilling_cycle_id = -1;
        lnBalance       = -1;
        lnUpdate_balance = -1;
        nCtrlType       = -1;
        memset(szProduct_id,0,sizeof(szProduct_id));
        memset(szService_nbr,0,sizeof(szService_nbr));
        memset(szArea_code,0,sizeof(szArea_code));
    }

    void Clear()
    {
        lnPrd_inst_id   = -1;
        nLatn_id        = -1;
        nBilling_cycle_id = -1;
        lnBalance       = -1;
        lnUpdate_balance = -1;
        nCtrlType       =-1;
        memset(szProduct_id,0,sizeof(szProduct_id));
        memset(szService_nbr,0,sizeof(szService_nbr));
        memset(szArea_code,0,sizeof(szArea_code));
    }
};
//------------------------------封顶优惠 end---------------------------------


//------------------------------包月剔重 begin---------------------------------
class CheckRepetitionInParam
{
public:
    long lnPrd_inst_id;     //主产品实例id
    int  nLatn_id;          //本地网标识
    int  nBilling_cycle_id;    //自然月
    char szProduct_id[30];  //ISMP订购产品ID
    long lnBalance;         //查询累积金额
    char szService_nbr[20]; //用户号码
    char szArea_code[10];   //用户归属区号
    int  nCtrlType;         //类型 0:没有记录需要插入，1:记录已经存在，不需要插入

    CheckRepetitionInParam()
    {
        lnPrd_inst_id   = -1;
        nLatn_id        = -1;
        nBilling_cycle_id = -1;
        lnBalance       = -1;
        nCtrlType       = -1;
        memset(szProduct_id,0,sizeof(szProduct_id));
        memset(szService_nbr,0,sizeof(szService_nbr));
        memset(szArea_code,0,sizeof(szArea_code));
    }

    void Clear()
    {
        lnPrd_inst_id   = -1;
        nLatn_id        = -1;
        nBilling_cycle_id = -1;
        lnBalance       = -1;
        nCtrlType       =-1;
        memset(szProduct_id,0,sizeof(szProduct_id));
        memset(szService_nbr,0,sizeof(szService_nbr));
        memset(szArea_code,0,sizeof(szArea_code));
    }
};
//------------------------------封顶优惠 end---------------------------------

#endif



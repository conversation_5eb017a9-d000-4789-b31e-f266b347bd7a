/*******************************************
*Copyrights   2007，深圳天源迪科计算机有限公司
*                   技术平台项目组
*All rights reserved.
*
*Filename：
*       DCPairManager.h
*Indentifier：
*
*Description：
*       序列值对超时处理线程
*Version：
*       V1.0
*Author:
*       YF.Du
*Finished：
*       2008年10月10日
*History:
*       2008/10/10  V1.0 文件创建
********************************************/
#ifndef _DC_PAIR_MANAGER_H_
#define _DC_PAIR_MANAGER_H_

#include <unistd.h>
#include <stdio.h>
#include <sys/time.h>
#include "TThread.h"
#include "DCPairList.h"

//#include "ace/OS.h"
//#include "ace/Signal.h"
//#include "ace/Condition_T.h"

#include "DCLogMacro.h"


class DCPairManager : public TThread
{
	public:

		/****************************************************************************************
		*@input
				pairList : 序列号值对队列
				timeOut : 超时时间(ms)

		*@output

		*@return

		*@description		构造函数
		******************************************************************************************/
		DCPairManager();

		~DCPairManager();

		int init(DCPairList *pairList,  unsigned long timeOut);

	private:

		void routine();

		void DealTimeOut(const SMsgPair msg);

	private:

		DCPairList		*m_pairList;		//序列号值对队列
		unsigned long	m_timeOut;		//超时时间(us)
};

#endif

#---------Common Sets for sub makefiles-----------------------#
#path for ALL
LBSALLROOT:=
#path for CMN_CODE
export LBSPUBROOT=/public/ocs_ah/src_cloud
export CMN_ROOT=/public/ocs_ah/CMN_CODE
#release version, 0-release; 1-debug
RELEASE_VERSION=1
#dependency files, 0-enable; 1-disable
DEPFLAG=1
#--------------------------------------------------------------#
#platform specific
PlatForm=$(shell uname -s)
# for base commonds
CPP_TUXEDO:=$(TUXDIR)/bin/buildclient -w
SHELL:=sh
TESTTOOL:=test
MAKE:=gmake

#Linux default gcc
ifeq ("$(PlatForm)","Linux")
CCDEP:=gcc -MM
#CC:=g++ -Wall -m64
CC:=g++  -m64
AR:=ar r

DEBUG_CXXFLAG:= -g -fPIC
DEBUG_DLLFLAG:= -g -shared
DEBUG_LDFLAG:=-g

NDEBUG_CXXFLAG:=-O2 -fPIC
NDEBUG_DLLFLAG:=-shared
NDEBUG_LDFLAG:=-O2 


endif	#end for Linux

#HP-UX default aCC
ifeq ("$(PlatForm)","HP-UX")
CCDEP:=aCC -E +Maked
CC:=aCC +DD64 -AA +W1016,1031 -mt -w
AR:=ar r

DEBUG_CXXFLAG:=-g
DEBUG_DLLFLAG:=-b +z
DEBUG_LDFLAG:=-g -Wl,+s

NDEBUG_CXXFLAG:=-O2
NDEBUG_DLLFLAG:=-b +z
NDEBUG_LDFLAG:=-O2 -Wl,+s

BASEFLAG:=-D_HP_ -DBITS64 -D_HPUX_11  -DHPUX_aCC -DACE_LACKS_SELECT -DINCLUDE_FSTREAM -D_REENTRANT -D_KERNEL_THREADS -D_THREAD_SAFE -DTHREAD -D_VIS_LONG_LONG -D_VIS_LONG_DOUBLE -D_VIS_UNICODE -D_VIS_STREAM_WCHAR -D_VIS_NO_IOSTREAM_WCHAR -D_VIS_NO_IOSTREAM_LONGDOUBLE -D_VIS_STD -DACE_HAS_THREADS -D_HPUX_SOURCE -DHPUX_VERS=1123 -DACE_LACKS_PRAGMA_ONCE -DACE_HAS_EXCEPTIONS -D__ACE_INLINE__ -DACE_LACKS_ASSERT_MACRO 

endif	#end for HP-UX

#AIX default xlC_r
ifeq ("$(PlatForm)","AIX")
CCDEP:=xlC_r -MM
CC:=xlC_r -Wall -m64
AR:=ar r

DEBUG_CXXFLAG:=-g
DEBUG_DLLFLAG:=
DEBUG_LDFLAG:=-g -Wl,+s

NDEBUG_CXXFLAG:=-O2
NDEBUG_DLLFLAG:=
NDEBUG_LDFLAG:=-O2 -Wl,+s

APP_STL := gnustl_static
APP_CPPFLAGS := -frtti -fexceptions

endif	#end for AIX

# for debug version
ifeq ("$(RELEASE_VERSION)","1")
CXXFLAGS:= $(DEBUG_CXXFLAG) $(BASEFLAG)
DLLFLAGS:= $(DEBUG_DLLFLAG)
LDFLAGS:= $(DEBUG_LDFLAG)

BUILD_PATH:=obj

# for release version
else
CXXFLAGS:= $(NDEBUG_CXXFLAG) $(BASEFLAG)
DLLFLAGS:= $(NDEBUG_DLLFLAG)
LDFLAGS:= $(NDEBUG_LDFLAG)

BUILD_PATH:=obj

endif  #end for RELEASE_VERSION

CFLAGS:=$(CXXFLAGS)
DFLAGS:=$(DLLFLAGS)
LFLAGS:=$(LDFLAGS)

#------------------------------------------------------------------#
#OCS_HOME path
OCS_HOME:=$(LBSPUBROOT)

#RELEASE PATH bin and lib
RELEASE_PATH:=
PROJECT_RPATH:=$(LBSPUBROOT)/release

#DFM include and library
DFM=$(LBSPUBROOT)/dfm
DFM_INC_PATH=$(DFM)/include
DFM_LIB_PATH=$(DFM)/lib

#THIRD path
THIRDDIR=$(LBSPUBROOT)/third

#TOOL path
TOOL=$(LBSPUBROOT)/tools/bin

#ITF path
INTERFACE=/public/ocs_ah/src_cloud/interface

#DCLOGCLI path
LOG=$(LBSPUBROOT)/Log/logclientnew

#AVRO path
AVRO=$(THIRDDIR)/avrocpp
AVRO=$(THIRDDIR)/avrocpp

#DCA path
DCA=$(THIRDDIR)/dca
DCA_INC=$(DCA)/include
DCA_LIB=$(DCA)/lib

#ZMQ path
ZMQ=$(THIRDDIR)/zmq/libzmq

#SODIUM path
SODIUM=$(THIRDDIR)/zmq/libsodium

#PGM path
PGM=$(THIRDDIR)/zmq/libpgm

#MQ path
#MQ=$(THIRDDIR)/mq
MQ=$(CMN_ROOT)/DCMQ/MQ

#STORM path
JSTORM=$(THIRDDIR)/storm_ack
JSTORM_INC=$(JSTORM)/include
JSTORM_LIB=$(JSTORM)/lib

#json path
JSON=$(THIRDDIR)/json
JSON_INC=$(JSON)/include
JSON_LIB=$(JSON)/lib

#zookeeper path
ZK=$(THIRDDIR)/ZooKeeperSDK
ZK_INC=$(THIRDDIR)/ZooKeeperSDK/include/zookeeper
ZK_LIB=$(THIRDDIR)/ZooKeeperSDK/lib

#TINYXML path
TINYXML=$(THIRDDIR)/tinyxml


#ORACLE include and library
ORACLE_ROOTS=$(ORACLE_HOME)
ORACLE_RDB_INC_PATH=$(ORACLE_ROOTS)/rdbms/public
ORACLE_PRECOMP_INC_PATH=$(ORACLE_ROOTS)/precomp/public
ORACLE_DEMO_INC_PATH=$(ORACLE_ROOTS)/rdbms/demo
ORACLE_PLSQL_INC_PATH=$(ORACLE_ROOTS)/plsql/public

ORACLE_LIB_PATH=$(ORACLE_ROOTS)/lib
ORACLE_LIBS=-lclntsh

#PLSQL
#USERID=ods30/ods30@tydic128
#ECHO=$(ORACLE_ROOTS)/bin/echodo
PCC=$(ORACLE_ROOTS)/bin/proc
PCCFLAGS= code=cpp cpp_suffix=cpp release_cursor=yes ireclen=256 parse=partial lname=err sqlcheck=syntax
PCC_LIBS=

#TT include and library
TT_ROOTS=$(TT_HOME)
TT_INC_PATH =$(TT_ROOTS)/include
TT_LIB_PATH =$(TT_ROOTS)/lib 
TT_LIBS=-ltten

#tuxedo
TUXEDO_ROOTS=$(TUXDIR)
TUXEDO_INC_PATH=#$(TUXEDO_ROOTS)/include
TUXEDO_LIB_PATH=#$(TUXEDO_ROOTS)/lib
TUXEDO_LIBS= 

#ACE include and library
ACE_ROOT=/public/ocs_ah/src/XDR_PRO/third/ACE/ACE_wrappers
ACE_LIBS=-lACE
ACEINCLUDE=-I$(ACE_ROOT)					
ACELIBDIR=-L$(ACE_ROOT)/lib

#dcc include and library
DCC_ROOTS=$(THIRDDIR)/dcc
DCC_INC_PATH=$(DCC_ROOTS)/include
DCC_LIB_PATH=$(DCC_ROOTS)/lib
DCC_LIBS=-ldcc

#m2db
M2DB_ROOTS=/public/sm/APC/m2db/
M2DB_INC_PATH=$(M2DB_ROOTS)/include
M2DB_LIB_PATH=$(M2DB_ROOTS)/lib

#json_c path
JSONC=$(THIRDDIR)/json_c
JSONC_INC=$(JSONC)/include
JSONC_LIB=$(JSONC)/lib

#jdk 1.7.0 +
JDK_HOME=/public/ocs_ah/app/jdk1.7.0_45
JDK_INC=$(JDK_HOME)/include
JDK_LIB=$(JDK_HOME)/lib

#-------------------------------------------------------------#
# Common includes and librarys


# for tracing included header file
DEP_INCS:=$(LBSPUBROOT)
#-------------------------------------------------------------#
#-------------------------------------------------------------#
# user defined functions
# for example, $(call CreateDir, path/build)
CreateDir=$(shell $(TESTTOOL) -d $(1) || mkdir -p $(1))

# for example, $(call GetCC, prefix, files)
GetCC=$(foreach var, $(filter %.cpp, $(2)), $(1)/$(var))

# for example, $(call GetObj, prefix, files)
GetObj=$(foreach var, $(basename $(2)), $(1)/$(var).o)

# for example, $(call GetPCC, prefix, files)
GetPCC=$(foreach var, $(basename $(filter %.pc, $(2))),$(1)/$(var).cpp)

# for example, $(call GetPC, prefix, files)
GetPC=$(foreach var, $(filter %.pc, $(2)), $(1)/$(var))

# for example, $(call StPrefix, prefix, files)
StPrefix=$(addprefix $(1),$(notdir $(2)))

/****************************************************************************************
*Copyrights  2006，深圳天源迪科计算机有限公司
*						OCS项目组
*All rights reserved.
*
* Filename：	ConfigureFile.cpp		
* Indentifier：		
* Description： 		
* Version：		V1.0
* Author:		guoxd
* Finished：	2006年12月27日
* History:		
******************************************************************************************/

#include "SysParam.h"
#include "trim.h"

#include <stdlib.h>
#include <string.h>
#include <assert.h>

#if defined(STDIOSTREAM_DISABLE)
#include <iostream.h>
#else
#include <iostream>
#endif


SysParam::SysParam()
{
  m_currentSectIter  = m_subsectionMapCurrent.begin();
  m_currentParamIter = m_paramMapCurrent.begin();
}

//##ModelId=3BCBD1020001
bool SysParam::initialization(const char *filename)
{
  return true;
}

//##ModelId=3B5D36E60073
bool SysParam::end()
{
  return true;
}

//##ModelId=3B8B4FFF002B
bool SysParam::getValue(const string& sectionPath, const string& name,
                        string& value)
{
  PARAMMIter it;
  pair<PARAMMIter, PARAMMIter> mrange;
  mrange = m_paramMap.equal_range(sectionPath);

  for (it = mrange.first; it != mrange.second; ++it)
  {
    if ((*it).second.name == name)
    {
      value = (*it).second.value;
      return true;
    }
  }
  return false;
}

//##ModelId=3B5D35650386
bool SysParam::getValue(const char *sectionPath, const char *name,
                        char *value, int& length)
{
  string path, key, keyvalue;
  path = sectionPath;
  key = name;

  if (!getValue(path, key, keyvalue))
    return false;
  if (keyvalue.length() >= length)
    return false;

  length = keyvalue.length();
  keyvalue.copy(value, length);
  return true;
}

//##ModelId=3B8B980A017F
bool SysParam::setSectionPath(const string& sectionPath)
{
  m_currentSection = sectionPath;
  m_paramMapCurrent.clear();
  m_subsectionMapCurrent.clear();
  m_currentSectIter = m_subsectionMapCurrent.begin();
  m_currentParamIter = m_paramMapCurrent.begin();

  pair<PARAMMIter, PARAMMIter> mrange;
  mrange = m_paramMap.equal_range(sectionPath);

#ifdef MEMBER_TEMPLATE_FUNCTION_DISABLE
  PARAMMIter it;
  for (it = mrange.first; it != mrange.second; ++it)
    m_paramMapCurrent.insert(*it);
#else
  m_paramMapCurrent.insert(mrange.first, mrange.second);
#endif

  pair<SECTIONIter, SECTIONIter> smrange;
  smrange = m_subsectionMap.equal_range(sectionPath);
#ifdef MEMBER_TEMPLATE_FUNCTION_DISABLE
  SECTIONIter sit;
  for (sit = smrange.first; sit != smrange.second; ++sit)
    m_subsectionMapCurrent.insert(*sit);
#else
  m_subsectionMapCurrent.insert(smrange.first, smrange.second);
#endif

  if ((m_subsectionMapCurrent.size() == 0)
      && (m_subsectionMapCurrent.size() == 0))
    return false;

  m_currentSectIter = m_subsectionMapCurrent.begin();
  m_currentParamIter = m_paramMapCurrent.begin();

  return true;
}

//##ModelId=3B8B994C02E0
int SysParam::getSectionValue(string& name, string& value)
{
  if (m_currentParamIter != m_paramMapCurrent.end())
  {
    name = (*m_currentParamIter).second.name;
    value = (*m_currentParamIter).second.value;
    m_currentParamIter++;
    return true;
  }
  return false;
}

//##ModelId=3BB3F2CB00D8
int SysParam::getSubSection(string& subsection)
{
  if (m_currentSectIter != m_subsectionMapCurrent.end())
  {
    subsection = (*m_currentSectIter).second;
    m_currentSectIter++;
    return true;
  }
  return false;
}

//##ModelId=3B8BA009033B
bool SysParam::openSection(const string& sectionPath)
{
  SECTIONCOUNTItr it;
  if ((it = m_sectionCount.find(sectionPath)) != m_sectionCount.end())
  {
    (*it).second++;
    return true;
  }
  else
    return false;
}

//##ModelId=3B8BA01500B7
bool SysParam::closeSection(const string& sectionPath)
{
  SECTIONCOUNTItr it;
  if ((it = m_sectionCount.find(sectionPath)) != m_sectionCount.end())
  {
    if (--((*it).second) > 0)
      return true;
  }
  m_sectionCount.erase(sectionPath);
  m_paramMap.erase(sectionPath);
  m_subsectionMap.erase(sectionPath);
  return true;
}

//##ModelId=3B8BA01E010A
bool SysParam::closeAllSection()
{
  m_sectionCount.clear();
  m_subsectionMap.clear();
  m_paramMap.clear();
  return true;
}


//##ModelId=3BC2E2D8020B
void SysParam::dump()
{
  PARAMMIter ptr;
  SECTIONIter str;
  SECTIONCOUNTItr sctr;

  dumpstream << "PARAM MAP DUMP:" << endl;
  for (ptr = m_paramMap.begin(); ptr != m_paramMap.end(); ptr++)
  {
    dumpstream << "<" << (*ptr).first << ">"
               << (*ptr).second.name << "::" << (*ptr).second.value << endl;
  }

  dumpstream << "SECTION MAP DUMP:" << endl;
  for (str = m_subsectionMap.begin(); str != m_subsectionMap.end(); str++)
  {
    dumpstream << "<" << (*str).first << ">" << (*str).second << endl;
  }
  dumpstream << "SECTION MAP COUNT DUMP:" << endl;
  for (sctr = m_sectionCount.begin(); sctr != m_sectionCount.end(); sctr++)
  {
    dumpstream << "<" << (*sctr).first << "> count:" << (*sctr).second << endl;
  }

  dumpstream << "CURRENT SECTION " << m_currentSection
             << " PARAM and SECTION DUMP:" << endl;
  for (ptr = m_paramMapCurrent.begin(); ptr != m_paramMapCurrent.end(); ptr++)
  {
    dumpstream << "<" << (*ptr).first << ">"
               << (*ptr).second.name << "::" << (*ptr).second.value << endl;
  }
  for (str = m_subsectionMapCurrent.begin();
       str != m_subsectionMapCurrent.end(); str++)
  {
    dumpstream << "<" << (*str).first << ">" << (*str).second << endl;
  }
}


//##ModelId=3BB2F2D6012C
bool SysParam::addValue(const string& sectionPath, const string& name,
                        const string& value)
{
  SECTIONCOUNTItr it;
  if ((it = m_sectionCount.find(sectionPath)) != m_sectionCount.end())
    return true;

  PARAM param;
  param.name = name;
  param.value = value;
  m_paramMap.insert(PARAMMAP::value_type(sectionPath, param));
  //m_paramMap[sectionPath] = param;
  return true;
}

//##ModelId=3BB2F2D703E0
bool SysParam::addSubSection(const string& sectionPath,
                             const string& subsection)
{
  SECTIONCOUNTItr it;
  if ((it = m_sectionCount.find(sectionPath)) != m_sectionCount.end())
  {
    (*it).second++;
    return true;
  }
  m_sectionCount.insert(SECTIONCOUNT::value_type(sectionPath, 1));
  m_subsectionMap.insert(SECTIONMAP::value_type(sectionPath, subsection));
  //m_sectionCount[sectionPath] = 1;
  //m_subsectionMap[sectionPath] = subsection;
  return true;
}

//##ModelId=3BB3E6D60361
bool SysParam::extractSectionPath(const string& pathFull, string& pathParent,
                                  string& sectionSub)
{
  string::size_type pos;
  if ((pos = pathFull.rfind(SECTDELIM)) == string::npos)
  {
    pathParent.assign("");
    sectionSub.assign("");
    return true;
  }
  pathParent = pathFull.substr(0, pos);
  if (++pos < pathFull.size())
    sectionSub = pathFull.substr(pos);
  else
    sectionSub.assign("");
  return true;
}
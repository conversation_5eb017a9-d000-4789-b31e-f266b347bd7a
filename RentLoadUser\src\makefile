include ../../../comm.mk

SRC_HOME=$(LBSPUBROOT)/RentLoad
SRC_PATH=$(SRC_HOME)/RentLoadUser/src
LIB_PATH=$(SRC_HOME)/release/lib/RentLoad
LIB_PATH_COMMON=$(SRC_HOME)/release/lib/Common/
DGR=$(LBSPUBROOT)/third/DCGrayscaleRoute

DCF=$(LBSPUBROOT)/dcf
DCF_INC_PATH=$(DCF)/include
#DCF_LIB_PATH=$(DCF)/lib
DCF_LIB_PATH=$(DCF)/bin

JSON=$(THIRD_HOME)/jsoncpp-0.10.2
JSON_INC=$(JSON)/include
JSON_LIB=$(JSON)/lib

inc=-I$(DFM_INC_PATH) \
	-I$(SRC_HOME)/RentLoadUser/inc \
	-I$(ITF)/include \
	-I$(SRC_HOME)/pub_common/inc \
	-I$(AVRO)/include \
	-I$(ACE_INC_PATH) \
	-I$(DCLOGCLI)/include \
	-I$(SRC_HOME)/common/inc \
	-I$(TXML)/include \
	-I$(DCFCNEW_INC) \
	-I$(KPI_SENDER_INC) \
	-I$(JSON_INC) \
	-I$(DGR)/include \
	-I$(DCF_INC_PATH)

version=1.0.0

COMMON_LINK=-L$(LIB_PATH_COMMON) -lrent_pub_common  -L$(DGR)/lib -ldcgrayscaleroute  

SRC_FILE=$(SRC_PATH)/DCLoadUserBase.o $(SRC_PATH)/DCUidCacheManager.o $(SRC_PATH)/DCLoadImmedUser.o $(SRC_PATH)/DCAtCompare.o        $(SRC_PATH)/DCAtCondition.o       $(SRC_PATH)/DCRDEventType.o \
		 $(SRC_PATH)/DCLoadOldUser.o  $(SRC_PATH)/DCLoadOldCrossUser.o $(SRC_PATH)/DCComboAdaptPrdInst.o \
		 $(SRC_PATH)/DCPairList.o     $(SRC_PATH)/TThread.o     	 $(SRC_PATH)/DCPairManager.o $(SRC_PATH)/DCCallZKServerMaker.o \
		 $(SRC_PATH)/DCConf.o         $(SRC_PATH)/DCDataCenter.o   $(SRC_PATH)/DCRentUser.o
bintarget=$(SRC_HOME)/release/bin/RentLoad

.PHONY:all clean dup

all: $(bintarget)

	
$(bintarget): $(SRC_FILE)
	$(CC) $(LFLAGS) -o $@ $^ -lz -lrt $(COMMON_LINK) -L$(DFM)/lib -ldfm -L$(ACE_LIB_PATH) -lACE -L$(DCLOGCLI)/lib -ldclogcli -L$(TXML)/lib -ltinyxml -L$(DCFCNEW_LIB) -ldcfclient_new -L$(JSON_LIB) -ljsoncpp -L$(KPI_SENDER_LIB) -lkpisender -L$(DCF_LIB_PATH) -ldcfserver
%.o:%.cpp
	$(CC) $(CFLAGS) -c $< $(inc)
	
clean:
	-rm -f $(bintarget) *.o
	
dup:
	cp -pf $(bintarget) $(PROJECT_RPATH)/other/bin && echo "dup $(bintarget) to $(PROJECT_RPATH)/other/bin"


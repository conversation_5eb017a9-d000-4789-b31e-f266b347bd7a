include ../../../comm.mk

RENT_BASEFLAG:= $(BASEFLAG) -DACE_LACKS_NUMERIC_LIMITS

CFLAGS= $(DEBUG_CXXFLAG) $(RENT_BASEFLAG)

SRC_HOME=$(LBSPUBROOT)/RentLoad
SRC_PATH=$(SRC_HOME)/pub_common
LIB_PATH=$(SRC_HOME)/release/lib/Common
inc=-I$(DFM)/include -I$(DCLOGCLI)/include -I$(SRC_HOME)/pub_common/inc -I$(ACE_INC_PATH)

version=1.0.0


libtarget=$(LIB_PATH)/librent_pub_common.a 

.PHONY:all clean dup

all: $(libtarget)
$(LIB_PATH)/librent_pub_common.a:$(SRC_PATH)/src/DCEventTime.o $(SRC_PATH)/src/DCDateTime.o $(SRC_PATH)/src/UtilLocal.o $(SRC_PATH)/src/publiclib.o $(SRC_PATH)/src/trim.o  \
                                 $(SRC_PATH)/src/paramcfg.o $(SRC_PATH)/src/BossDes.o $(SRC_PATH)/src/CharPtr.o  $(SRC_PATH)/src/SysParam.o  $(SRC_PATH)/src/ConfigureFile.o                                
	ar -r  $@ $^  
%.o:%.cpp
	$(CC) $(CFLAGS) -c $< $(inc)
	
clean:
	-rm -f $(libtarget) *.o
	
dup:

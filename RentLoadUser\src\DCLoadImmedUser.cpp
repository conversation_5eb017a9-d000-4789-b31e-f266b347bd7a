#include <utility>
#include <unistd.h>
#include <sys/types.h>
#include <time.h>
#include "DCParseXml.h"
#include "DCLoadImmedUser.h"
#include "DCPerfStatistic.h"

#define DIFFTIME_US(Tb, Ta) ((((Tb)>>24) - ((Ta)>>24))*1000000 + ((Tb)&0xFFFFFF) - ((Ta)&0xFFFFFF))

using namespace std;


DCLoadImmedUser::~DCLoadImmedUser()
{
	SAFE_DELETE_PTR(m_pCallServer);
}


//初始化
int DCLoadImmedUser::InitSub()
{
	int nRet = 0;
	
	string latnId = m_cfgPara.latnList;	

	m_ltLatn.clear();
    PublicLib::SplitStr(latnId.c_str(),'|',m_ltLatn);

    if (m_ltLatn.empty())
    {		
		DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "DCLoadImmedUser::InitSub","Configuration item of %s is null",m_inputParam.strLatn.c_str());
        return -1;
    }
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadImmedUser::InitSub", "%s is[%s] get size:%d",m_inputParam.strLatn.c_str(),latnId.c_str(),m_ltLatn.size());

	m_nLatnId = 0;
	m_nBillingCycleId = 0;	

	//初始化消息发送模块
	m_pCallServer = NULL;
	m_pCallServer = new DCCallZKServerMaker();
	nRet = m_pCallServer->init(m_cfgPara);
	if (nRet < 0)
	{
        DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"","DCLoadOldUser::InitSub, init DCCallZKServerMaker failed, error code[%d]", nRet);
		return -1;
	}
	
	DCBIZLOG(DCLOG_LEVEL_INFO, 0,"","DCLoadImmedUser::InitSub success");
	DCDATLOG("RA00003:");
	return 0;
}

int DCLoadImmedUser::processDcfs()
{
	DCBIZLOG(DCLOG_LEVEL_TRACE, 0,"", "DCLoadImmedUser::processDcfs begin");

	;  //do nothing
	
	DCBIZLOG(DCLOG_LEVEL_TRACE, 0,"", "DCLoadImmedUser::processDcfs end");	
    return 0;
}


//处理函数
int DCLoadImmedUser::process()
{
	DCBIZLOG(DCLOG_LEVEL_TRACE, 0,"", "DCLoadImmedUser::process begin.");

	int nRet = 0;
	int nLatnId = 0;

	for (std::list<string>::iterator iterLatnId = m_ltLatn.begin(); iterLatnId != m_ltLatn.end(); ++iterLatnId)
    { 
		nLatnId =  atoi((*iterLatnId).c_str());
	
		//如果日期或本地网变更,则重新刷新账期信息
		char szTime[16]={0};
		PublicLib::GetTime(szTime, YYYYMMDD);
		int nBillingCycleID = atoi(szTime);
		if (m_nBillingCycleId != nBillingCycleID || m_nLatnId!=nLatnId)
		{
			m_nBillingCycleId = nBillingCycleID;
			m_nLatnId = nLatnId;
			DCDataCenter::instance()->RefreshBillingCycle((int)(nBillingCycleID/100),m_nLatnId);
		}	
		p_dbm->CheckReset(); // 加载用户资料之前，重连一下
		nRet = m_pComboAdpt->LoadSpecialAcct(nLatnId);
		if (nRet < 0)
		{
			if(ReConnect()) //重连失败
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"DCLoadImmedUser::process","LoadSpecialAcct failed, LatnId[%d].",nLatnId);				
				return -1;
			}						

			nRet = m_pComboAdpt->LoadSpecialAcct(nLatnId);
			if (nRet < 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"","LoadSpecialAcct failed, LatnId[%d].",nLatnId);				
				return -1;
			}
		}	
		nRet = DealBusi(nLatnId);
		if(nRet<0)
		{							
	        DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"", "LoadImmedUser failed,nRet[%d]",nRet);					
		    return -1;
		}
		DCDATLOG(); //主动触发日志输出
		
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"DCLoadImmedUser::process", "Cycle scan table [ItemOutImmed_user] end one times,sleep 3s.");

	ACE_Time_Value aSleepS(3);
	ACE_OS::sleep(aSleepS); 
	return 0;

}


int DCLoadImmedUser::DealBusi(int nLatnId)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadImmedUser::DealBusi begin,nLatnId[%d]",nLatnId);	

	char *sCycleBeginTime = DCDataCenter::instance()->sCycleBeginTime;
	char *sCycleEndTime = DCDataCenter::instance()->sCycleEndTime;
	
	long lnPidId = pthread_self();//getpid()
	struct timeval tm;
	gettimeofday(&tm, NULL);
	unsigned long lnbegin = (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);
	unsigned long lnend = 0, lnUseTime = 0;
	int nTps=0;
	long lnCounts=0;
	map<long, STSpecialAcctInfo>::iterator itrmap;
	int nRet = 0;
	int nPrdInstNum=0,nSubInstNum=0,nOfrInstNum=0;
	set<long> setAcctId;
	set<long> setPrdGroup;
	set<ST_AcctKey> setBatchAcct;//用于过滤同一批处理过的账户
	set<ST_AcctKey>::iterator iterBacthAcct;
	long lnSpecialId = 0;
	int nInstSize = 0;	
	int nBillingCycleID = DCDataCenter::instance()->iBillingCycleId;
	int nAcctType = 0;
	long lnOfrInstId = 0;	
	bool bHaveError = false;
	multimap<long,ocs::DetailInst>::iterator iter;
	int nState = IMMED_STATE_INIT;

	vector<STImmedUser> vecImmedUser;
    nRet = LoadImmedUser(vecImmedUser);
	if (nRet < 0)
	{
		 DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"", "LoadImmedUserfailed,nRet[%d]",nRet);						
		 return -1;
	}

	for(int idx=0;idx<vecImmedUser.size();idx++)
	{
		STImmedUser stImmedUser = vecImmedUser[idx];		
		DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Do:stImmedUser[%s]",stImmedUser.dump_to_string().c_str());
		nState = IMMED_STATE_DOING;
		nRet = UpdateInstDealed(stImmedUser,nState);
		if (nRet < 0)
		{
	        DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"", "UpdateInstDealed itemoutimmed_id[%ld] failed",stImmedUser.itemoutimmed_id);						
		    return -1;
		}
		m_pComboAdpt->releaseUserMap(true);
		lnCounts++;
		setAcctId.clear();
		
		bool bSpecialAcctFlag = false;
		#if 0
		//立即出账不做跨账户的判断
		
		itrmap = m_pComboAdpt->m_mapSpecialAcctType.find(stImmedUser.acct_id);
		if(itrmap != m_pComboAdpt->m_mapSpecialAcctType.end())
		{
			bSpecialAcctFlag = true;
		    if (GROUP_CROSS == itrmap->second.nGroupId)
		    {
				lnSpecialId = itrmap->second.lnModGroupId;
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","this lnAcctId[%ld] is CrossAcct,lnModGroupId[%ld]", stImmedUser.acct_id,lnSpecialId);
				std::map<long,set<long> >::iterator iterMod = m_pComboAdpt->m_mapModGroupAcct.find(lnSpecialId);
				if(iterMod != m_pComboAdpt->m_mapModGroupAcct.end())
				{
					setAcctId = iterMod->second;			
					DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","lnModGroupId[%ld] has setAcctId.size[%d]", lnSpecialId,setAcctId.size());
				}	
			}			
		}
		else
		{
			setAcctId.insert(stImmedUser.acct_id);
			lnSpecialId = stImmedUser.acct_id;
		}
		ST_AcctKey stAcctKey;
		if (bSpecialAcctFlag)
		{
		    nAcctType = AdjustAcctType(itrmap->second.nGroupId);
		}
		else
		{
			setAcctId.insert(stImmedUser.acct_id);
			nAcctType = SPECIAL_ACCT_COMM;
		}		
		
		#endif
		
		setAcctId.insert(stImmedUser.acct_id);
		nAcctType = SPECIAL_ACCT_COMM;
		lnSpecialId = stImmedUser.acct_id;
		DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","setAcctId.size is [%d]", setAcctId.size());
		ST_AcctKey stAcctKey;
		stAcctKey.nAcctType = nAcctType;
		stAcctKey.lnAcctId = lnSpecialId;			
		iterBacthAcct = setBatchAcct.find(stAcctKey);
		if(iterBacthAcct!=setBatchAcct.end()) //过滤同一批里处理过的,并更新记录状态为结束				
		{
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","lnAcctId[%ld] nAcctType[%d] already been dealed",stAcctKey.lnAcctId,stAcctKey.nAcctType);	
			nState = IMMED_STATE_SUCCESS;
			nRet = UpdateInstDealed(stImmedUser,nState);
			if (nRet < 0)
			{		        
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"", "UpdateInstDealed failed, itemoutimmed_id[%ld]",stImmedUser.itemoutimmed_id); 				
				return -1;
			}
			continue;
		}
		setBatchAcct.insert(stAcctKey);
					
		m_pComboAdpt->releaseUserMap(true);
		nState = IMMED_STATE_SUCCESS;
		if(!setAcctId.empty())
		{
			if(false==m_pComboAdpt->LoadUserByAcctList(setAcctId,nLatnId,lnSpecialId,nAcctType,setPrdGroup))
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, -1,"","LoadUserByAcctList failed,AcctId/CrossModGroupId[%ld] AcctType[%d].",lnSpecialId,nAcctType);				
				return -1;
			}
			nInstSize = m_pComboAdpt->m_vecOfrInstOutput.size();	
			if (0==nInstSize) 
			{				
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","have no offer inst");
				nState = IMMED_STATE_SUCCESS;
				nRet = UpdateInstDealed(stImmedUser,nState);
				if (nRet < 0)
				{		        
					DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"", "UpdateInstDealed failed, itemoutimmed_id[%ld]",stImmedUser.itemoutimmed_id); 				
					return -1;
				}
				continue;
			}
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","nInstSize is[%d],detail below:",nInstSize);
			for ( std::vector<ocs::OfrInst>::iterator tIter = m_pComboAdpt->m_vecOfrInstOutput.begin(); tIter != m_pComboAdpt->m_vecOfrInstOutput.end(); ++tIter)
			{		
				DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","lnOfrId[%ld],lnOfrInstId[%ld],nCalcPriority[%d],szEffDate[%s],szExpDate[%s],szEventEffDate[%s],szEventExpDate[%s],nCalcPriority[%d],nDynInstFlag[%d],lnSelectGroupID[%ld]",tIter->lnOfrId,tIter->lnOfrInstId,tIter->nCalcPriority,tIter->szEffDate.c_str(),tIter->szExpDate.c_str(),tIter->szEventEffDate.c_str(),tIter->szEventExpDate.c_str(),tIter->nCalcPriority,tIter->nDynInstFlag,tIter->lnSelectGroupID);
			}	
			//组装消息发送
			T_UserInfo tUserInfo;					
			tUserInfo.nObversionMode = 0;
			tUserInfo.nUserMode = USER_IMMED;
	        tUserInfo.nTriggerCycle = nBillingCycleID;
			
			m_pComboAdpt->SetHeadInfo(stImmedUser.latn_id,nBillingCycleID,lnSpecialId,nAcctType,tUserInfo);
			//int nGroupNum = nInstSize%m_nInstSendBatNum==0?(int)(nInstSize/m_nInstSendBatNum):(int)(nInstSize/m_nInstSendBatNum)+1;
			//m_pComboAdpt->m_stAcctHead.GroupNum = nGroupNum;//新增接口传递一组消息个数
			int nGroupNum = 1,nPriority=0,nLastPriority=0,nFeeType=0;
			for(int i=0;i<nInstSize;)
			{
				ocs::IAcctBodyReq stAcctBodyReq;
				int j=0;
				nPrdInstNum=0;nSubInstNum=0;nOfrInstNum=0;
				
				m_pComboAdpt->m_stAcctHead.crossserv.clear();//避免重复发送
				m_pComboAdpt->m_stAcctHead.serv_id.clear();

				if(i==0)
				{
					//现每个用户都要做过户操作，所以从servid取，crossserv暂时保存
					for(int iprd=0;iprd<m_pComboAdpt->m_vecGroupPrdInstId.size();iprd++)
					{
						m_pComboAdpt->m_stAcctHead.crossserv.push_back(m_pComboAdpt->m_vecGroupPrdInstId[iprd]);
						m_pComboAdpt->m_stAcctHead.serv_id.push_back(m_pComboAdpt->m_vecGroupPrdInstId[iprd]);
					}
				}
				
				for(j=0;i<nInstSize;j++)
				{
					nPriority = m_pComboAdpt->m_vecOfrInstOutput[i].nCalcPriority;
					if(m_cfgPara.nInstSendBatNum > 0 && j>=m_cfgPara.nInstSendBatNum && (nLastPriority!=nPriority || nFeeType==FEE_TYPE_RENT || nFeeType==FEE_TYPE_PRODUCT))
						break;
					nLastPriority = nPriority;
					nFeeType = m_pComboAdpt->m_vecOfrInstOutput[i].nFeeType;
					
					lnOfrInstId = m_pComboAdpt->m_vecOfrInstOutput[i].lnOfrInstId;
					stAcctBodyReq.VOfrInst.push_back(m_pComboAdpt->m_vecOfrInstOutput[i]);
					iter=m_pComboAdpt->m_mmapOfrDetail.lower_bound(lnOfrInstId);
					for(;iter!=m_pComboAdpt->m_mmapOfrDetail.upper_bound(lnOfrInstId);iter++)
						stAcctBodyReq.VDetailInst.push_back(iter->second);
					switch(m_pComboAdpt->m_vecOfrInstOutput[i].nFeeType)
					{
					case 2:
						nPrdInstNum++;
						break;
					case 3:
						nSubInstNum++;
						break;
					default:
						nOfrInstNum++;
						break;
					}
					i++;
				}
				if(i==nInstSize)
				{
					m_pComboAdpt->m_stAcctHead.BillingFlag = 0;//标识一组消息中最后一条
					//最后一条消息也做过户
					if(m_pComboAdpt->m_stAcctHead.serv_id.size()==0)
					{
						for(int iprd=0;iprd<m_pComboAdpt->m_vecGroupPrdInstId.size();iprd++)
						{
							m_pComboAdpt->m_stAcctHead.serv_id.push_back(m_pComboAdpt->m_vecGroupPrdInstId[iprd]);
						}
					}
				}
				else
					m_pComboAdpt->m_stAcctHead.BillingFlag = 1;
				m_pComboAdpt->m_stAcctHead.GroupNum = nGroupNum++;//新增接口传递一组消息个数
				
				RentUserInfo stRentInfo;
				stRentInfo.lnSpecialId = lnSpecialId;
				stRentInfo.nAcctType = nAcctType;
				stRentInfo.nLatnId = nLatnId;
				stRentInfo.nBillingCycleId = m_nBillingCycleId;
				stRentInfo.nPrdInstNum = nPrdInstNum;
				stRentInfo.nSubInstNum = nSubInstNum;
				stRentInfo.nOfrInstNum = nOfrInstNum;
				stRentInfo.lnPidId = lnPidId;
				stRentInfo.stFmtHead = m_pComboAdpt->m_stFmtHead;
				stRentInfo.stAcctHead = m_pComboAdpt->m_stAcctHead;
				stRentInfo.stAcctBodyReq = stAcctBodyReq;
				stRentInfo.nGroupNum = nGroupNum;
				stRentInfo.nMsgType = MSGTYPE_IMMED;
				stRentInfo.vecTransferId.push_back(stImmedUser.itemoutimmed_id);

				nRet = m_pCallServer->process(&stRentInfo,MSGCTRL_MODE_NORMAL, p_dbm);
				if(nRet < 0)
		        {
		            DCBIZLOG(DCLOG_LEVEL_ERROR, 0, "","call DCCallZKServerMaker::process fail");
					bHaveError = true;
					break;
		        } 
				//仅全量处理流程写日志
				//WriteRentLog(nLatnId,lnPidId,nPrdInstNum,nSubInstNum,nOfrInstNum);
			}
		}		
		else
		{
			bHaveError = true;
		}
		if (bHaveError)
		{
			//更新接口表状态
			nState = IMMED_STATE_FAILED;
			nRet = UpdateInstDealed(stImmedUser,nState);
			if (nRet < 0)
			{
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"", "UpdateInstDealed failed, itemoutimmed_id[%ld]",stImmedUser.itemoutimmed_id); 				
				return -1;		        	
			}
		}
		else
		{
			#if 0
			//等账务回调后再更新itemoutimmed_state
			nState = IMMED_STATE_SUCCESS;
			nRet = UpdateInstDealed(stImmedUser,nState);
			if (nRet < 0)
			{		        
				DCBIZLOG(DCLOG_LEVEL_ERROR, 0,"", "UpdateInstDealed failed, itemoutimmed_id[%ld]",stImmedUser.itemoutimmed_id); 				
				return -1;
			}
			#endif
		}
		

    }
	gettimeofday(&tm, NULL);//性能日志
	lnend = (tm.tv_sec<<24)|(tm.tv_usec&0xFFFFFF);//性能日志
	lnUseTime = DIFFTIME_US(lnend, lnbegin);
	nTps=0;
	if(lnUseTime>0 && lnCounts>0)
		nTps =(int)((lnCounts*1000000.0)/lnUseTime);
	std::string strStat;
	p_dbm->get_statistic()->to_string(strStat, true);	 //过滤未使用的
	DCBIZLOG(DCLOG_LEVEL_INFO, 0,"PERF RentLoad LoadImmedUser","LoadUseTime[%ldus] LoadImmedUser[%ld][tps=%d] LatnId[%d]\n%s",lnUseTime,lnCounts,nTps,nLatnId,strStat.c_str());
	DCDATLOG("RA00005:%d!%ld!%ld!%d!%s",nLatnId,lnUseTime,lnCounts,nTps,strStat.c_str());
	
	//重置统计信息
	p_dbm->get_statistic()->reset();

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadImmedUser::LoadImmedUser end,nLatnId[%d]",nLatnId);
	return 0;
}
int DCLoadImmedUser::AdjustAcctType(int nGroupId)
{	
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadImmedUser::AdjustAcctType begin,nGroupId[%d]",nGroupId);
	int nAcctType = SPECIAL_ACCT_COMM;
	if (GROUP_BIG == nGroupId)
	{
		nAcctType = SPECIAL_ACCT_BIG;
	}
	else if (GROUP_CROSS == nGroupId)
	{
		nAcctType = SPECIAL_ACCT_INST;
	}
	else if (GROUP_MOST == nGroupId)
	{
		nAcctType = SPECIAL_ACCT_MOST;
	}	
    DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadImmedUser::AdjustAcctType end,nGroupId[%d],nAcctType[%d]",nGroupId,nAcctType);
	return nAcctType;
}


int DCLoadImmedUser::UpdateInstDealed(STImmedUser &stImmedUser, int nState)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","UpdateInstDealed begin,stImmedUser[%s],nState[%d]",stImmedUser.dump_to_string().c_str(),nState);
	int nCount = 0,nRet = 0;
	string strSqlText = "";
	char sqlname[64] = {0};
	sprintf(sqlname,"UpdateImmedState|%d",stImmedUser.latn_id);
	UDBSQL *pml = p_dbm->GetSQL(sqlname);
	if (pml == NULL)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "Not find Sql [%s]", sqlname);
		return FAIL_NOT_FIND_SQLNAME;
	}

	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "Get Sql [%s] OK", sqlname);
	while(nCount < NUMBER_OF_CYCLES)
	{
		try
		{			
	        pml->UnBindParam();
			pml->BindParam(1,nState);
		    pml->BindParam(2,stImmedUser.itemoutimmed_id);
		    pml->GetSqlString(strSqlText);            
	        DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "[%s][%s]", sqlname,strSqlText.c_str());
	        pml->Execute();
	        int nAffectRows = pml->GetRowCount();
	        if(nAffectRows > 0)
	        {
	            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "update success! affected row count[%d]", nAffectRows);
	        }
	        else
	        {
	            DCBIZLOG(DCLOG_LEVEL_DEBUG, 0, "", "do not update any record, affected row count[%d]", nAffectRows);
	        }
	        pml->Connection()->Commit();		
	        pml->Close();
		}
		catch (UDBException &e)
		{
			DCBIZLOG(DCLOG_LEVEL_ERROR, -1, "", "[%s][%s]", sqlname,e.ToString());
			try
			{
				pml->Connection()->Rollback();
			}
			catch(...){}
			p_dbm->CheckReset();
			nCount++;
			nRet = FAIL_UPDATE_ITEM_OUT_IMMED_USER;
			continue;
		}
		nRet = 0;
		break;
	}
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","UpdateInstDealed end,stImmedUser[%s],nState[%d]",stImmedUser.dump_to_string().c_str(),nState);
	return nRet;
}

int DCLoadImmedUser::LoadImmedUser(vector<STImmedUser> &vecImmedUser)
{
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadImmedUser::LoadImmedUser begin");

    string strSql;
	int nCount = -1,nRet = 0;
	char szSqlname[64] = {0};	
	sprintf(szSqlname , "LoadImmedUser|%d",m_nLatnId);
	UDBSQL*pQuery = p_dbm->GetSQL(szSqlname);
	if(pQuery == NULL)
	{
		DCBIZLOG(DCLOG_LEVEL_ERROR,-1,"","not find sql[%s]",szSqlname);
		return FAIL_NOT_FIND_SQLNAME;
	}
	int nBillingCycle = m_nBillingCycleId/100;

	pQuery->UnBindParam();		
	pQuery->BindParam(1,m_inputParam.nProcNum);
	pQuery->BindParam(2,m_nProcId);
	pQuery->BindParam(3,to_string(nBillingCycle));
	pQuery->GetSqlString(strSql);
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","Do SQL: begin to do [%s],sql :[%s] ",szSqlname,strSql.c_str());
	
	while(nCount < NUMBER_OF_CYCLES)
	{
		nRet = 0;
		vecImmedUser.clear();
		try
		{
			pQuery->UnBindParam();		
			pQuery->BindParam(1,m_inputParam.nProcNum);
			pQuery->BindParam(2,m_nProcId);
			pQuery->BindParam(3,to_string(nBillingCycle));
			pQuery->GetSqlString(strSql);
			DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","Do SQL: begin to do [%s],sql :[%s] ",szSqlname,strSql.c_str());

		    pQuery->Execute();
		    while(pQuery->Next())
		    {
				STImmedUser stImmedUser;
				pQuery->GetValue(1, stImmedUser.itemoutimmed_id);
				pQuery->GetValue(2, stImmedUser.acct_id);
				pQuery->GetValue(3, stImmedUser.acc_num);			
				stImmedUser.latn_id = m_nLatnId;			
				DCBIZLOG(DCLOG_LEVEL_DEBUG,0,"","Get:stImmedUser[%s]",stImmedUser.dump_to_string().c_str());
				vecImmedUser.push_back(stImmedUser);					
			}		
			pQuery->Close();
		}
		catch (UDBException &e)
		{
			std::string sql;
			pQuery->GetSqlString(sql);
			DCBIZLOG(DCLOG_LEVEL_DEBUG,0, "", "sql[%s]",sql.c_str());
			DCBIZLOG(DCLOG_LEVEL_ERROR,-1, "", "[%s],execption[%s]", szSqlname,e.ToString());
			p_dbm->CheckReset();
			nCount++;
			nRet = -1;
			continue;
		}
		break;
	}
	
	
	DCBIZLOG(DCLOG_LEVEL_DEBUG, 0,"","DCLoadImmedUser::LoadImmedUser end,vecImmedUser.size[%d],nRet[%d]",vecImmedUser.size(),nRet);
	return nRet;
}



